#ifndef TESTCONTROLLER_H
#define TESTCONTROLLER_H

#include <QObject>
#include <QTimer>
#include <QList>
#include "modbustestcase.h"
#include "serialmanager.h"

class TestController : public QObject
{
    Q_OBJECT

public:
    explicit TestController(SerialManager *serialManager, QObject *parent = nullptr);
    ~TestController();

    void setTestCases(const QList<ModbusTestCase> &cases);
    void startTests(int delay);
    void stopTests();
    bool isRunning() const;

signals:
    void testCaseFinished(int index, const ModbusTestCase &testCase, bool passed);
    void allTestsFinished();
    void logMessage(const QString &message);

private slots:
    void runNextTest();
    void onTestTimeout();
    void handleSerialData(const QString &portId, const QString &message);

private:
    bool checkResult();

    SerialManager *m_serialManager;
    QList<ModbusTestCase> m_testCases;
    int m_currentTestIndex;
    bool m_isRunningTests;
    int m_delay;

    QTimer m_testTimer;
    QTimer m_testTimeoutTimer;

    QString m_receivedDataA;
    QString m_receivedDataB;
};

#endif // TESTCONTROLLER_H
