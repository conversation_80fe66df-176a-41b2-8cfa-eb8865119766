#ifndef MAPPINGWINDOW_H
#define MAPPINGWINDOW_H

#include <QMainWindow>
#include <QComboBox>
#include <QListWidget>
#include <QGraphicsScene>
#include <QGraphicsView>
#include <QGraphicsLineItem>
#include <QMap>
#include <QStack>
#include <QString>
#include <QList>
#include <QTimer>
#include <QTextEdit>
#include <QPushButton>
#include <QMenu>
#include <QDialogButtonBox>
#include "modbusserial.h"

namespace Ui {
class MappingWindow;
}

class MappingWindow : public QMainWindow {
    Q_OBJECT

public:
    MappingWindow(QWidget *parent = nullptr);
    ~MappingWindow();
    void setSerialPorts(ModbusSerialA* portA, ModbusSerialB* portB);
    
    // 文件格式枚举
    enum FileFormat {
        XML,
        JSON
    };

    // 导入导出配置槽函数
    void importConfig();
    void exportConfig();

private slots:
    void addMapping();
    void saveMapping();
    void loadMapping();
    void undo();
    void redo();
    void resetTestState();
    void generateTestSteps();
    void editTestStep();
    void showTestStepDetails();
    void onSerialPortChanged(int index); // 串口选择变化时更新设备列表
    void onGlobalCrc16CheckBoxToggled(bool checked); // 全局CRC16校验复选框状态变化
    void onHideInterfaceCheckBoxToggled(bool checked); // 隐藏界面复选框状态变化

    // 新增的测试相关槽函数
    void onTestButtonClicked();
    void onClearLogButtonClicked();
    void runNextTest();
    void onTestTimeout();
    void handleSerialAMessageReceived(const QString &message);
    void handleSerialBMessageReceived(const QString &message);
    void onTestReportButtonClicked();
    void onMappingListContextMenuRequested(const QPoint& pos);
    void onInsertMappingAt();
    void onDeleteMappingAt();
    void showDeviceContextMenu(const QPoint& pos);
    void editDeviceName();
    void updateDeviceNameInLists(const QString& oldName, const QString& newName);
    void saveDeviceListsToFile();
    void loadDeviceListsFromFile();
    void onTestStepsListDoubleClicked(QListWidgetItem* item);
    void onGraphicsViewDoubleClicked(const QPointF& position);
    void editTestStepByIndex(int index);
    void onFlowchartContextMenuRequested(const QPoint& pos); // 流程图右键菜单
    void onFlowchartAddMapping(); // 流程图添加映射
    void onFlowchartDeleteMapping(); // 流程图删除映射
    void onInsertMappingAtPosition(int position); // 在指定位置插入映射
    void showTestReport(); // 显示测试报告
    QString generateTestReport(); // 生成测试报告内容

protected:
    bool eventFilter(QObject* obj, QEvent* event) override;

private:
    void initUI();
    void initCommandMaps();
    void updateMappingList();
    void updateGraphicalMapping(int highlightStep = -1);
    void updateTestStepsList();
    
    // 新增的测试相关函数
    void startTest();
    void stopTest();
    void logMessage(const QString &message, const QString &type = "INFO");
    void updateFlowchartWithTestProgress(int currentStep, bool passed);
    QByteArray hexStringToByteArray(const QString &hexString);
    QString byteArrayToHexString(const QByteArray &data);
    bool connectToSerialPorts();
    void disconnectFromSerialPorts();

    // 映射关系结构体
    struct MappingRelation {
        QString serialPort;  // "COMA" 或 "COMB"
        QString device;     // 设备名称
        QString deviceType; // "controller" 或 "protection"
        
        MappingRelation() {}
        MappingRelation(QString sp, QString dev, QString type)
            : serialPort(sp), device(dev), deviceType(type) {}
    };
    QList<MappingRelation> mappingRelations;
    QMap<QString, QString> mapping; // 保持向后兼容
    QList<QGraphicsLineItem*> mappingLines;

    // 测试步骤结构体
    struct TestStep {
        QString type;       // 步骤类型: "controller", "protection", "info"
        QString device;     // 设备名称 (可以考虑用 description 替代或合并)
        QString command;    // 命令内容 (旧格式，可逐步弃用)
        QString expected;   // 预期结果 (旧格式)
        QString actual;     // 实际结果 (旧格式)
        bool passed;        // 是否通过
        QString notes;      // 备注信息 (对应统一格式的 description)
        QString configType; // "modbus" or "uart" (新增，用于区分配置类型)
        int delay = 0;      // ms (新增，用于步骤间的延时)

        // 统一格式字段
        QString sentDataA;
        QString sentDataB;
        QString expectReceiveA;
        QString expectReceiveB;
        QString actualReceiveA; // 对应旧的 actualReceiveData
        QString actualReceiveB;
        QString crc16;
        bool crc16Enabled = false;  // 新增：是否启用CRC16校验
        int stationId;              // 设备站号(00-FF)，统一格式也使用
        int functionCode = 0;
        QString targetSerialPort = "A"; // "A" or "B"
        bool compareEnabled = false; // 新增：是否启用比对判断

        // 旧的串口测试相关字段，逐步迁移到新字段
        QString sendData;           // 发送的串口数据 (映射到 sentDataA)
        QString expectReceiveData;  // 期望接收的串口数据 (映射到 expectReceiveA)
        QString actualReceiveData;  // 实际接收的串口数据 (映射到 actualReceiveA)

        // Default constructor
        TestStep() : passed(false), delay(0), crc16Enabled(false), stationId(0), functionCode(0), targetSerialPort("A") {}

        // Constructor for convenience (can be updated or overloaded)
        TestStep(QString t, QString dev, QString cmd, QString exp, QString act, bool p, QString n, QString ct, int dly,
                 QString sda, QString sdb, QString era, QString erb,
                 QString ara, QString arb, QString crc, 
                 int sid, int fc, QString port)
            : type(t), device(dev), command(cmd), expected(exp), actual(act), passed(p), notes(n), configType(ct), delay(dly),
              sentDataA(sda), sentDataB(sdb), expectReceiveA(era), expectReceiveB(erb),
              actualReceiveA(ara), actualReceiveB(arb), crc16(crc), 
              stationId(sid), functionCode(fc), targetSerialPort(port) {
            // Map old fields if new ones are primary
            if (sendData.isEmpty() && !sda.isEmpty()) sendData = sda;
            if (expectReceiveData.isEmpty() && !era.isEmpty()) expectReceiveData = era;
            if (actualReceiveData.isEmpty() && !ara.isEmpty()) actualReceiveData = ara;
        }
    };
    QList<TestStep> testSteps;
    QListWidget *testStepsList;

    // 撤销/重做栈
    QStack<QPair<QString, QString>> undoStack;
    QStack<QPair<QString, QString>> redoStack;
    
    // 映射关系的撤销/重做栈
    QStack<QList<MappingRelation>> undoMappingStack;
    QStack<QList<MappingRelation>> redoMappingStack;

    // UI组件
    QComboBox *serialPortComboBox;
    QComboBox *deviceComboBox;
    QListWidget *mappingList;
    QGraphicsScene *scene;
    QGraphicsView *view;
    
    // 新增的UI组件
    QTextEdit *logTextEdit;
    QPushButton *testButton;
    QPushButton *clearLogButton;
    
    // 设备列表
    QStringList controllerDevices;
    QStringList protectionDevices;
    
    // 新增的测试相关成员变量
    ModbusSerialA *serialA = nullptr;
    ModbusSerialB *serialB = nullptr;
    QTimer *testTimer;
    QTimer *testTimeoutTimer;
    int currentTestIndex;
    bool isRunningTests;
    QString receivedDataA;
    QString receivedDataB;
    QList<QGraphicsItem*> flowchartItems; // 用于存储流程图中的图形项，便于更新
    int selectedNodeIndex = -1; // 当前选中的流程图节点索引，-1表示没有选中
    
    // 命令映射表
    QMap<QString, QString> controllerRemoteOnCommands;  // 遥控合命令
    QMap<QString, QString> controllerRemoteOffCommands; // 遥控分命令
    QMap<QString, QString> controllerReadCommands;      // 读数命令

    QMap<QString, QString> protectionRemoteCommands;    // 遥控命令
    QMap<QString, QString> protectionReadCommands;      // 读数命令
    
    // UI对象
    Ui::MappingWindow *ui;
};

#endif // MAPPINGWINDOW_H
