#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QSerialPort>
#include <QSerialPortInfo>
#include <QString>
#include <QTimer>

#include "modbusserial.h"
#include "serialmanager.h"
#include "mappingwindow.h"
#include "setvaluedialog.h"

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
class QTableWidgetItem;
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    // 菜单动作槽函数
    void on_actionExit_triggered();
    void on_actionMapping_triggered();
    void on_actionSetValue_triggered();
    
    // 串口连接槽函数
    void on_actionConnectA_triggered();
    void on_actionConnectB_triggered();
    void on_actionDisconnectA_triggered();
    void on_actionDisconnectB_triggered();
    
    // 数据发送槽函数
    void on_pushButtonSendA_clicked();
    void on_pushButtonSendB_clicked();
    
    // 清空接收区域槽函数
    void on_pushButtonClearA_clicked();
    void on_pushButtonClearB_clicked();
    
    // 信号处理槽函数
    void handleSerialConnectionChanged(const QString &portId, bool connected);
    void handleSerialError(const QString &portId, const QString &error);

private:
    Ui::MainWindow *ui;
    SerialManager *m_serialManager;
    QTimer *timerAutoReadA;
    QTimer *timerAutoReadB;
    
    // 私有方法
    void InitialSetting();
    void SearchSerialPorts();
};

#endif // MAINWINDOW_H
