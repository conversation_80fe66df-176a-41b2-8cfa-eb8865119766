#include "setvaluedialog.h"
#include "ui_setvaluedialog.h"
#include "parseitemdialog.h"
#include "valuefileselectiondialog.h"
#include <QMessageBox>
#include <QDebug>
#include <QFileDialog>
#include <QFileInfo>
#include <QJsonDocument>
#include <QJsonArray>
#include <QJsonObject>
#include <QFile>
#include <QMenu>
#include <QInputDialog>
#include <QDateTime>
#include <QThread>
#include <QTimer>
#include <QKeyEvent>
#include <QSet>
#include <QApplication>
#include <QCoreApplication>
#include <QScreen>
#include <QAxObject>
#include <QVariant>
#include <QStandardPaths>
#include <algorithm>

SetValueDialog::SetValueDialog(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::SetValueDialog),
    m_serialManager(nullptr),
    serialA(nullptr),
    serialB(nullptr),
    currentSerial(nullptr)
{
    ui->setupUi(this);
    
    // 保存原始窗口大小
    originalSize = this->size();
    
    initUI();
    
    // 设置窗口图标
    setWindowIcon(QIcon(":/images/images/settings.png"));
    
    // 连接右键菜单信号
    ui->tableWidgetParse->setContextMenuPolicy(Qt::CustomContextMenu);
    connect(ui->tableWidgetParse, &QTableWidget::customContextMenuRequested, this, &SetValueDialog::showContextMenu);
    
    // 连接表格项变更信号
    connect(ui->tableWidgetParse, &QTableWidget::itemChanged, this, &SetValueDialog::onTableItemChanged);
    
    // 初始化延迟保存定时器
    saveTimer = new QTimer(this);
    saveTimer->setSingleShot(true);
    saveTimer->setInterval(500); // 500ms延迟
    connect(saveTimer, &QTimer::timeout, this, &SetValueDialog::performDelayedSave);
}

SetValueDialog::~SetValueDialog()
{
    delete ui;
}

void SetValueDialog::setSerialPorts(ModbusSerialA *portA, ModbusSerialB *portB)
{
    serialA = portA;
    serialB = portB;
    
    // 连接串口信号
    if (serialA) {
        connect(serialA, &ModbusSerialA::communicationMessageReceived, this, &SetValueDialog::handleSerialMessageReceived);
    }
    
    if (serialB) {
        connect(serialB, &ModbusSerialB::communicationMessageReceived, this, &SetValueDialog::handleSerialMessageReceived);
    }
    
    // 更新串口下拉框
    ui->comboBoxPort->clear();
    if (serialA) {
        ui->comboBoxPort->addItem("串口A");
    }
    if (serialB) {
        ui->comboBoxPort->addItem("串口B");
    }
    
    // 默认选择第一个串口
    if (ui->comboBoxPort->count() > 0) {
        ui->comboBoxPort->setCurrentIndex(0);
        on_comboBoxPort_currentIndexChanged(0);
    }
}

void SetValueDialog::initUI()
{
    // 初始化日志控制变量
    enableDebugLog = false;  // 默认关闭调试日志
    enableUILog = false;      // 默认开启UI日志
    
    // 设置表格列标题
    ui->tableWidgetParse->setColumnCount(23);
    ui->tableWidgetParse->setHorizontalHeaderLabels(QStringList() 
        << "序号" << "名称(英文)" << "描述(中文)" << "数据类型" << "读取方向" 
        << "采集周期(毫秒)" << "存储器类型序号" << "存储器地址" << "解析方式" << "位偏移量" << "BCD解析"
        << "地址" << "处理方式" << "最小值" << "最大值" << "单位" << "特性选择" << "投退选择" << "保护投退" << "期望数据" << "期望数据(定值文件读取)" << "显示数据" << "比对");
    
    // 优化表格显示 - 使用固定列宽模式提升性能
    ui->tableWidgetParse->horizontalHeader()->setSectionResizeMode(QHeaderView::Interactive);
    // 设置合理的默认列宽
    ui->tableWidgetParse->setColumnWidth(0, 50);   // 序号
    ui->tableWidgetParse->setColumnWidth(1, 150);  // 名称
    ui->tableWidgetParse->setColumnWidth(2, 80);  // 描述
    ui->tableWidgetParse->setColumnWidth(3, 80);   // 数据类型
    ui->tableWidgetParse->setColumnWidth(4, 80);   // 读取方向
    ui->tableWidgetParse->setColumnWidth(5, 80);  // 采集周期
    ui->tableWidgetParse->setColumnWidth(6, 80);  // 存储器类型序号
    ui->tableWidgetParse->setColumnWidth(7, 80);  // 存储器地址
    ui->tableWidgetParse->setColumnWidth(8, 150);  // 解析方式
    ui->tableWidgetParse->setColumnWidth(9, 60);   // 位偏移量
    ui->tableWidgetParse->setColumnWidth(10, 60);  // BCD解析
    ui->tableWidgetParse->setColumnWidth(11, 60);  // 地址
    ui->tableWidgetParse->setColumnWidth(12, 80); // 处理方式
    ui->tableWidgetParse->setColumnWidth(13, 60);  // 最小值
    ui->tableWidgetParse->setColumnWidth(14, 60);  // 最大值
    ui->tableWidgetParse->setColumnWidth(15, 50);  // 单位
    ui->tableWidgetParse->setColumnWidth(16, 60);  // 特性选择
    ui->tableWidgetParse->setColumnWidth(17, 60);  // 投退选择
    ui->tableWidgetParse->setColumnWidth(18, 60);  // 保护投退
    ui->tableWidgetParse->setColumnWidth(19, 80); // 期望数据
    ui->tableWidgetParse->setColumnWidth(20, 80); // 期望数据(定值文件读取)
    ui->tableWidgetParse->setColumnWidth(21, 80); // 显示数据
    ui->tableWidgetParse->setColumnWidth(22, 60);  // 比对
    ui->tableWidgetParse->verticalHeader()->setVisible(false);
    ui->tableWidgetParse->setAlternatingRowColors(true);
    ui->tableWidgetParse->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->tableWidgetParse->setSelectionMode(QAbstractItemView::ExtendedSelection);
    ui->tableWidgetParse->setEditTriggers(QAbstractItemView::NoEditTriggers);
    
    // 设置默认值
    ui->spinBoxDeviceAddress->setValue(1);
    ui->comboBoxFunctionCode->setCurrentIndex(2); // 默认03功能码
    ui->lineEditDataAddress->setText("00");
    ui->spinBoxDataCount->setValue(30); // 默认0x1E
    ui->checkBoxCRC16->setChecked(true);
    ui->spinBoxDelay->setValue(1000);
    
    // 设置默认设备型号为"无设备"（假设"无设备"在下拉框的最后一个位置）
    int deviceCount = ui->comboBoxDeviceModel->count();
    if (deviceCount > 0) {
        // 查找"无设备"选项的索引
        int noDeviceIndex = -1;
        for (int i = 0; i < deviceCount; i++) {
            if (ui->comboBoxDeviceModel->itemText(i) == "无设备") {
                noDeviceIndex = i;
                break;
            }
        }
        if (noDeviceIndex >= 0) {
            ui->comboBoxDeviceModel->setCurrentIndex(noDeviceIndex);
        }
    }
    
    // 手动调用一次配置函数来设置"无设备"的初始状态
    // 注意：QMetaObject::connectSlotsByName已经自动连接了on_comboBoxDeviceModel_currentIndexChanged槽函数
    configureDeviceParameters("无设备");
    loadDeviceConfigFile("无设备");
    
    // 连接隐藏复选框的信号槽
    connect(ui->checkBoxHideParseConfig, &QCheckBox::toggled, this, [this](bool checked) {
        ui->groupBoxParse->setVisible(!checked);
        ui->checkBoxCRC16->setVisible(!checked);
        ui->labelFunctionCode->setVisible(!checked);
        ui->comboBoxFunctionCode->setVisible(!checked);
        ui->labelDataAddress->setVisible(!checked);
        ui->lineEditDataAddress->setVisible(!checked);
        ui->labelDataCount->setVisible(!checked);
        ui->spinBoxDataCount->setVisible(!checked);
        ui->labelDelay->setVisible(!checked);
        ui->spinBoxDelay->setVisible(!checked);
        
        if (!checked) {
            // 取消隐藏时，恢复原始大小并居中显示
            this->resize(originalSize);
            
            // 获取屏幕几何信息
            QRect screenGeometry = QApplication::primaryScreen()->geometry();
            int x = (screenGeometry.width() - this->width()) / 2;
            int y = (screenGeometry.height() - this->height()) / 2;
            this->move(x, y);
        } else {
            // 隐藏时，调整窗口大小以适应内容
            this->adjustSize();
        }
    });
    
    // 默认隐藏报文解析配置栏、CRC16复选框和相关控件
    ui->groupBoxParse->setVisible(false);
    ui->checkBoxCRC16->setVisible(false);
    ui->labelFunctionCode->setVisible(false);
    ui->comboBoxFunctionCode->setVisible(false);
    ui->labelDataAddress->setVisible(false);
    ui->lineEditDataAddress->setVisible(false);
    ui->labelDataCount->setVisible(false);
    ui->spinBoxDataCount->setVisible(false);
    ui->labelDelay->setVisible(false);
    ui->spinBoxDelay->setVisible(false);
    
    // 设置表格键盘事件处理
    ui->tableWidgetParse->installEventFilter(this);
}

void SetValueDialog::updateParseTable()
{
    // 清空表格
    ui->tableWidgetParse->clearContents();
    ui->tableWidgetParse->setRowCount(parseItems.size());
    
    // 批量禁用信号以提升性能
    ui->tableWidgetParse->blockSignals(true);
    
    // 填充表格
    for (int i = 0; i < parseItems.size(); i++) {
        const ParseItem &item = parseItems[i];
        
        // 创建表格项的辅助函数
        auto createCenteredItem = [](const QString &text) -> QTableWidgetItem* {
            QTableWidgetItem *item = new QTableWidgetItem(text);
            item->setTextAlignment(Qt::AlignCenter);
            return item;
        };
        
        auto createItem = [](const QString &text) -> QTableWidgetItem* {
            return new QTableWidgetItem(text);
        };
        
        // 序号
        ui->tableWidgetParse->setItem(i, 0, createCenteredItem(QString::number(i + 1)));
        
        // 使用辅助函数创建表格项
        ui->tableWidgetParse->setItem(i, 1, createItem(item.name));
        ui->tableWidgetParse->setItem(i, 2, createItem(item.description));
        ui->tableWidgetParse->setItem(i, 3, createCenteredItem(item.dataType));
        ui->tableWidgetParse->setItem(i, 4, createCenteredItem(item.readDirection));
        ui->tableWidgetParse->setItem(i, 5, createCenteredItem(QString::number(item.samplePeriod)));
        ui->tableWidgetParse->setItem(i, 6, createCenteredItem(QString::number(item.deviceTypeId)));
        ui->tableWidgetParse->setItem(i, 7, createCenteredItem(QString::number(item.deviceAddress)));
        ui->tableWidgetParse->setItem(i, 8, createItem(item.parseMethod));
        ui->tableWidgetParse->setItem(i, 9, createCenteredItem(QString::number(item.bitOffset)));
        ui->tableWidgetParse->setItem(i, 10, createCenteredItem(item.bcdParse ? "是" : "否"));
        ui->tableWidgetParse->setItem(i, 11, createCenteredItem(item.address));
        ui->tableWidgetParse->setItem(i, 12, createItem(item.processMethod));
        ui->tableWidgetParse->setItem(i, 13, createCenteredItem(QString::number(item.minValue, 'f', 4)));
        ui->tableWidgetParse->setItem(i, 14, createCenteredItem(QString::number(item.maxValue, 'f', 4)));
        ui->tableWidgetParse->setItem(i, 15, createCenteredItem(item.unit));
        
        // 特性选择
        auto featureSelectionItem = createCenteredItem("");
        featureSelectionItem->setCheckState(item.featureSelection ? Qt::Checked : Qt::Unchecked);
        ui->tableWidgetParse->setItem(i, 16, featureSelectionItem);
        
        // 投退选择
        if (item.name.contains("投退") || item.description.contains("投退")) {
            // 包含"投退"关键字的行显示复选框
            auto enableProtectionItem = createCenteredItem("");
            enableProtectionItem->setCheckState(item.enableProtection ? Qt::Checked : Qt::Unchecked);
            ui->tableWidgetParse->setItem(i, 17, enableProtectionItem);
        } else {
            // 不包含"投退"关键字的行显示"---"
            ui->tableWidgetParse->setItem(i, 17, createCenteredItem("---"));
        }
        
        // 保护投退列：对于没有"投退"关键字的行显示"---"
        QString protectionStatusText = item.protectionStatus;
        if (!item.name.contains("投退") && !item.description.contains("投退")) {
            protectionStatusText = "---";
        }
        ui->tableWidgetParse->setItem(i, 18, createCenteredItem(protectionStatusText));
        ui->tableWidgetParse->setItem(i, 19, createCenteredItem(item.expectedData));
        ui->tableWidgetParse->setItem(i, 20, createCenteredItem(item.expectedDataFromFile));
        ui->tableWidgetParse->setItem(i, 21, createCenteredItem(item.actualData));
        
        // 比对结果（带颜色）
        auto compareResultItem = createCenteredItem(item.compareResult);
        if (item.compareResult == "✔") {
            compareResultItem->setForeground(QBrush(QColor(0, 128, 0))); // 绿色
        } else if (item.compareResult == "×") {
            compareResultItem->setForeground(QBrush(QColor(255, 0, 0))); // 红色
        }
        ui->tableWidgetParse->setItem(i, 22, compareResultItem);
    }
    
    // 重新启用信号
    ui->tableWidgetParse->blockSignals(false);
    
    // 移除自动列宽调整以提升性能
    // ui->tableWidgetParse->resizeColumnsToContents();
}

void SetValueDialog::updateSingleRow(int row)
{
    if (row < 0 || row >= parseItems.size()) return;
    
    const ParseItem &item = parseItems[row];
    
    // 序号
    QTableWidgetItem *idItem = new QTableWidgetItem(QString::number(row + 1));
    idItem->setTextAlignment(Qt::AlignCenter);
    ui->tableWidgetParse->setItem(row, 0, idItem);
    
    // 名称
    QTableWidgetItem *nameItem = new QTableWidgetItem(item.name);
    ui->tableWidgetParse->setItem(row, 1, nameItem);
    
    // 描述
    QTableWidgetItem *descItem = new QTableWidgetItem(item.description);
    ui->tableWidgetParse->setItem(row, 2, descItem);
    
    // 数据类型
    QTableWidgetItem *typeItem = new QTableWidgetItem(item.dataType);
    typeItem->setTextAlignment(Qt::AlignCenter);
    ui->tableWidgetParse->setItem(row, 3, typeItem);
    
    // 读取方向
    QTableWidgetItem *dirItem = new QTableWidgetItem(item.readDirection);
    dirItem->setTextAlignment(Qt::AlignCenter);
    ui->tableWidgetParse->setItem(row, 4, dirItem);
    
    // 采集周期
    QTableWidgetItem *periodItem = new QTableWidgetItem(QString::number(item.samplePeriod));
    periodItem->setTextAlignment(Qt::AlignCenter);
    ui->tableWidgetParse->setItem(row, 5, periodItem);
    
    // 存储器类型序号
    QTableWidgetItem *typeIdItem = new QTableWidgetItem(QString::number(item.deviceTypeId));
    typeIdItem->setTextAlignment(Qt::AlignCenter);
    ui->tableWidgetParse->setItem(row, 6, typeIdItem);
    
    // 存储器地址
    QTableWidgetItem *addrItem = new QTableWidgetItem(QString::number(item.deviceAddress));
    addrItem->setTextAlignment(Qt::AlignCenter);
    ui->tableWidgetParse->setItem(row, 7, addrItem);
    
    // 解析方式
    QTableWidgetItem *methodItem = new QTableWidgetItem(item.parseMethod);
    ui->tableWidgetParse->setItem(row, 8, methodItem);
    
    // 位偏移量
    QTableWidgetItem *offsetItem = new QTableWidgetItem(QString::number(item.bitOffset));
    offsetItem->setTextAlignment(Qt::AlignCenter);
    ui->tableWidgetParse->setItem(row, 9, offsetItem);
    
    // BCD解析
    QTableWidgetItem *bcdItem = new QTableWidgetItem(item.bcdParse ? "是" : "否");
    bcdItem->setTextAlignment(Qt::AlignCenter);
    ui->tableWidgetParse->setItem(row, 10, bcdItem);
    
    // 地址
    QTableWidgetItem *addressItem = new QTableWidgetItem(item.address);
    addressItem->setTextAlignment(Qt::AlignCenter);
    ui->tableWidgetParse->setItem(row, 11, addressItem);
    
    // 处理方式
    QTableWidgetItem *processMethodItem = new QTableWidgetItem(item.processMethod);
    ui->tableWidgetParse->setItem(row, 12, processMethodItem);
    
    // 最小值
    QTableWidgetItem *minValueItem = new QTableWidgetItem(QString::number(item.minValue, 'f', 4));
    minValueItem->setTextAlignment(Qt::AlignCenter);
    ui->tableWidgetParse->setItem(row, 13, minValueItem);
    
    // 最大值
    QTableWidgetItem *maxValueItem = new QTableWidgetItem(QString::number(item.maxValue, 'f', 4));
    maxValueItem->setTextAlignment(Qt::AlignCenter);
    ui->tableWidgetParse->setItem(row, 14, maxValueItem);
    
    // 单位
    QTableWidgetItem *unitItem = new QTableWidgetItem(item.unit);
    unitItem->setTextAlignment(Qt::AlignCenter);
    ui->tableWidgetParse->setItem(row, 15, unitItem);
    
    // 特性选择
    QTableWidgetItem *featureSelectionItem = new QTableWidgetItem();
    featureSelectionItem->setTextAlignment(Qt::AlignCenter);
    featureSelectionItem->setCheckState(item.featureSelection ? Qt::Checked : Qt::Unchecked);
    ui->tableWidgetParse->setItem(row, 16, featureSelectionItem);
    
    // 投退选择
    if (item.name.contains("投退") || item.description.contains("投退")) {
        // 包含"投退"关键字的行显示复选框
        QTableWidgetItem *enableProtectionItem = new QTableWidgetItem();
        enableProtectionItem->setTextAlignment(Qt::AlignCenter);
        enableProtectionItem->setCheckState(item.enableProtection ? Qt::Checked : Qt::Unchecked);
        ui->tableWidgetParse->setItem(row, 17, enableProtectionItem);
    } else {
        // 不包含"投退"关键字的行显示"---"
        QTableWidgetItem *enableProtectionItem = new QTableWidgetItem("---");
        enableProtectionItem->setTextAlignment(Qt::AlignCenter);
        ui->tableWidgetParse->setItem(row, 17, enableProtectionItem);
    }
    
    // 保护投退列：对于没有"投退"关键字的行显示"---"
    QString protectionStatusText = item.protectionStatus;
    if (!item.name.contains("投退") && !item.description.contains("投退")) {
        protectionStatusText = "---";
    }
    QTableWidgetItem *protectionStatusItem = new QTableWidgetItem(protectionStatusText);
    protectionStatusItem->setTextAlignment(Qt::AlignCenter);
    ui->tableWidgetParse->setItem(row, 18, protectionStatusItem);
    
    // 期望数据
    QTableWidgetItem *expectedDataItem = new QTableWidgetItem(item.expectedData);
    expectedDataItem->setTextAlignment(Qt::AlignCenter);
    ui->tableWidgetParse->setItem(row, 19, expectedDataItem);
    
    // 期望数据(定值文件读取)
    QTableWidgetItem *expectedDataFromFileItem = new QTableWidgetItem(item.expectedDataFromFile);
    expectedDataFromFileItem->setTextAlignment(Qt::AlignCenter);
    ui->tableWidgetParse->setItem(row, 20, expectedDataFromFileItem);
    
    // 实际数据
    QTableWidgetItem *actualDataItem = new QTableWidgetItem(item.actualData);
    actualDataItem->setTextAlignment(Qt::AlignCenter);
    ui->tableWidgetParse->setItem(row, 21, actualDataItem);
    
    // 比对结果
    QTableWidgetItem *compareResultItem = new QTableWidgetItem(item.compareResult);
    compareResultItem->setTextAlignment(Qt::AlignCenter);
    // 根据比对结果设置颜色
    if (item.compareResult == "✔") {
        compareResultItem->setForeground(QBrush(QColor(0, 128, 0))); // 绿色
    } else if (item.compareResult == "×") {
        compareResultItem->setForeground(QBrush(QColor(255, 0, 0))); // 红色
    }
    ui->tableWidgetParse->setItem(row, 22, compareResultItem);
    
    // 移除自动列宽调整以提升性能
    // ui->tableWidgetParse->resizeColumnsToContents();
}

void SetValueDialog::on_comboBoxPort_currentIndexChanged(int index)
{
    if (index < 0) return;
    
    QString portName = ui->comboBoxPort->currentText();
    if (portName == "串口A") {
        currentSerial = serialA;
    } else if (portName == "串口B") {
        currentSerial = serialB;
    } else {
        currentSerial = nullptr;
    }
    
    // 更新UI状态
    bool connected = currentSerial && currentSerial->isConnected();
    ui->pushButtonSend->setEnabled(connected);
}

void SetValueDialog::on_pushButtonSend_clicked()
{
    if (!currentSerial || !currentSerial->isConnected()) {
        QMessageBox::warning(this, tr("警告"), tr("当前串口未连接"));
        return;
    }
    
    // 获取参数
    int deviceAddress = ui->spinBoxDeviceAddress->value();
    int functionCode = ui->comboBoxFunctionCode->currentIndex() + 1;
    
    // 解析数据地址
    bool ok;
    QString dataAddressStr = ui->lineEditDataAddress->text().trimmed();
    int dataAddress = dataAddressStr.toInt(&ok, 16);
    if (!ok) {
        QMessageBox::warning(this, tr("警告"), tr("数据地址格式错误，请输入十六进制数"));
        return;
    }
    
    // 获取数据个数
    int dataCount = ui->spinBoxDataCount->value();
    
    // 创建数据部分
    QByteArray data;
    data.append((dataAddress >> 8) & 0xFF); // 高字节
    data.append(dataAddress & 0xFF);        // 低字节
    data.append((dataCount >> 8) & 0xFF);   // 高字节
    data.append(dataCount & 0xFF);          // 低字节
    
    // 创建完整的Modbus帧
    QByteArray frame = createModbusFrame(deviceAddress, functionCode, data);
    
    // 发送数据
    if (currentSerial->sendRawData(frame)) {
        QDateTime currentTime = QDateTime::currentDateTime();
        QString hexString;
        for (int i = 0; i < frame.size(); ++i) {
            hexString += QString("%1 ").arg(static_cast<quint8>(frame[i]), 2, 16, QChar('0')).toUpper();
        }
        QString logMessage = currentTime.toString("[hh:mm:ss.zzz] ") + "TX: " + hexString.trimmed();
        ui->textBrowserLog->append(QString("<font color='#0000FF'>%1</font>").arg(logMessage));
    } else {
        QMessageBox::warning(this, tr("警告"), tr("发送数据失败"));
    }
}

void SetValueDialog::on_checkBoxCRC16_stateChanged(int state)
{
    // 更新UI状态
    bool useCRC16 = (state == Qt::Checked);
    // 这里可以添加其他与CRC16相关的UI更新
}

void SetValueDialog::on_tableWidgetParse_itemDoubleClicked(QTableWidgetItem *item)
{
    if (!item) return;
    
    // 获取当前行
    int row = item->row();
    if (row >= 0 && row < parseItems.size()) {
        editParseItem();
    }
}

void SetValueDialog::on_pushButtonGenerateReport_clicked()
{
    // 生成HTML报告内容
    QString htmlContent = generateHtmlReport();
    
    // 询问用户保存为HTML还是PDF
    QMessageBox::StandardButton reply;
    reply = QMessageBox::question(this, tr("选择报告格式"), 
                                tr("您想将报告保存为什么格式？"),
                                QMessageBox::StandardButton::Save | 
                                QMessageBox::StandardButton::SaveAll | 
                                QMessageBox::StandardButton::Cancel,
                                QMessageBox::StandardButton::Save);
    
    if (reply == QMessageBox::StandardButton::Cancel) {
        return;
    }
    
    // 获取保存路径
    QString defaultFileName = QString(tr("定值召唤报告_%1")).arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss"));
    QString selectedFilter;
    QString filePath;
    
    if (reply == QMessageBox::StandardButton::Save) {
        // 保存为HTML
        selectedFilter = tr("HTML文件 (*.html)");
        filePath = QFileDialog::getSaveFileName(this, tr("保存报告"), 
                                             defaultFileName + ".html", 
                                             tr("HTML文件 (*.html)"), 
                                             &selectedFilter);
        if (!filePath.isEmpty()) {
            if (saveReportToHtml(htmlContent, filePath)) {
                QMessageBox::information(this, tr("保存成功"), tr("报告已成功保存为HTML文件。"));
                // 询问是否打开文件
                if (QMessageBox::question(this, tr("打开文件"), tr("是否立即打开报告文件？"), 
                                        QMessageBox::Yes | QMessageBox::No) == QMessageBox::Yes) {
                    QDesktopServices::openUrl(QUrl::fromLocalFile(filePath));
                }
            } else {
                QMessageBox::critical(this, tr("保存失败"), tr("无法保存HTML报告文件。"));
            }
        }
    } else if (reply == QMessageBox::StandardButton::SaveAll) {
        // 保存为PDF
        selectedFilter = tr("PDF文件 (*.pdf)");
        filePath = QFileDialog::getSaveFileName(this, tr("保存报告"), 
                                             defaultFileName + ".pdf", 
                                             tr("PDF文件 (*.pdf)"), 
                                             &selectedFilter);
        if (!filePath.isEmpty()) {
            if (saveReportToPdf(htmlContent, filePath)) {
                QMessageBox::information(this, tr("保存成功"), tr("报告已成功保存为PDF文件。"));
                // 询问是否打开文件
                if (QMessageBox::question(this, tr("打开文件"), tr("是否立即打开报告文件？"), 
                                        QMessageBox::Yes | QMessageBox::No) == QMessageBox::Yes) {
                    QDesktopServices::openUrl(QUrl::fromLocalFile(filePath));
                }
            } else {
                QMessageBox::critical(this, tr("保存失败"), tr("无法保存PDF报告文件。"));
            }
        }
    }
}

void SetValueDialog::showContextMenu(const QPoint &pos)
{
    QMenu menu(this);
    
    QAction *addAction = menu.addAction(tr("新建数据标签"));

    QAction *editAction = menu.addAction(tr("修改数据标签"));
    QAction *deleteAction = menu.addAction(tr("删除数据标签"));
    menu.addSeparator();
    QAction *saveAction = menu.addAction(tr("保存成CSV文件"));
    QAction *loadAction = menu.addAction(tr("从CSV文件中加载"));
    
    // 只有在选中行时才启用编辑和删除操作
    bool hasSelection = ui->tableWidgetParse->currentRow() >= 0;
    editAction->setEnabled(hasSelection);
    deleteAction->setEnabled(hasSelection);
    
    // 连接信号
    connect(addAction, &QAction::triggered, this, &SetValueDialog::addParseItem);
    connect(editAction, &QAction::triggered, this, &SetValueDialog::editParseItem);
    connect(deleteAction, &QAction::triggered, this, &SetValueDialog::deleteParseItem);
    connect(saveAction, &QAction::triggered, this, &SetValueDialog::saveParseItems);
    connect(loadAction, &QAction::triggered, this, &SetValueDialog::loadParseItems);
    
    // 显示菜单
    menu.exec(ui->tableWidgetParse->mapToGlobal(pos));
}

void SetValueDialog::addParseItem()
{
    // 创建一个新的解析项
    ParseItem newItem;
    newItem.name = "Tag" + QString::number(parseItems.size() + 1);
    newItem.description = "标签" + QString::number(parseItems.size() + 1);
    newItem.dataType = "UINT16";
    newItem.readDirection = "只读";
    newItem.samplePeriod = 1000;
    newItem.deviceTypeId = 3;
    newItem.deviceAddress = 0;
    newItem.parseMethod = "2字节 无符号 先高后低";
    newItem.bitOffset = 0;
    newItem.bcdParse = 0;
    
    // 新增字段的默认值
    newItem.address = QString::number(40000 + parseItems.size() + 1);
    newItem.processMethod = "原始数据";
    newItem.minValue = 0.0;
    newItem.maxValue = 100.0;
    newItem.unit = "";
    newItem.processParam = 1.0;
    newItem.protectionStatus = "";
    newItem.expectedData = "";
    newItem.actualData = "";
    newItem.compareResult = "";
    
    // 使用ParseItemDialog编辑新项
    ParseItemDialog dialog(newItem, this);
    
    // 标记是否已经处理了批量增加
    bool batchProcessed = false;
    
    // 连接批量增加信号
    connect(&dialog, &ParseItemDialog::batchItemsCreated, this, [this, &batchProcessed](const QList<ParseItem> &batchItems) {
        // 添加批量项目到列表
        for (const ParseItem &item : batchItems) {
            parseItems.append(item);
        }
        
        // 更新表格
        updateParseTable();
        
        // 选中最后添加的行
        if (!parseItems.isEmpty()) {
            ui->tableWidgetParse->selectRow(parseItems.size() - 1);
        }
        
        // 标记已处理批量增加
        batchProcessed = true;
    });
    
    if (dialog.exec() == QDialog::Accepted && !batchProcessed) {
        // 如果是普通确认（非批量增加），添加单个项目到列表
        parseItems.append(newItem);
        
        // 更新表格
        updateParseTable();
        
        // 选中新添加的行
        ui->tableWidgetParse->selectRow(parseItems.size() - 1);
    }
}

void SetValueDialog::editParseItem()
{
    int row = ui->tableWidgetParse->currentRow();
    if (row < 0 || row >= parseItems.size()) return;
    
    ParseItem &item = parseItems[row];
    
    // 使用ParseItemDialog编辑项
    ParseItemDialog dialog(item, this);
    if (dialog.exec() == QDialog::Accepted) {
        // 只更新被编辑的那一行，而不是整个表格
        updateSingleRow(row);
    }
}

void SetValueDialog::deleteParseItem()
{
    int row = ui->tableWidgetParse->currentRow();
    if (row < 0 || row >= parseItems.size()) return;
    
    // 确认删除
    QMessageBox::StandardButton reply = QMessageBox::question(this, tr("确认删除"),
                                                           tr("确定要删除选中的数据标签吗？"),
                                                           QMessageBox::Yes | QMessageBox::No);
    if (reply == QMessageBox::Yes) {
        // 从列表中移除
        parseItems.removeAt(row);
        
        // 更新表格
        updateParseTable();
    }
}

void SetValueDialog::deleteSelectedParseItems()
{
    // 获取选中的行
    QList<QTableWidgetSelectionRange> ranges = ui->tableWidgetParse->selectedRanges();
    if (ranges.isEmpty()) {
        QMessageBox::information(this, tr("提示"), tr("请先选择要删除的数据标签"));
        return;
    }
    
    // 收集所有选中的行号
    QSet<int> selectedRows;
    for (const QTableWidgetSelectionRange &range : ranges) {
        for (int row = range.topRow(); row <= range.bottomRow(); ++row) {
            selectedRows.insert(row);
        }
    }
    
    if (selectedRows.isEmpty()) return;
    
    // 确认删除
    QString message = tr("确定要删除选中的 %1 个数据标签吗？").arg(selectedRows.size());
    QMessageBox::StandardButton reply = QMessageBox::question(this, tr("确认删除"), message,
                                                           QMessageBox::Yes | QMessageBox::No);
    if (reply == QMessageBox::Yes) {
        // 将行号转换为列表并按降序排序，从后往前删除以避免索引变化
        QList<int> rowList(selectedRows.begin(), selectedRows.end());
        std::sort(rowList.begin(), rowList.end(), std::greater<int>());
        
        // 从后往前删除
        for (int row : rowList) {
            if (row >= 0 && row < parseItems.size()) {
                parseItems.removeAt(row);
            }
        }
        
        // 更新表格
        updateParseTable();
    }
}

bool SetValueDialog::eventFilter(QObject *obj, QEvent *event)
{
    if (obj == ui->tableWidgetParse && event->type() == QEvent::KeyPress) {
        QKeyEvent *keyEvent = static_cast<QKeyEvent*>(event);
        if (keyEvent->key() == Qt::Key_Delete) {
            deleteSelectedParseItems();
            return true;
        }
    }
    return QMainWindow::eventFilter(obj, event);
}

void SetValueDialog::saveParseItems()
{
    QString filePath = QFileDialog::getSaveFileName(this, tr("保存数据标签"), "", tr("CSV文件 (*.csv)"));
    if (filePath.isEmpty()) return;
    
    if (saveParseItemsToFile(filePath)) {
        QMessageBox::information(this, tr("保存成功"), tr("数据标签已成功保存到文件"));
    } else {
        QMessageBox::warning(this, tr("保存失败"), tr("保存数据标签时发生错误"));
    }
}

void SetValueDialog::loadParseItems()
{
    // 直接显示文件选择对话框让用户选择要加载的CSV文件
    QString filePath = QFileDialog::getOpenFileName(this, 
        tr("选择CSV解析配置文件"), 
        "", 
        tr("CSV文件 (*.csv);;所有文件 (*.*)"));
    
    if (!filePath.isEmpty()) {
        if (loadParseItemsFromFile(filePath)) {
            updateParseTable();
            QMessageBox::information(this, tr("成功"), tr("CSV文件加载成功！"));
        } else {
            QMessageBox::warning(this, tr("错误"), tr("CSV文件加载失败！"));
        }
    }
}

bool SetValueDialog::saveParseItemsToFile(const QString &filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::warning(this, tr("错误"), tr("无法打开文件进行写入: %1").arg(filePath));
        return false;
    }
    
    // 写入UTF-8 BOM，让Excel能够正确识别UTF-8编码
    file.write("\xEF\xBB\xBF");
    
    QTextStream out(&file);
#if QT_VERSION >= QT_VERSION_CHECK(6, 0, 0)
    out.setEncoding(QStringConverter::Utf8);
#else
    out.setCodec("UTF-8");
#endif
    
    // 写入CSV头
    out << "名称,描述,数据类型,读取方向,采集周期,存储器类型序号,存储器地址,解析方式,位偏移量,BCD解析,地址,处理方式,最小值,最大值,单位,数据处理参数,特性选择,投退选择,保护投退,期望数据,期望数据(定值文件读取),显示数据,比对\n";
    
    // 写入数据 - 优化字符串操作
    for (const ParseItem &item : parseItems) {
        // 使用静态函数减少重复代码
        auto escapeCSV = [](const QString &str) -> QString {
            return str.contains(',') ? ('"' + str + '"') : str;
        };
        
        QString name = escapeCSV(item.name);
        QString desc = escapeCSV(item.description);
        QString dataType = escapeCSV(item.dataType);
        QString readDir = escapeCSV(item.readDirection);
        QString parseMethod = escapeCSV(item.parseMethod);
        QString address = escapeCSV(item.address);
        QString processMethod = escapeCSV(item.processMethod);
        QString unit = escapeCSV(item.unit);
        QString protectionStatus = escapeCSV(item.protectionStatus);
        QString expectedData = escapeCSV(item.expectedData);
        QString expectedDataFromFile = escapeCSV(item.expectedDataFromFile);
        QString actualData = escapeCSV(item.actualData);
        QString compareResult = escapeCSV(item.compareResult);
        
        out << name << ","
            << desc << ","
            << dataType << ","
            << readDir << ","
            << item.samplePeriod << ","
            << item.deviceTypeId << ","
            << item.deviceAddress << ","
            << parseMethod << ","
            << item.bitOffset << ","
            << item.bcdParse << ","
            << address << ","
            << processMethod << ","
            << item.minValue << ","
            << item.maxValue << ","
            << unit << ","
            << item.processParam << ","
            << (item.featureSelection ? 1 : 0) << ","
            << (item.enableProtection ? 1 : 0) << ","
            << protectionStatus << ","
            << expectedData << ","
            << expectedDataFromFile << ","
            << actualData << ","
            << compareResult << "\n";
    }
    
    file.close();
    // 不显示保存成功信息
    return true;
}

bool SetValueDialog::loadParseItemsFromFile(const QString &filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QMessageBox::warning(this, tr("错误"), tr("无法打开文件进行读取: %1").arg(filePath));
        return false;
    }
    
    QTextStream in(&file);
#if QT_VERSION >= QT_VERSION_CHECK(6, 0, 0)
    in.setEncoding(QStringConverter::Utf8);
#else
    in.setCodec("UTF-8");
#endif
    
    // 读取第一行，检查是否为标题行
    QString firstLine = in.readLine();
    bool hasHeader = firstLine.startsWith("名称,描述");
    
    // 如果不是标题行，需要重置文件指针到开头
    if (!hasHeader) {
        file.seek(0);
    }
    
    // 清空当前解析项
    parseItems.clear();
    
    // 预分配空间以提升性能
    parseItems.reserve(1000);
    
    // 读取数据
    while (!in.atEnd()) {
        QString line = in.readLine();
        if (line.isEmpty()) continue;
        
        // 优化CSV解析 - 减少内存分配
        QList<QString> fields;
        fields.reserve(25); // 预分配字段数量
        bool inQuotes = false;
        QString field;
        field.reserve(100); // 预分配字段长度
        
        const int lineLength = line.length();
        for (int i = 0; i < lineLength; i++) {
            const QChar c = line[i];
            
            if (c == '"') {
                inQuotes = !inQuotes;
            } else if (c == ',' && !inQuotes) {
                fields.append(field);
                field.clear();
            } else {
                field.append(c);
            }
        }
        
        // 添加最后一个字段
        fields.append(field);
        
        if (fields.size() < 9) continue; // 至少需要9个字段
        
        ParseItem item;
        item.name = fields[0];
        item.description = fields[1];
        item.dataType = fields[2];
        item.readDirection = fields[3];
        item.samplePeriod = fields[4].toInt();
        item.deviceTypeId = fields[5].toInt();
        item.deviceAddress = fields[6].toInt();
        item.parseMethod = fields[7];
        item.bitOffset = fields[8].toInt();
        
        // 如果有BCD解析字段
        if (fields.size() > 9) {
            item.bcdParse = fields[9].toInt();
        } else {
            item.bcdParse = 0;
        }
        
        // 读取新增字段（如果存在）
        if (fields.size() >= 23) {
            // 最新格式，包含featureSelection和expectedDataFromFile字段
            item.address = fields[10];
            item.processMethod = fields[11];
            item.minValue = fields[12].toDouble();
            item.maxValue = fields[13].toDouble();
            item.unit = fields[14];
            item.processParam = fields[15].toDouble();
            item.featureSelection = (fields[16].toInt() == 1);
            item.enableProtection = (fields[17].toInt() == 1);
            item.protectionStatus = fields[18];
            item.expectedData = fields[19];
            item.expectedDataFromFile = fields[20];
            item.actualData = fields[21];
            item.compareResult = fields[22];
        } else if (fields.size() >= 21) {
            // 旧格式，包含enableProtection字段但不包含新增字段
            item.address = fields[10];
            item.processMethod = fields[11];
            item.minValue = fields[12].toDouble();
            item.maxValue = fields[13].toDouble();
            item.unit = fields[14];
            item.processParam = fields[15].toDouble();
            item.featureSelection = false; // 默认值
            item.enableProtection = (fields[16].toInt() == 1);
            item.protectionStatus = fields[17];
            item.expectedData = fields[18];
            item.expectedDataFromFile = ""; // 默认值
            item.actualData = fields[19];
            item.compareResult = fields[20];
        } else if (fields.size() >= 20) {
            // 更旧格式，包含processParam字段但不包含enableProtection
            item.address = fields[10];
            item.processMethod = fields[11];
            item.minValue = fields[12].toDouble();
            item.maxValue = fields[13].toDouble();
            item.unit = fields[14];
            item.processParam = fields[15].toDouble();
            item.featureSelection = false; // 默认值
            item.enableProtection = false; // 默认值
            item.protectionStatus = fields[16];
            item.expectedData = fields[17];
            item.expectedDataFromFile = ""; // 默认值
            item.actualData = fields[18];
            item.compareResult = fields[19];
        } else if (fields.size() >= 19) {
            // 更旧格式，不包含processParam和enableProtection字段
            item.address = fields[10];
            item.processMethod = fields[11];
            item.minValue = fields[12].toDouble();
            item.maxValue = fields[13].toDouble();
            item.unit = fields[14];
            item.processParam = 1.0; // 默认值
            item.featureSelection = false; // 默认值
            item.enableProtection = false; // 默认值
            item.protectionStatus = fields[15];
            item.expectedData = fields[16];
            item.expectedDataFromFile = ""; // 默认值
            item.actualData = fields[17];
            item.compareResult = fields[18];
        } else {
            // 为更旧格式文件设置默认值
            item.address = QString::number(40000 + parseItems.size() + 1);
            item.processMethod = "原始数据";
            item.minValue = 0.0;
            item.maxValue = 100.0;
            item.unit = "";
            item.processParam = 1.0;
            item.featureSelection = false; // 默认值
            item.enableProtection = false; // 默认值
            item.protectionStatus = "";
            item.expectedData = "";
            item.expectedDataFromFile = ""; // 默认值
            item.actualData = "";
            item.compareResult = "";
        }
        
        // 验证数据类型，如果不是新格式，则转换为新格式
        if (item.dataType == "浮点") {
            item.dataType = "FLOAT";
        } else if (item.dataType == "整型") {
            if (item.parseMethod.contains("无符号")) {
                item.dataType = "UINT16";
            } else {
                item.dataType = "INT16";
            }
        } else if (item.dataType == "字符串") {
            item.dataType = "STRING";
        } else if (item.dataType == "布尔") {
            item.dataType = "BIT";
        }
        
        // 验证解析方式，确保包含字节序信息
        if (item.parseMethod == "字节 无符号 先高后低") {
            item.parseMethod = "字节 无符号 先高后低(大端序)";
        } else if (item.parseMethod == "字节 无符号 先低后高") {
            item.parseMethod = "字节 无符号 先低后高(小端序)";
        } else if (item.parseMethod == "字节 有符号 先高后低") {
            item.parseMethod = "字节 有符号 先高后低(大端序)";
        } else if (item.parseMethod == "字节 有符号 先低后高") {
            item.parseMethod = "字节 有符号 先低后高(小端序)";
        }
        
        // 检查名称或描述中是否包含"投退"关键字，如果有则自动设置投退选择为选中状态
        if (item.name.contains("投退") || item.description.contains("投退")) {
            item.enableProtection = true;
        }
        
        parseItems.append(item);
    }
    
    file.close();
    
    // 更新表格
    updateParseTable();
    
    // 不显示加载成功信息
    return true;
}

QByteArray SetValueDialog::createModbusFrame(int stationId, int functionCode, const QByteArray &data)
{
    QByteArray frame;
    frame.append(static_cast<char>(stationId));      // 站号
    frame.append(static_cast<char>(functionCode));   // 功能码
    frame.append(data);                              // 数据
    
    // 添加CRC校验
    if (ui->checkBoxCRC16->isChecked()) {
        frame.append(CrcUtils::calculateCRC16Bytes(frame));
    }
    
    return frame;
}

// 延迟保存函数实现
void SetValueDialog::performDelayedSave()
{
    if (pendingSave) {
        saveParseItemsToFile("parse_items.csv");
        pendingSave = false;
    }
}

// 定值文件选择按钮点击槽函数
void SetValueDialog::on_pushButtonSelectValueFile_clicked()
{
    // 获取当前选择的设备型号
    QString currentDeviceModel = ui->comboBoxDeviceModel->currentText();
    
    // 检查是否选择了有效的设备型号
    if (currentDeviceModel == "无设备") {
        QMessageBox::warning(this, "警告", "请先选择有效的设备型号！");
        return;
    }
    
    // 打开文件选择对话框
    QString fileName = QFileDialog::getOpenFileName(
        this,
        "选择定值文件",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation),
        "Excel文件 (*.xls *.xlsx)"
    );
    
    if (fileName.isEmpty()) {
        return; // 用户取消了文件选择
    }
    
    // 创建定值文件选择对话框（非模态）
    ValueFileSelectionDialog *dialog = new ValueFileSelectionDialog(fileName, currentDeviceModel, parseItems, this);
    
    // 连接日志信号到日志输出
    connect(dialog, &ValueFileSelectionDialog::logMessageGenerated, this, [this](const QString &message) {
        ui->textBrowserLog->append(QString("<font color='#008000'>%1</font>").arg(message));
    });
    
    // 连接数据导入完成信号，使用增量更新优化性能
    connect(dialog, &ValueFileSelectionDialog::dataImported, this, [this, dialog]() {
        // 获取最新的解析项数据
        QList<ParseItem> newParseItems = dialog->getUpdatedParseItems();
        
        // 找出发生变化的行并进行增量更新
        QSet<int> changedRows;
        for (int i = 0; i < qMin(parseItems.size(), newParseItems.size()); ++i) {
            // 检查expectedDataFromFile字段是否发生变化
            if (parseItems[i].expectedDataFromFile != newParseItems[i].expectedDataFromFile ||
                parseItems[i].expectedData != newParseItems[i].expectedData ||
                parseItems[i].featureSelection != newParseItems[i].featureSelection ||
                parseItems[i].enableProtection != newParseItems[i].enableProtection) {
                changedRows.insert(i);
            }
        }
        
        // 更新parseItems数据
        parseItems = newParseItems;
        
        // 只更新发生变化的行，避免全表重绘
        if (!changedRows.isEmpty()) {
            // 分批更新，每批最多20行，并在批次间调用processEvents保持UI响应
            QList<int> sortedRows = changedRows.values();
            std::sort(sortedRows.begin(), sortedRows.end());
            
            const int batchSize = 20;
            for (int i = 0; i < sortedRows.size(); i += batchSize) {
                int endIndex = qMin(i + batchSize, sortedRows.size());
                for (int j = i; j < endIndex; ++j) {
                    updateSingleRow(sortedRows[j]);
                }
                
                // 在批次间处理事件，保持UI响应
                if (endIndex < sortedRows.size()) {
                    QCoreApplication::processEvents();
                }
            }
            
            qDebug() << QString("数据导入完成，增量更新了%1行").arg(changedRows.size());
        } else {
            qDebug() << "数据导入完成，无需更新表格";
        }
    });
    
    // 连接对话框关闭信号，在关闭时使用增量更新
    connect(dialog, &QDialog::finished, this, [this, dialog](int result) {
        // 获取最新的解析项数据
        QList<ParseItem> newParseItems = dialog->getUpdatedParseItems();
        
        // 找出发生变化的行并进行增量更新
        QSet<int> changedRows;
        for (int i = 0; i < qMin(parseItems.size(), newParseItems.size()); ++i) {
            if (parseItems[i].expectedDataFromFile != newParseItems[i].expectedDataFromFile ||
                parseItems[i].expectedData != newParseItems[i].expectedData ||
                parseItems[i].featureSelection != newParseItems[i].featureSelection ||
                parseItems[i].enableProtection != newParseItems[i].enableProtection) {
                changedRows.insert(i);
            }
        }
        
        // 更新parseItems数据
        parseItems = newParseItems;
        
        // 只更新发生变化的行
        if (!changedRows.isEmpty()) {
            for (int row : changedRows) {
                updateSingleRow(row);
            }
            qDebug() << QString("对话框关闭，增量更新了%1行").arg(changedRows.size());
        }
        
        // 删除对话框对象
        dialog->deleteLater();
    });
    
    // 显示非模态对话框
    dialog->show();
    dialog->raise();
    dialog->activateWindow();
}

// 设备型号下拉框选择变更槽函数
void SetValueDialog::on_comboBoxDeviceModel_currentIndexChanged(int index)
{
    QString deviceModel = ui->comboBoxDeviceModel->itemText(index);
    configureDeviceParameters(deviceModel);
    loadDeviceConfigFile(deviceModel);
}

// 配置设备参数
void SetValueDialog::configureDeviceParameters(const QString &deviceModel)
{
    if (deviceModel == "无设备") {
        // 无设备配置：功能码03，地址00，数据个数30，延时1000，CRC16选中
        ui->comboBoxFunctionCode->setCurrentIndex(2); // 03功能码
        ui->lineEditDataAddress->setText("00");
        ui->spinBoxDataCount->setValue(30);
        ui->spinBoxDelay->setValue(1000);
        ui->checkBoxCRC16->setChecked(true);
    }
    else if (deviceModel == "LL510P") {
        // LL510P配置：功能码03，地址00，数据个数30，延时1000，CRC16选中
        ui->comboBoxFunctionCode->setCurrentIndex(2); // 03功能码
        ui->lineEditDataAddress->setText("00");
        ui->spinBoxDataCount->setValue(30);
        ui->spinBoxDelay->setValue(1000);
        ui->checkBoxCRC16->setChecked(true);
    }
    else if (deviceModel == "LM510P") {
        // LM510P配置：功能码03，地址00，数据个数64，延时1000，CRC16选中
        ui->comboBoxFunctionCode->setCurrentIndex(2); // 03功能码
        ui->lineEditDataAddress->setText("00");
        ui->spinBoxDataCount->setValue(64);
        ui->spinBoxDelay->setValue(1000);
        ui->checkBoxCRC16->setChecked(true);
    }
}

// 加载设备配置文件
bool SetValueDialog::loadDeviceConfigFile(const QString &deviceModel)
{
    // 如果是"无设备"，不加载任何配置文件，保持空状态
    if (deviceModel == "无设备") {
        // 清空解析数据和表格
        parseItems.clear();
        ui->tableWidgetParse->setRowCount(0);
        ui->textBrowserLog->append("无设备模式：报文解析配置为空状态");
        return true;
    }
    
    QString fileName = deviceModel + ".csv";
    QString filePath = QCoreApplication::applicationDirPath() + "/" + fileName;
    
    // 检查文件是否存在
    QFile file(filePath);
    if (file.exists()) {
        // 文件存在，直接加载
        if (loadParseItemsFromFile(filePath)) {
            updateParseTable();
            ui->textBrowserLog->append(QString("已自动加载设备配置文件：%1").arg(fileName));
            return true;
        }
        else {
            ui->textBrowserLog->append(QString("加载设备配置文件失败：%1").arg(fileName));
        }
    }
    else {
        // 文件不存在，弹出文件选择对话框
        ui->textBrowserLog->append(QString("未找到设备配置文件：%1，请手动选择").arg(fileName));
        
        QString selectedFile = QFileDialog::getOpenFileName(
            this,
            QString("选择%1设备配置文件").arg(deviceModel),
            QCoreApplication::applicationDirPath(),
            "CSV文件 (*.csv);;所有文件 (*.*)"
        );
        
        if (!selectedFile.isEmpty()) {
            if (loadParseItemsFromFile(selectedFile)) {
                updateParseTable();
                ui->textBrowserLog->append(QString("已加载设备配置文件：%1").arg(QFileInfo(selectedFile).fileName()));
                return true;
            }
            else {
                ui->textBrowserLog->append(QString("加载设备配置文件失败：%1").arg(QFileInfo(selectedFile).fileName()));
            }
        }
    }
    
    return false;
}

// 处理接收到的串口消息
void SetValueDialog::handleSerialMessageReceived(const QString &message)
{
    // 只显示报文收发数据和解析结果
    if (message.startsWith("TX:") || message.startsWith("RX:") || message.contains("解析结果")) {
        QDateTime currentTime = QDateTime::currentDateTime();
        QString logMessage = currentTime.toString("[hh:mm:ss.zzz] ") + message;
        ui->textBrowserLog->append(QString("<font color='#0000FF'>%1</font>").arg(logMessage));
    }
    
    // 解析响应数据
    if (message.startsWith("RX:")) {
        // 从消息中提取十六进制数据
        QString hexData = message.mid(4).trimmed(); // 跳过"RX: "
        QByteArray responseData;
        
        // 将十六进制字符串转换为字节数组
        QStringList hexBytes = hexData.split(' ', Qt::SkipEmptyParts);
        for (const QString &hexByte : hexBytes) {
            bool ok;
            quint8 byte = hexByte.toInt(&ok, 16);
            if (ok) {
                responseData.append(static_cast<char>(byte));
            }
        }
        
        // 解析响应
        if (!responseData.isEmpty()) {
            parseResponse(responseData);
        }
    }
}

void SetValueDialog::parseResponse(const QByteArray &response)
{
    // 检查响应数据长度是否合理
    if (response.size() < 5) { // 至少需要站号、功能码、数据长度和CRC校验
        // 不显示错误信息
        return;
    }
    
    // 提取功能码
    quint8 functionCode = static_cast<quint8>(response[1]);
    
    // 检查功能码是否有错误（Modbus错误响应的功能码会加0x80）
    if (functionCode & 0x80) {
        quint8 errorCode = static_cast<quint8>(response[2]);
        QString errorMsg;
        switch (errorCode) {
            case 0x01: errorMsg = "非法功能"; break;
            case 0x02: errorMsg = "非法数据地址"; break;
            case 0x03: errorMsg = "非法数据值"; break;
            case 0x04: errorMsg = "从站设备故障"; break;
            case 0x05: errorMsg = "确认"; break;
            case 0x06: errorMsg = "从站设备忙"; break;
            default: errorMsg = QString("未知错误(0x%1)").arg(errorCode, 2, 16, QChar('0')).toUpper();
        }
        // 不显示Modbus错误信息
        return;
    }
    
    // 根据功能码处理不同类型的响应
    switch (functionCode) {
        case 0x03: // 读保持寄存器
        case 0x04: // 读输入寄存器
            parseReadRegistersResponse(response);
            break;
        case 0x01: // 读线圈
        case 0x02: // 读离散输入
            parseReadCoilsResponse(response);
            break;
        case 0x05: // 写单个线圈
        case 0x06: // 写单个寄存器
            parseWriteSingleResponse(response);
            break;
        case 0x0F: // 写多个线圈
        case 0x10: // 写多个寄存器
            parseWriteMultipleResponse(response);
            break;
        default:
            // 不显示不支持功能码信息
            break;
    }
}

// 解析读寄存器响应
void SetValueDialog::parseReadRegistersResponse(const QByteArray &response)
{
    // 检查响应数据长度
    if (response.size() < 3) {
        // 不显示错误信息
        return;
    }
    
    // 提取数据字节数
    quint8 byteCount = static_cast<quint8>(response[2]);
    
    // 检查数据长度是否匹配
    if (response.size() < 3 + byteCount + 2) { // 站号(1) + 功能码(1) + 字节数(1) + 数据(n) + CRC(2)
        // 不显示错误信息
        return;
    }
    
    // 提取数据部分
    QByteArray data = response.mid(3, byteCount);
    
    // 保存寄存器数据供反算使用
    lastRegisterData = data;
    
    // 计算寄存器数量
    int registerCount = byteCount / 2; // 每个寄存器占2个字节
    
    // 解析每个寄存器的值
    QStringList registerValues;
    for (int i = 0; i < registerCount; i++) {
        // Modbus使用大端序（高字节在前）
        quint16 registerValue = ((static_cast<quint8>(data[i*2]) << 8) | static_cast<quint8>(data[i*2+1]));
        registerValues.append(QString("0x%1").arg(registerValue, 4, 16, QChar('0')).toUpper());
    }
    
    // 显示解析结果
    if (enableUILog) {
        ui->textBrowserLog->append(QString("<font color='#0000FF'>解析结果：读取到%1个寄存器，值为：%2</font>")
                                  .arg(registerCount)
                                  .arg(registerValues.join(", ")));
    }
    
    // 根据配置的解析项进行详细解析
    parseDataValues(data);
}

// 解析读线圈/离散输入响应
void SetValueDialog::parseReadCoilsResponse(const QByteArray &response)
{
    // 检查响应数据长度
    if (response.size() < 3) {
        // 不显示错误信息
        return;
    }
    
    // 提取数据字节数
    quint8 byteCount = static_cast<quint8>(response[2]);
    
    // 检查数据长度是否匹配
    if (response.size() < 3 + byteCount + 2) { // 站号(1) + 功能码(1) + 字节数(1) + 数据(n) + CRC(2)
        // 不显示错误信息
        return;
    }
    
    // 提取数据部分
    QByteArray data = response.mid(3, byteCount);
    
    // 解析每个位的状态
    QStringList bitValues;
    for (int i = 0; i < byteCount; i++) {
        quint8 byteValue = static_cast<quint8>(data[i]);
        for (int bit = 0; bit < 8; bit++) {
            bool bitState = (byteValue & (1 << bit)) != 0;
            bitValues.append(bitState ? "1" : "0");
        }
    }
    
    // 显示解析结果
    if (enableUILog) {
        ui->textBrowserLog->append(QString("<font color='#0000FF'>解析结果：读取到%1个位，状态为：%2</font>")
                                  .arg(bitValues.size())
                                  .arg(bitValues.join(", ")));
    }
}

// 解析写单个响应
void SetValueDialog::parseWriteSingleResponse(const QByteArray &response)
{
    // 检查响应数据长度
    if (response.size() < 8) { // 站号(1) + 功能码(1) + 地址(2) + 值(2) + CRC(2)
        // 不显示错误信息
        return;
    }
    
    // 提取地址和值
    quint16 address = ((static_cast<quint8>(response[2]) << 8) | static_cast<quint8>(response[3]));
    quint16 value = ((static_cast<quint8>(response[4]) << 8) | static_cast<quint8>(response[5]));
    
    // 显示解析结果
    ui->textBrowserLog->append(QString("<font color='#0000FF'>解析结果：成功写入地址0x%1，值为0x%2</font>")
                              .arg(address, 4, 16, QChar('0')).toUpper()
                              .arg(value, 4, 16, QChar('0')).toUpper());
}

// 解析写多个响应
void SetValueDialog::parseWriteMultipleResponse(const QByteArray &response)
{
    // 检查响应数据长度
    if (response.size() < 8) { // 站号(1) + 功能码(1) + 起始地址(2) + 数量(2) + CRC(2)
        // 不显示错误信息
        return;
    }
    
    // 提取起始地址和数量
    quint16 startAddress = ((static_cast<quint8>(response[2]) << 8) | static_cast<quint8>(response[3]));
    quint16 quantity = ((static_cast<quint8>(response[4]) << 8) | static_cast<quint8>(response[5]));
    
    // 显示解析结果
    ui->textBrowserLog->append(QString("<font color='#0000FF'>解析结果：成功写入%1个值，起始地址为0x%2</font>")
                              .arg(quantity)
                              .arg(startAddress, 4, 16, QChar('0')).toUpper());
}

// 根据配置的解析项解析数据值
void SetValueDialog::parseDataValues(const QByteArray &data)
{
    // 如果没有配置解析项，则不进行解析
    if (parseItems.isEmpty()) {
        // 不显示提示信息
        return;
    }
    
    // 解析结果列表
    QStringList parseResults;
    
    // 遍历所有解析项
    for (int i = 0; i < parseItems.size(); i++) {
        ParseItem &item = parseItems[i]; // 使用引用以便修改
        // 检查设备地址是否在数据范围内
        // 将Modbus地址转换为寄存器索引（40001对应寄存器0，40002对应寄存器1等）
        int registerIndex = 0;
        if (item.address.startsWith("4")) {
            // 保持寄存器地址（40001 -> 0, 40002 -> 1）
            registerIndex = item.address.toInt() - 40001;
        } else {
            // 如果不是40xxx格式，使用deviceAddress
            registerIndex = item.deviceAddress;
        }
        int byteOffset = registerIndex * 2; // 每个寄存器占2个字节
        if (byteOffset + 1 >= data.size()) {
            parseResults.append(QString("%1(%2): 地址超出范围").arg(item.name).arg(item.description));
            continue;
        }
        
        // 根据解析方式解析数据
        QString valueStr;
        
        if (item.parseMethod == "按位取") {
            // 按位取值
            int byteIndex = byteOffset + (item.bitOffset / 8);
            int bitIndex = item.bitOffset % 8;
            
            if (byteIndex < data.size()) {
                bool bitValue = (static_cast<quint8>(data[byteIndex]) & (1 << bitIndex)) != 0;
                valueStr = bitValue ? "1" : "0";
            } else {
                valueStr = "地址超出范围";
            }
        } else if (item.parseMethod == "2字节 无符号 先高后低") {
            // 2字节无符号大端序
            if (byteOffset + 1 < data.size()) {
                quint16 value = (static_cast<quint8>(data[byteOffset]) << 8) |
                               static_cast<quint8>(data[byteOffset+1]);
                valueStr = QString::number(value);
            } else {
                valueStr = "数据不足";
            }
        } else if (item.parseMethod == "2字节 无符号 先低后高") {
            // 2字节无符号小端序
            if (byteOffset + 1 < data.size()) {
                quint16 value = (static_cast<quint8>(data[byteOffset+1]) << 8) |
                               static_cast<quint8>(data[byteOffset]);
                valueStr = QString::number(value);
            } else {
                valueStr = "数据不足";
            }
        } else if (item.parseMethod == "2字节 有符号 先高后低") {
            // 2字节有符号大端序
            if (byteOffset + 1 < data.size()) {
                quint16 rawValue = (static_cast<quint8>(data[byteOffset]) << 8) |
                                  static_cast<quint8>(data[byteOffset+1]);
                qint16 value = static_cast<qint16>(rawValue);
                valueStr = QString::number(value);
            } else {
                valueStr = "数据不足";
            }
        } else if (item.parseMethod == "2字节 有符号 先低后高") {
            // 2字节有符号小端序
            if (byteOffset + 1 < data.size()) {
                quint16 rawValue = (static_cast<quint8>(data[byteOffset+1]) << 8) |
                                  static_cast<quint8>(data[byteOffset]);
                qint16 value = static_cast<qint16>(rawValue);
                valueStr = QString::number(value);
            } else {
                valueStr = "数据不足";
            }
        } else if (item.parseMethod == "2字节 无符号 取高字节") {
            // 2字节取高字节
            if (byteOffset + 1 < data.size()) {
                quint8 value = static_cast<quint8>(data[byteOffset]);
                valueStr = QString::number(value);
            } else {
                valueStr = "数据不足";
            }
        } else if (item.parseMethod == "2字节 无符号 取低字节") {
            // 2字节取低字节
            if (byteOffset + 1 < data.size()) {
                quint8 value = static_cast<quint8>(data[byteOffset+1]);
                valueStr = QString::number(value);
            } else {
                valueStr = "数据不足";
            }
        } else if (item.parseMethod == "2字节 有符号 取高字节") {
            // 2字节取高字节（有符号）
            if (byteOffset + 1 < data.size()) {
                qint8 value = static_cast<qint8>(data[byteOffset]);
                valueStr = QString::number(value);
            } else {
                valueStr = "数据不足";
            }
        } else if (item.parseMethod == "2字节 有符号 取低字节") {
            // 2字节取低字节（有符号）
            if (byteOffset + 1 < data.size()) {
                qint8 value = static_cast<qint8>(data[byteOffset+1]);
                valueStr = QString::number(value);
            } else {
                valueStr = "数据不足";
            }
        } else if (item.parseMethod == "4字节 无符号 顺序4321") {
            // 4字节无符号 字节顺序4321 (大端序)
            if (byteOffset + 3 < data.size()) {
                quint32 value = (static_cast<quint8>(data[byteOffset]) << 24) |
                               (static_cast<quint8>(data[byteOffset+1]) << 16) |
                               (static_cast<quint8>(data[byteOffset+2]) << 8) |
                               static_cast<quint8>(data[byteOffset+3]);
                valueStr = QString::number(value);
            } else {
                valueStr = "数据不足";
            }
        } else if (item.parseMethod == "4字节 无符号 顺序2143") {
            // 4字节无符号 字节顺序2143
            if (byteOffset + 3 < data.size()) {
                quint32 value = (static_cast<quint8>(data[byteOffset+1]) << 24) |
                               (static_cast<quint8>(data[byteOffset]) << 16) |
                               (static_cast<quint8>(data[byteOffset+3]) << 8) |
                               static_cast<quint8>(data[byteOffset+2]);
                valueStr = QString::number(value);
            } else {
                valueStr = "数据不足";
            }
        } else if (item.parseMethod == "4字节 无符号 顺序1234") {
            // 4字节无符号 字节顺序1234 (小端序)
            if (byteOffset + 3 < data.size()) {
                quint32 value = (static_cast<quint8>(data[byteOffset+3]) << 24) |
                               (static_cast<quint8>(data[byteOffset+2]) << 16) |
                               (static_cast<quint8>(data[byteOffset+1]) << 8) |
                               static_cast<quint8>(data[byteOffset]);
                valueStr = QString::number(value);
            } else {
                valueStr = "数据不足";
            }
        } else if (item.parseMethod == "4字节 无符号 顺序3412") {
            // 4字节无符号 字节顺序3412
            if (byteOffset + 3 < data.size()) {
                quint32 value = (static_cast<quint8>(data[byteOffset+2]) << 24) |
                               (static_cast<quint8>(data[byteOffset+3]) << 16) |
                               (static_cast<quint8>(data[byteOffset]) << 8) |
                               static_cast<quint8>(data[byteOffset+1]);
                valueStr = QString::number(value);
            } else {
                valueStr = "数据不足";
            }
        } else if (item.parseMethod == "4字节 有符号 顺序4321") {
            // 4字节有符号 字节顺序4321 (大端序)
            if (byteOffset + 3 < data.size()) {
                quint32 rawValue = (static_cast<quint8>(data[byteOffset]) << 24) |
                                  (static_cast<quint8>(data[byteOffset+1]) << 16) |
                                  (static_cast<quint8>(data[byteOffset+2]) << 8) |
                                  static_cast<quint8>(data[byteOffset+3]);
                qint32 value = static_cast<qint32>(rawValue);
                valueStr = QString::number(value);
            } else {
                valueStr = "数据不足";
            }
        } else if (item.parseMethod == "4字节 有符号 顺序2143") {
            // 4字节有符号 字节顺序2143
            if (byteOffset + 3 < data.size()) {
                quint32 rawValue = (static_cast<quint8>(data[byteOffset+1]) << 24) |
                                  (static_cast<quint8>(data[byteOffset]) << 16) |
                                  (static_cast<quint8>(data[byteOffset+3]) << 8) |
                                  static_cast<quint8>(data[byteOffset+2]);
                qint32 value = static_cast<qint32>(rawValue);
                valueStr = QString::number(value);
            } else {
                valueStr = "数据不足";
            }
        } else if (item.parseMethod == "4字节 有符号 顺序1234") {
            // 4字节有符号 字节顺序1234 (小端序)
            if (byteOffset + 3 < data.size()) {
                quint32 rawValue = (static_cast<quint8>(data[byteOffset+3]) << 24) |
                                  (static_cast<quint8>(data[byteOffset+2]) << 16) |
                                  (static_cast<quint8>(data[byteOffset+1]) << 8) |
                                  static_cast<quint8>(data[byteOffset]);
                qint32 value = static_cast<qint32>(rawValue);
                valueStr = QString::number(value);
            } else {
                valueStr = "数据不足";
            }
        } else if (item.parseMethod == "4字节 有符号 顺序3412") {
            // 4字节有符号 字节顺序3412
            if (byteOffset + 3 < data.size()) {
                quint32 rawValue = (static_cast<quint8>(data[byteOffset+2]) << 24) |
                                  (static_cast<quint8>(data[byteOffset+3]) << 16) |
                                  (static_cast<quint8>(data[byteOffset]) << 8) |
                                  static_cast<quint8>(data[byteOffset+1]);
                qint32 value = static_cast<qint32>(rawValue);
                valueStr = QString::number(value);
            } else {
                valueStr = "数据不足";
            }
        } else {
            // 未知解析方式
            valueStr = "未知解析方式";
        }
        
        // 检查是否需要BCD解析（保留原有BCD功能）
        if (item.bcdParse && valueStr != "数据不足" && valueStr != "地址超出范围" && valueStr != "未知解析方式") {
            bool ok;
            quint32 rawValue = valueStr.toUInt(&ok);
            if (ok) {
                // BCD解析
                qint64 decimalValue = 0;
                qint64 multiplier = 1;
                quint32 tempValue = rawValue;
                while (tempValue > 0) {
                    int digit = tempValue & 0xF;
                    if (digit > 9) {
                        // 无效的BCD数字
                        valueStr = "无效BCD";
                        break;
                    }
                    decimalValue += digit * multiplier;
                    multiplier *= 10;
                    tempValue >>= 4;
                }
                if (tempValue == 0) {
                    valueStr = QString::number(decimalValue);
                }
            }
        }

        
        // 根据处理方式对实际数据进行处理
        double processedValue = 0.0;
        if (valueStr != "数据不足" && valueStr != "地址超出范围") {
            double actualValue = valueStr.toDouble();
            
            if (item.processMethod == "除以处理参数" && item.processParam != 0) {
                processedValue = actualValue / item.processParam;
            } else if (item.processMethod == "乘以处理参数") {
                processedValue = actualValue * item.processParam;
            } else if (item.processMethod == "自定义比例" && item.processParam != 0) {
                processedValue = actualValue * item.processParam;
            } else if (item.processMethod == "D1(智能除法)") {
                // D1智能除法：根据40001地址的数据值决定除法规则
                double referenceValue = 1.0; // 默认值
                int referenceCategory = 1; // 默认分类
                
                // 查找40001地址的解析项数据
                for (const ParseItem &refItem : parseItems) {
                    if (refItem.address == "40001" && !refItem.actualData.isEmpty() && 
                        refItem.actualData != "数据不足" && refItem.actualData != "地址超出范围") {
                        referenceValue = refItem.actualData.toDouble();
                        
                        // 根据40001地址（InP）的显示数据值范围确定分类
                        if (referenceValue < 10) {
                            referenceCategory = 1;
                        } else if (referenceValue >= 10 && referenceValue < 800) {
                            referenceCategory = 2;
                        } else {
                            referenceCategory = 3;
                        }
                        break;
                    }
                }
                
                // 根据分类决定除法规则
                if (referenceCategory == 1) {
                    processedValue = actualValue / 100.0;
                } else if (referenceCategory == 2) {
                    processedValue = actualValue / 10.0;
                } else {
                    processedValue = actualValue; // 直接转换
                }
            } else if (item.processMethod == "DM1(智能除法)") {
                // DM1智能除法：根据40001地址（一次CT额定电流1）的数据值决定除法规则
                double referenceValue = 1.0; // 默认值
                int referenceCategory = 1; // 默认分类
                
                // 查找40001地址的解析项数据
                for (const ParseItem &refItem : parseItems) {
                    if (refItem.address == "40001" && !refItem.actualData.isEmpty() && 
                        refItem.actualData != "数据不足" && refItem.actualData != "地址超出范围") {
                        referenceValue = refItem.actualData.toDouble();
                        
                        // 根据40001地址（InP1）的显示数据值范围确定分类
                        if (referenceValue < 10) {
                            referenceCategory = 1;
                        } else {
                            referenceCategory = 2;
                        }
                        break;
                    }
                }
                
                // 根据分类决定除法规则
                if (referenceCategory == 1) {
                    processedValue = actualValue / 100.0;
                } else {
                    processedValue = actualValue / 10.0;
                }
            } else if (item.processMethod == "DM2(智能除法)") {
                // DM2智能除法：根据40002地址（一次CT额定电流2）的数据值决定除法规则
                double referenceValue = 1.0; // 默认值
                int referenceCategory = 1; // 默认分类
                
                // 查找40002地址的解析项数据
                for (const ParseItem &refItem : parseItems) {
                    if (refItem.address == "40002" && !refItem.actualData.isEmpty() && 
                        refItem.actualData != "数据不足" && refItem.actualData != "地址超出范围") {
                        referenceValue = refItem.actualData.toDouble();
                        
                        // 根据40002地址（InP2）的显示数据值范围确定分类
                        if (referenceValue < 10) {
                            referenceCategory = 1;
                        } else {
                            referenceCategory = 2;
                        }
                        break;
                    }
                }
                
                // 根据分类决定除法规则
                if (referenceCategory == 1) {
                    processedValue = actualValue / 100.0;
                } else {
                    processedValue = actualValue / 10.0;
                }
            } else {
                // "原始数据"情况下不做处理，保持原值
                processedValue = actualValue;
            }
            
            // 更新实际数据
            item.actualData = QString::number(processedValue, 'f', 3);
        } else {
            item.actualData = valueStr;
        }
        
        // 根据投退选择和实际数据设置保护投退状态
        if (!item.enableProtection) {
            // 如果投退选择未被选中，显示---
            item.protectionStatus = "---";
        } else if (valueStr != "数据不足" && valueStr != "地址超出范围") {
            // 如果投退选择被选中，根据实际数据值设置状态
            int actualIntValue = static_cast<int>(processedValue);
            if (actualIntValue == 0) {
                item.protectionStatus = "OFF";
            } else if (actualIntValue == 1) {
                item.protectionStatus = "TRIP";
            } else if (actualIntValue == 2) {
                item.protectionStatus = "ALARM";
            } else {
                item.protectionStatus = "未知";
            }
        } else {
            item.protectionStatus = "---";
        }
        
        // 根据期望数据和处理后的实际数据计算比对结果
        if (!item.expectedData.isEmpty() && !item.actualData.isEmpty()) {
            if (valueStr != "数据不足" && valueStr != "地址超出范围") {
                double expectedValue = item.expectedData.toDouble();
                if (qAbs(processedValue - expectedValue) < 0.01) { // 允许0.01的误差
                    item.compareResult = "✓";
                } else {
                    item.compareResult = "✗";
                }
            } else {
                item.compareResult = "✗";
            }
        } else {
            item.compareResult = "";
        }
        
        // 添加到解析结果列表
        parseResults.append(QString("%1(%2): 寄存器：0X%3；实际数据：%4")
                           .arg(item.name)
                           .arg(item.description)
                           .arg(item.address.toUpper())
                           .arg(valueStr));
    }
    
    // 使用增量更新优化性能，只更新实际发生变化的行
    QSet<int> changedRows;
    for (int i = 0; i < parseItems.size(); ++i) {
        // 检查actualData、protectionStatus、compareResult字段是否发生变化
        // 由于这些字段在上面的循环中都可能被修改，我们需要更新所有行
        changedRows.insert(i);
    }
    
    // 分批更新，避免UI卡死
    if (!changedRows.isEmpty()) {
        QList<int> sortedRows = changedRows.values();
        std::sort(sortedRows.begin(), sortedRows.end());
        
        const int batchSize = 20;
        for (int i = 0; i < sortedRows.size(); i += batchSize) {
            int endIndex = qMin(i + batchSize, sortedRows.size());
            for (int j = i; j < endIndex; ++j) {
                updateSingleRow(sortedRows[j]);
            }
            
            // 在批次间处理事件，保持UI响应
            if (endIndex < sortedRows.size()) {
                QCoreApplication::processEvents();
            }
        }
        
        qDebug() << QString("Modbus数据解析完成，增量更新了%1行").arg(changedRows.size());
    }
    
    // 显示解析结果
    if (enableUILog) {
        ui->textBrowserLog->append(QString("<font color='#0000FF'>详细解析结果：</font>"));
        for (const QString &result : parseResults) {
            ui->textBrowserLog->append(QString("<font color='#0000FF'>  %1</font>").arg(result));
        }
    }
}

// 处理表格项变更
void SetValueDialog::onTableItemChanged(QTableWidgetItem *item)
{
    if (!item) return;
    
    int row = item->row();
    int column = item->column();
    
    // 检查行索引是否有效
    if (row < 0 || row >= parseItems.size()) {
        return;
    }
    
    bool needSave = false;
    
    // 检查是否是特性选择列（第16列）
    if (column == 16) {
        // 更新parseItems中的featureSelection字段
        parseItems[row].featureSelection = (item->checkState() == Qt::Checked);
        needSave = true;
    }
    // 检查是否是投退选择列（第17列）
    else if (column == 17) {
        // 更新parseItems中的enableProtection字段
        parseItems[row].enableProtection = (item->checkState() == Qt::Checked);
        needSave = true;
        
        // 重新计算保护投退状态
        ParseItem &parseItem = parseItems[row];
        if (!parseItem.enableProtection) {
            parseItem.protectionStatus = "---";
        } else if (!parseItem.actualData.isEmpty()) {
            // 根据实际数据重新计算保护投退状态
            bool ok;
            double actualValue = parseItem.actualData.toDouble(&ok);
            if (ok) {
                int actualIntValue = static_cast<int>(actualValue);
                if (actualIntValue == 0) {
                    parseItem.protectionStatus = "OFF";
                } else if (actualIntValue == 1) {
                    parseItem.protectionStatus = "TRIP";
                } else if (actualIntValue == 2) {
                    parseItem.protectionStatus = "ALARM";
                } else {
                    parseItem.protectionStatus = "未知";
                }
            } else {
                parseItem.protectionStatus = "---";
            }
        }
        
        // 更新保护投退列的显示
        QTableWidgetItem *protectionItem = ui->tableWidgetParse->item(row, 18);
        if (protectionItem) {
            protectionItem->setText(parseItem.protectionStatus);
        }
        
        // 自动保存解析项配置到默认文件
        saveParseItemsToFile("parse_items.csv");
    }
    // 检查是否是期望数据列（第19列）
    else if (column == 19) {
        // 更新parseItems中的expectedData字段
        parseItems[row].expectedData = item->text().trimmed();
        
        // 重新计算比对结果
        ParseItem &parseItem = parseItems[row];
        if (parseItem.expectedData.isEmpty() || parseItem.actualData.isEmpty()) {
            parseItem.compareResult = "";
        } else {
            // 比较期望数据和实际数据
            bool expectedOk, actualOk;
            double expectedValue = parseItem.expectedData.toDouble(&expectedOk);
            double actualValue = parseItem.actualData.toDouble(&actualOk);
            
            if (expectedOk && actualOk) {
                // 数值比较（允许小的误差）
                const double tolerance = 0.0001;
                if (qAbs(expectedValue - actualValue) < tolerance) {
                    parseItem.compareResult = "✔";
                } else {
                    parseItem.compareResult = "×";
                }
            } else {
                // 字符串比较
                if (parseItem.expectedData == parseItem.actualData) {
                    parseItem.compareResult = "✔";
                } else {
                    parseItem.compareResult = "×";
                }
            }
        }
        
        // 更新比对结果列的显示
        QTableWidgetItem *compareItem = ui->tableWidgetParse->item(row, 22);
        if (compareItem) {
            compareItem->setText(parseItem.compareResult);
            // 根据比对结果设置颜色
            if (parseItem.compareResult == "✔") {
                compareItem->setForeground(QBrush(QColor(0, 128, 0))); // 绿色
            } else if (parseItem.compareResult == "×") {
                compareItem->setForeground(QBrush(QColor(255, 0, 0))); // 红色
            }
        }
        
        needSave = true;
    }
    // 检查是否是期望数据(定值文件读取)列（第20列）
    else if (column == 20) {
        // 更新parseItems中的expectedDataFromFile字段
        parseItems[row].expectedDataFromFile = item->text().trimmed();
        needSave = true;
    }
    
    // 使用延迟保存机制，减少频繁IO操作
    if (needSave) {
        pendingSave = true;
        saveTimer->start(); // 重新启动定时器
    }
}

// 生成HTML格式的测试报告
QString SetValueDialog::generateHtmlReport()
{
    // 获取解析项列表
    QList<ParseItem> items = parseItems;
    
    // 获取当前配置信息
    QString portName = ui->comboBoxPort->currentText();
    int deviceAddress = ui->spinBoxDeviceAddress->value();
    QString functionCode = ui->comboBoxFunctionCode->currentText();
    QString dataAddress = ui->lineEditDataAddress->text();
    int dataCount = ui->spinBoxDataCount->value();
    bool useCRC16 = ui->checkBoxCRC16->isChecked();
    
    // 生成HTML报告
    QString html = R"(
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>JcSoft定值召唤报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2 { color: #2c3e50; }
        .header { text-align: center; margin-bottom: 30px; }
        .section { margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .config-info { background-color: #e2e3e5; padding: 10px; border-radius: 5px; }
        .footer { margin-top: 30px; text-align: center; font-size: 0.8em; color: #6c757d; }
    </style>
</head>
<body>
    <div class="header">
        <h1>JcSoft定值召唤报告</h1>
        <p>生成时间: )" + QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss") + R"(</p>
    </div>
    
    <div class="section">
        <h2>通信配置信息</h2>
        <div class="config-info">
            <p><strong>通信串口：</strong>)" + portName + R"(</p>
            <p><strong>设备地址：</strong>)" + QString::number(deviceAddress) + R"(</p>
            <p><strong>功能码：</strong>)" + functionCode + R"(</p>
            <p><strong>数据地址：</strong>)" + dataAddress + R"(</p>
            <p><strong>数据个数：</strong>)" + QString::number(dataCount) + R"(</p>
            <p><strong>CRC16校验：</strong>)" + (useCRC16 ? "是" : "否") + R"(</p>
        </div>
    </div>
    
    <div class="section">
        <h2>解析项配置</h2>
        <table>
            <tr>
                <th>序号</th>
                <th>名称</th>
                <th>描述</th>
                <th>数据类型</th>
                <th>地址</th>
                <th>解析方式</th>
                <th>单位</th>
                <th>投退选择</th>
                <th>期望数据</th>
                <th>显示数据</th>
                <th>比对结果</th>
            </tr>)";
    
    // 添加解析项数据
    for (int i = 0; i < items.size(); ++i) {
        const ParseItem &item = items[i];
        html += R"(
            <tr>
                <td>)" + QString::number(i + 1) + R"(</td>
                <td>)" + item.name + R"(</td>
                <td>)" + item.description + R"(</td>
                <td>)" + item.dataType + R"(</td>
                <td>)" + item.address + R"(</td>
                <td>)" + item.parseMethod + R"(</td>
                <td>)" + item.unit + R"(</td>
                <td>)" + (item.enableProtection ? "启用" : "禁用") + R"(</td>
                <td>)" + item.expectedData + R"(</td>
                <td>)" + item.actualData + R"(</td>
                <td>)" + item.compareResult + R"(</td>
            </tr>)";
    }
    
    html += R"(
        </table>
    </div>
    
    <div class="section">
        <h2>通信日志</h2>
        <pre>)" + ui->textBrowserLog->toPlainText() + R"(</pre>
    </div>
    
    <div class="footer">
        <p>JcSoft - 定值召唤测试工具 &copy; )" + QDateTime::currentDateTime().toString("yyyy") + R"(</p>
    </div>
</body>
</html>
)";
    
    return html;
}

// 保存HTML报告到文件
bool SetValueDialog::saveReportToHtml(const QString &htmlContent, const QString &filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qDebug() << "无法打开文件进行写入:" << filePath;
        return false;
    }
    
    QTextStream stream(&file);
#if QT_VERSION >= QT_VERSION_CHECK(6, 0, 0)
    stream.setEncoding(QStringConverter::Utf8);
#else
    stream.setCodec("UTF-8");
#endif
    stream << htmlContent;
    file.close();
    
    return true;
}

// 保存HTML报告为PDF文件
bool SetValueDialog::saveReportToPdf(const QString &htmlContent, const QString &filePath)
{
    // 创建临时HTML文件
    QTemporaryFile tempFile;
    if (!tempFile.open()) {
        qDebug() << "无法创建临时文件";
        return false;
    }
    
    // 写入HTML内容到临时文件
    QTextStream stream(&tempFile);
#if QT_VERSION >= QT_VERSION_CHECK(6, 0, 0)
    stream.setEncoding(QStringConverter::Utf8);
#else
    stream.setCodec("UTF-8");
#endif
    stream << htmlContent;
    stream.flush();
    
    // 创建打印机和打印对话框
    QPrinter printer(QPrinter::HighResolution);
    printer.setOutputFormat(QPrinter::PdfFormat);
    printer.setOutputFileName(filePath);
    printer.setPageSize(QPageSize(QPageSize::A4));
    
    // 使用QTextDocument加载HTML并打印到PDF
    QTextDocument document;
    document.setHtml(htmlContent);
    document.print(&printer);
    
    return true;
}

// 写入定值按钮点击事件
void SetValueDialog::on_pushButtonWriteValue_clicked()
{
    // 检查是否有选中的串口
    if (!currentSerial) {
        QMessageBox::warning(this, "警告", "请先选择通信串口！");
        return;
    }
    
    // 检查串口是否已连接
    if (!currentSerial || !currentSerial->isConnected()) {
        QMessageBox::warning(this, "警告", "串口未连接，请先连接串口！");
        return;
    }
    
    // 检查是否有解析项配置
    if (parseItems.isEmpty()) {
        QMessageBox::warning(this, "警告", "请先配置解析项！");
        return;
    }
    
    // 第一步：自动执行召唤数据操作
    ui->textBrowserLog->append("<font color='#FF8000'>开始写入定值操作，首先执行召唤数据...</font>");
    on_pushButtonSend_clicked();
    
    // 等待一段时间让召唤数据完成
    QTimer::singleShot(2000, this, [this]() {
        // 继续执行原有的写入定值逻辑
        this->performWriteValueOperation();
    });
}

// 执行写入定值操作的具体逻辑
void SetValueDialog::performWriteValueOperation()
{
    
    // 统计比对不一致的项目
    QList<ParseItem> inconsistentItems;
    for (const ParseItem &item : parseItems) {
        // 只要期望数据不为空且比对结果不一致，就添加到不一致列表
        if (!item.expectedData.isEmpty()) {
            // 使用比对结果字段判断是否一致（与表格显示逻辑保持一致）
            // 包含所有可能的不一致标记：✗、×、x、X
            if (item.compareResult == "✗" || item.compareResult == "×" || 
                item.compareResult == "x" || item.compareResult == "X") {
                inconsistentItems.append(item);
                // qDebug() << "Found inconsistent item:" << item.description << "compareResult:" << item.compareResult;
            }
        }
    }
    
    if (inconsistentItems.isEmpty()) {
        // qDebug() << "No inconsistent items found. Total parseItems:" << parseItems.size();
        // for (const ParseItem &item : parseItems) {
        //     qDebug() << "Item:" << item.description << "expectedData:" << item.expectedData << "compareResult:" << item.compareResult;
        // }
        QMessageBox::information(this, "提示", "没有发现比对不一致的定值项！\n\n所有启用的定值项的期望数据与实际数据都一致。");
        return;
    }
    
    // 生成反算数据和指令预览
    // qDebug() << "Found" << inconsistentItems.size() << "inconsistent items, starting to generate preview...";
    QString itemList;
    QString reverseCalculationDetails;
    QString modbusCommandPreview;
    int deviceAddress = ui->spinBoxDeviceAddress->value();
    
    // 准备写入数据映射
    QMap<quint16, QPair<quint16, QString>> addressValueMap;
    
    for (const ParseItem &item : inconsistentItems) {
        // qDebug() << "Processing item:" << item.description << "expectedData:" << item.expectedData << "address:" << item.address;
        // qDebug() << "  parseMethod:" << item.parseMethod << "processMethod:" << item.processMethod << "processParam:" << item.processParam;
        // qDebug() << "  actualData:" << item.actualData << "compareResult:" << item.compareResult;
        
        // 解析期望数据
        bool ok;
        double expectedValue = item.expectedData.toDouble(&ok);
        if (!ok) {
            // qDebug() << "Failed to parse expectedData:" << item.expectedData << "for item:" << item.description;
            // 即使解析失败也要记录到反算详情中
            reverseCalculationDetails += QString("• %1 (地址:%2)\n  错误: 无法解析期望数据 '%3'\n\n")
                                       .arg(item.description)
                                       .arg(item.address)
                                       .arg(item.expectedData);
            continue;
        }
        
        // 解析地址 - 先尝试十进制，再尝试十六进制
        bool addressOk;
        quint16 address = item.address.toUShort(&addressOk, 10);  // 先尝试十进制
        if (!addressOk) {
            address = item.address.toUShort(&addressOk, 16);  // 再尝试十六进制
        }
        if (!addressOk) {
            // qDebug() << "Failed to parse address:" << item.address << "for item:" << item.description;
            // 即使解析失败也要记录到反算详情中
            reverseCalculationDetails += QString("• %1 (地址:%2)\n  错误: 无法解析地址 '%3' (尝试了十进制和十六进制)\n\n")
                                       .arg(item.description)
                                       .arg(item.address)
                                       .arg(item.address);
            continue;
        }
        
        // qDebug() << "Successfully parsed - expectedValue:" << expectedValue << "address:" << address;
        
        // 解析实际数据以获取原始16位值
        bool actualOk;
        double actualValue = item.actualData.toDouble(&actualOk);
        quint16 originalValue = 0;
        
        if (actualOk && item.processParam != 0) {
            originalValue = static_cast<quint16>(actualValue * item.processParam);
        }
        
        // 根据解析方式和处理方式确定反算逻辑
        quint16 writeValue = 0;
        QString reverseFormula;
        
        if (item.parseMethod.contains("取低字节") && item.processMethod == "除以处理参数") {
            // 从保存的寄存器数据中获取真实的16位寄存器值
            quint16 currentRegisterValue = 0;
            
            // 计算寄存器在数据中的位置
            // 读取时起始地址为0x0000，所以40001对应索引0，40002对应索引1，40003对应索引2
            int registerIndex = address - 40001; // 40001对应索引0
            int byteOffset = registerIndex * 2;
            
            if (!lastRegisterData.isEmpty() && byteOffset + 1 < lastRegisterData.size()) {
                // 从保存的数据中获取真实的16位寄存器值
                currentRegisterValue = (static_cast<quint8>(lastRegisterData[byteOffset]) << 8) |
                                     static_cast<quint8>(lastRegisterData[byteOffset + 1]);
            } else {
                // 如果没有保存的数据，使用计算值作为备选
                currentRegisterValue = originalValue;
            }
            
            quint8 highByte = (currentRegisterValue >> 8) & 0xFF;
            quint8 newLowByte = static_cast<quint8>(expectedValue * item.processParam);
            writeValue = (static_cast<quint16>(highByte) << 8) | newLowByte;
            reverseFormula = QString("显示数据: %1, 实际数据: %2, 发送数据: 0x%3\n高字节保持0x%4, 低字节=%1×%5=%2(0x%3), 组合=0x%6")
                           .arg(expectedValue, 0, 'f', 4)
                           .arg(static_cast<int>(expectedValue * item.processParam))
                           .arg(newLowByte, 2, 16, QChar('0')).toUpper()
                           .arg(highByte, 2, 16, QChar('0')).toUpper()
                           .arg(item.processParam, 0, 'f', 4)
                           .arg(writeValue, 4, 16, QChar('0')).toUpper();
        } else if (item.parseMethod.contains("取高字节") && item.processMethod == "除以处理参数") {
            // 从保存的寄存器数据中获取真实的16位寄存器值
            quint16 currentRegisterValue = 0;
            
            // 计算寄存器在数据中的位置
            // 读取时起始地址为0x0000，所以40001对应索引0，40002对应索引1，40003对应索引2
            int registerIndex = address - 40001; // 40001对应索引0
            int byteOffset = registerIndex * 2;
            
            if (!lastRegisterData.isEmpty() && byteOffset + 1 < lastRegisterData.size()) {
                // 从保存的数据中获取真实的16位寄存器值
                currentRegisterValue = (static_cast<quint8>(lastRegisterData[byteOffset]) << 8) |
                                     static_cast<quint8>(lastRegisterData[byteOffset + 1]);
            } else {
                // 如果没有保存的数据，使用计算值作为备选
                currentRegisterValue = originalValue;
            }
            
            quint8 lowByte = currentRegisterValue & 0xFF;
            quint8 newHighByte = static_cast<quint8>(expectedValue * item.processParam);
            writeValue = (static_cast<quint16>(newHighByte) << 8) | lowByte;
            reverseFormula = QString("显示数据: %1, 实际数据: %2, 发送数据: 0x%3\n低字节保持0x%4, 高字节=%1×%5=%2(0x%3), 组合=0x%6")
                           .arg(expectedValue, 0, 'f', 4)
                           .arg(static_cast<int>(expectedValue * item.processParam))
                           .arg(newHighByte, 2, 16, QChar('0')).toUpper()
                           .arg(lowByte, 2, 16, QChar('0')).toUpper()
                           .arg(item.processParam, 0, 'f', 4)
                           .arg(writeValue, 4, 16, QChar('0')).toUpper();
        }
        else if (item.processMethod == "除以处理参数") {
            writeValue = static_cast<quint16>(expectedValue * item.processParam);
            reverseFormula = QString("%1×%2=%3")
                           .arg(expectedValue, 0, 'f', 4)
                           .arg(item.processParam, 0, 'f', 4)
                           .arg(writeValue);
        }
        else if (item.processMethod == "乘以处理参数") {
            writeValue = static_cast<quint16>(expectedValue / item.processParam);
            reverseFormula = QString("%1÷%2=%3")
                           .arg(expectedValue, 0, 'f', 4)
                           .arg(item.processParam, 0, 'f', 4)
                           .arg(writeValue);
        }
        else {
            writeValue = static_cast<quint16>(expectedValue);
            reverseFormula = QString("直接使用期望值=%1").arg(expectedValue, 0, 'f', 4);
        }
        
        // 构建项目列表
        if (enableUILog) {
            itemList += QString("• %1: 实际值=%2, 期望值=%3\n")
                       .arg(item.name)
                       .arg(item.actualData)
                       .arg(item.expectedData);
        }
        
        // 构建反算详情
        reverseCalculationDetails += QString("• %1 (地址:0x%2)\n  反算公式: %3\n  下发数据: %4 (0x%5)\n\n")
                                   .arg(item.name)
                                   .arg(item.address.toUpper())
                                   .arg(reverseFormula)
                                   .arg(writeValue)
                                   .arg(writeValue, 4, 16, QChar('0')).toUpper();
        
        addressValueMap[address] = qMakePair(writeValue, item.name);
    }
    
    // 生成Modbus指令预览
    if (!addressValueMap.isEmpty()) {
        quint16 startAddress = 0x0000;
        
        QList<quint16> allRegisterValues;
        
        // 首先从保存的寄存器数据中恢复所有寄存器的原始值
        if (!lastRegisterData.isEmpty()) {
            int dataRegisterCount = lastRegisterData.size() / 2;
            for (int i = 0; i < dataRegisterCount; ++i) {
                // 从保存的数据中获取原始寄存器值
                quint16 originalValue = (static_cast<quint8>(lastRegisterData[i*2]) << 8) |
                                      static_cast<quint8>(lastRegisterData[i*2 + 1]);
                allRegisterValues.append(originalValue);
            }
        } else {
            // 如果没有保存的数据，无法生成正确的指令
            // ui->textBrowserLog->append("<font color='#FF0000'>错误：没有保存的寄存器数据，无法生成Modbus指令</font>");
            return;
        }
        
        // 然后只修改需要变更的寄存器
        int actualRegisterCount = allRegisterValues.size();
        for (auto it = addressValueMap.begin(); it != addressValueMap.end(); ++it) {
            quint16 address = it.key();
            quint16 value = it.value().first;
            
            // 地址映射修正：40001对应索引0，40002对应索引1，40003对应索引2
            int index = address - 40001;
            if (index >= 0 && index < actualRegisterCount) {
                allRegisterValues[index] = value;
            }
        }
        
        QByteArray frame = createWriteValueFrame(deviceAddress, 0x10, QString("%1").arg(startAddress, 4, 16, QChar('0')), allRegisterValues);
        
        if (!frame.isEmpty()) {
            QString hexString;
            for (int j = 0; j < frame.size(); ++j) {
                hexString += QString("%1 ").arg(static_cast<quint8>(frame[j]), 2, 16, QChar('0')).toUpper();
            }
            modbusCommandPreview = hexString.trimmed();
        }
    }
    
    // 构建详细的寄存器数据对比信息（调试模式下显示）
    QString registerCompareDetails;
    // registerCompareDetails += QString("\n========== 寄存器数据对比分析 ==========\n");
    // registerCompareDetails += QString("addressValueMap大小: %1\n").arg(addressValueMap.size());
    
    // if (!addressValueMap.isEmpty()) {
    //     registerCompareDetails += "需要修改的寄存器详细对比:\n";
    //     
    //     for (auto it = addressValueMap.begin(); it != addressValueMap.end(); ++it) {
    //         quint16 address = it.key();
    //         quint16 newValue = it.value().first;
    //         QString description = it.value().second;
    //         
    //         registerCompareDetails += QString("调试: address=%1, newValue=%2\n").arg(address).arg(newValue);
    //         
    //         // address是十进制值，需要转换为寄存器索引
    //         // 设备地址从40001开始，Modbus从40000开始，需要减1进行映射
    //         // 设备地址40003对应Modbus地址40002，registerIndex=2
    //         int registerIndex = address - 40001;
    //         registerCompareDetails += QString("调试: registerIndex=%1, 条件判断=%2\n")
    //                                  .arg(registerIndex)
    //                                  .arg((registerIndex >= 0 && registerIndex < 52) ? "通过" : "失败");
    //         
    //         if (registerIndex >= 0 && registerIndex < 52) {
    //             int index = registerIndex;
    //             quint16 originalValue = 0;
    //             
    //             // 获取原始寄存器值
    //             if (!lastRegisterData.isEmpty()) {
    //                 // 40003对应字节偏移6 (registerIndex=3, 3*2=6)
    //                 // 但实际数据中40003应该对应偏移6，40004对应偏移8
    //                 // 根据报文分析，需要调整偏移计算
    //                 int byteOffset = registerIndex * 2;
    //                 registerCompareDetails += QString("调试: byteOffset=%1, lastRegisterData.size()=%2\n")
    //                                          .arg(byteOffset).arg(lastRegisterData.size());
    //                 if (byteOffset + 1 < lastRegisterData.size()) {
    //                     originalValue = (static_cast<quint8>(lastRegisterData[byteOffset]) << 8) |
    //                                   static_cast<quint8>(lastRegisterData[byteOffset + 1]);
    //                     registerCompareDetails += QString("调试: 读取字节[%1]=%2, 字节[%3]=%4, 组合值=%5\n")
    //                                              .arg(byteOffset).arg(static_cast<quint8>(lastRegisterData[byteOffset]), 2, 16, QChar('0'))
    //                                              .arg(byteOffset+1).arg(static_cast<quint8>(lastRegisterData[byteOffset+1]), 2, 16, QChar('0'))
    //                                              .arg(originalValue, 4, 16, QChar('0'));
    //                 }
    //             }
    //             
    //             // 显示寄存器地址，直接使用原始地址
    //             quint16 displayAddress = address;
    //             
    //             registerCompareDetails += QString("  寄存器地址 %1:\n")
    //                                      .arg(displayAddress);
    //             registerCompareDetails += QString("    原始数据: %1 (0x%2)\n")
    //                                      .arg(originalValue)
    //                                      .arg(originalValue, 4, 16, QChar('0')).toUpper();
    //             registerCompareDetails += QString("    反算数据: %1 (0x%2)\n")
    //                                      .arg(newValue)
    //                                      .arg(newValue, 4, 16, QChar('0')).toUpper();
    //             registerCompareDetails += QString("    变化情况: %1\n")
    //                                      .arg(originalValue == newValue ? "无变化" : "已修改");
    //             registerCompareDetails += QString("    描述信息: %1\n\n")
    //                                      .arg(description);
    //         }
    //     }
    //     
    //     // 添加报文参数信息
    //     registerCompareDetails += "========== 准备下发报文 ==========\n";
    //     registerCompareDetails += "报文参数:\n";
    //     registerCompareDetails += QString("  设备地址: %1 (0x%2)\n")
    //                              .arg(deviceAddress)
    //                              .arg(deviceAddress, 2, 16, QChar('0')).toUpper();
    //     registerCompareDetails += "  功能码: 16 (0x10) - 写多个寄存器\n";
    //     registerCompareDetails += QString("  起始地址: %1 (0x%2)\n")
    //                              .arg(0)
    //                              .arg(0, 4, 16, QChar('0')).toUpper();
    //     
    //     if (!lastRegisterData.isEmpty()) {
    //         int registerCount = lastRegisterData.size() / 2;
    //         registerCompareDetails += QString("  寄存器数量: %1 (0x%2)\n")
    //                                  .arg(registerCount)
    //                                  .arg(registerCount, 4, 16, QChar('0')).toUpper();
    //         registerCompareDetails += QString("  数据字节数: %1\n\n")
    //                                  .arg(registerCount * 2);
    //     }
    // }
    
    // 构建完整的确认弹窗内容
    QString confirmMessage;
    if (enableUILog) {
        confirmMessage = QString("发现 %1 个比对不一致的定值项：\n\n%2\n")
                               .arg(inconsistentItems.size())
                               .arg(itemList);
    } else {
        confirmMessage = QString("发现 %1 个比对不一致的定值项。\n\n")
                               .arg(inconsistentItems.size());
    }
    
    // 屏蔽预览反算结果和预览下发指令
    // if (!reverseCalculationDetails.isEmpty()) {
    //     confirmMessage += QString("\n========== 预览反算结果 ==========\n%1").arg(reverseCalculationDetails);
    // }
    
    if (!registerCompareDetails.isEmpty()) {
        confirmMessage += registerCompareDetails;
    }
    
    // if (!modbusCommandPreview.isEmpty()) {
    //     confirmMessage += QString("========== 预览下发指令 ==========\n%1\n\n").arg(modbusCommandPreview);
    // }
    
    // confirmMessage += QString("========== 调试信息 ==========\n找到不一致项目数量: %1\n反算详情长度: %2\nModbus指令长度: %3\n\n").arg(inconsistentItems.size()).arg(reverseCalculationDetails.length()).arg(modbusCommandPreview.length());
    
    confirmMessage += "确定要将这些项目的期望数据写入到设备中吗？\n\n注意：此操作将修改设备的定值参数，请确保数据正确！";
    
    // 确认写入操作
    int ret = QMessageBox::question(this, "确认写入定值", 
                                   confirmMessage,
                                   QMessageBox::Yes | QMessageBox::No,
                                   QMessageBox::No);
    
    if (ret == QMessageBox::Yes) {
        writeInconsistentValuesToDevice(inconsistentItems);
    }
}

// 清空日志按钮点击事件
void SetValueDialog::on_pushButtonClearLog_clicked()
{
    // 确认清空操作
    int ret = QMessageBox::question(this, "确认清空", 
                                   "确定要清空所有通信日志吗？",
                                   QMessageBox::Yes | QMessageBox::No,
                                   QMessageBox::No);
    
    if (ret == QMessageBox::Yes) {
        ui->textBrowserLog->clear();
        ui->textBrowserLog->append(QString("[%1] 日志已清空")
                                  .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss")));
    }
}

// 写入定值到设备
void SetValueDialog::writeValueToDevice()
{
    int deviceAddress = ui->spinBoxDeviceAddress->value();
    int delay = ui->spinBoxDelay->value();
    
    // 统计需要写入的项目
    QList<ParseItem> writeItems;
    for (const ParseItem &item : parseItems) {
        if (item.enableProtection && !item.expectedData.isEmpty()) {
            writeItems.append(item);
        }
    }
    
    if (writeItems.isEmpty()) {
        QMessageBox::information(this, "提示", "没有需要写入的数据项！\n\n请确保：\n1. 解析项已启用投退选择\n2. 期望数据不为空");
        return;
    }
    
    // 不显示开始写入信息
    
    // 逐个写入定值项
    for (int i = 0; i < writeItems.size(); ++i) {
        const ParseItem &item = writeItems[i];
        
        // 解析期望数据
        bool ok;
        double expectedValue = item.expectedData.toDouble(&ok);
        if (!ok) {
            // 不显示错误信息
            continue;
        }
        
        // 解析地址
        bool addressOk;
        quint16 address = item.address.toUShort(&addressOk, 10);  // 先尝试十进制
        if (!addressOk) {
            address = item.address.toUShort(&addressOk, 16);  // 再尝试十六进制
        }
        if (!addressOk) {
            // 不显示错误信息
            continue;
        }
        
        // 根据解析方式和数据处理参数调整值
        quint16 writeValue = 0;
        
        // 第一步：根据处理方式进行逆运算，得到原始16进制数据
        double reversedValue = expectedValue;
        
        if (item.processMethod == "除以处理参数") {
            reversedValue = expectedValue * item.processParam;
        }
        else if (item.processMethod == "乘以处理参数") {
            reversedValue = expectedValue / item.processParam;
        }
        else if (item.processMethod == "D1智能除法(InP显示值=...)") {
            // D1智能除法的逆运算：显示值 * 处理参数
            reversedValue = expectedValue * item.processParam;
        }
        // 原始数据不需要处理，保持原值
        
        // 第二步：根据解析方式确定如何放置数据
        if (item.parseMethod.contains("取低字节")) {
            // 取低字节：需要保持高字节不变，将逆运算结果作为新的低字节
            
            // 从保存的寄存器数据中获取真实的16位寄存器值
            quint16 currentRegisterValue = 0;
            
            // 计算寄存器在数据中的位置
            // 读取时起始地址为0x0000，对应40001，所以40005对应索引4
            int registerIndex = address - 40001; // 40001对应索引0，40005对应索引4
            int byteOffset = registerIndex * 2;
            
            if (!lastRegisterData.isEmpty() && byteOffset + 1 < lastRegisterData.size()) {
                // 从保存的数据中获取真实的16位寄存器值
                currentRegisterValue = (static_cast<quint8>(lastRegisterData[byteOffset]) << 8) |
                                     static_cast<quint8>(lastRegisterData[byteOffset + 1]);
                
                // 调试输出：显示地址计算和数据读取过程（仅在调试模式下）
                if (enableDebugLog) {
                    ui->textBrowserLog->append(QString("<font color='#008000'>调试信息 - 取低字节处理：</font>"));
                    ui->textBrowserLog->append(QString("  地址: %1, 索引: %2, 字节偏移: %3").arg(address).arg(registerIndex).arg(byteOffset));
                    ui->textBrowserLog->append(QString("  原始寄存器值: 0x%1 (高字节:0x%2, 低字节:0x%3)")
                                              .arg(currentRegisterValue, 4, 16, QChar('0')).toUpper()
                                              .arg((currentRegisterValue >> 8) & 0xFF, 2, 16, QChar('0')).toUpper()
                                              .arg(currentRegisterValue & 0xFF, 2, 16, QChar('0')).toUpper());
                    ui->textBrowserLog->append(QString("  期望数据: %1, 逆运算结果: %2").arg(expectedValue).arg(reversedValue));
                }
            }
            
            quint8 highByte = (currentRegisterValue >> 8) & 0xFF;  // 保持当前高字节
            quint8 newLowByte = static_cast<quint8>(static_cast<int>(reversedValue) & 0xFF);
            writeValue = (static_cast<quint16>(highByte) << 8) | newLowByte;
            
            // 调试输出：显示最终写入值（仅在调试模式下）
            if (enableDebugLog) {
                ui->textBrowserLog->append(QString("  保持高字节: 0x%1, 新低字节: 0x%2")
                                          .arg(highByte, 2, 16, QChar('0')).toUpper()
                                          .arg(newLowByte, 2, 16, QChar('0')).toUpper());
                ui->textBrowserLog->append(QString("  最终写入值: 0x%1").arg(writeValue, 4, 16, QChar('0')).toUpper());
            }
        }
        else if (item.parseMethod.contains("取高字节")) {
            // 取高字节：需要保持低字节不变，将逆运算结果作为新的高字节
            
            // 从保存的寄存器数据中获取真实的16位寄存器值
            quint16 currentRegisterValue = 0;
            
            // 计算寄存器在数据中的位置
            // 读取时起始地址为0x0000，对应40001，所以40005对应索引4
            int registerIndex = address - 40001; // 40001对应索引0，40005对应索引4
            int byteOffset = registerIndex * 2;
            
            if (!lastRegisterData.isEmpty() && byteOffset + 1 < lastRegisterData.size()) {
                // 从保存的数据中获取真实的16位寄存器值
                currentRegisterValue = (static_cast<quint8>(lastRegisterData[byteOffset]) << 8) |
                                     static_cast<quint8>(lastRegisterData[byteOffset + 1]);
                
                // 调试输出：显示地址计算和数据读取过程（仅在调试模式下）
                if (enableDebugLog) {
                    ui->textBrowserLog->append(QString("<font color='#008000'>调试信息 - 取高字节处理：</font>"));
                    ui->textBrowserLog->append(QString("  地址: %1, 索引: %2, 字节偏移: %3").arg(address).arg(registerIndex).arg(byteOffset));
                    ui->textBrowserLog->append(QString("  原始寄存器值: 0x%1 (高字节:0x%2, 低字节:0x%3)")
                                              .arg(currentRegisterValue, 4, 16, QChar('0')).toUpper()
                                              .arg((currentRegisterValue >> 8) & 0xFF, 2, 16, QChar('0')).toUpper()
                                              .arg(currentRegisterValue & 0xFF, 2, 16, QChar('0')).toUpper());
                    ui->textBrowserLog->append(QString("  期望数据: %1, 逆运算结果: %2").arg(expectedValue).arg(reversedValue));
                }
            }
            
            quint8 lowByte = currentRegisterValue & 0xFF;  // 保持当前低字节
            quint8 newHighByte = static_cast<quint8>(static_cast<int>(reversedValue) & 0xFF);
            writeValue = (static_cast<quint16>(newHighByte) << 8) | lowByte;
            
            // 调试输出：显示最终写入值（仅在调试模式下）
            if (enableDebugLog) {
                ui->textBrowserLog->append(QString("  新高字节: 0x%1, 保持低字节: 0x%2")
                                          .arg(newHighByte, 2, 16, QChar('0')).toUpper()
                                          .arg(lowByte, 2, 16, QChar('0')).toUpper());
                ui->textBrowserLog->append(QString("  最终写入值: 0x%1").arg(writeValue, 4, 16, QChar('0')).toUpper());
            }
        }
        else {
            // 完整16位数据：直接使用逆运算结果
            writeValue = static_cast<quint16>(reversedValue);
        }
        
        // 创建写入帧（使用功能码06 - 写单个寄存器）
        QList<quint16> values;
        values.append(writeValue);
        QByteArray frame = createWriteValueFrame(deviceAddress, 0x06, QString("%1").arg(address, 4, 16, QChar('0')), values);
        
        if (frame.isEmpty()) {
            // 不显示错误信息
            continue;
        }
        
        // 发送写入命令
        currentSerial->sendRawData(frame);
        
        // 记录发送的数据
        QString hexString;
        for (int j = 0; j < frame.size(); ++j) {
            hexString += QString("%1 ").arg(static_cast<quint8>(frame[j]), 2, 16, QChar('0')).toUpper();
        }
        
        QDateTime currentTime = QDateTime::currentDateTime();
    QString logMessage = currentTime.toString("[hh:mm:ss.zzz] ") + QString("TX: %1").arg(hexString.trimmed());
    ui->textBrowserLog->append(QString("<font color='#0000FF'>%1</font>").arg(logMessage));
        // 不显示写入详情
        
        // 延时
        if (i < writeItems.size() - 1) {
            QThread::msleep(delay);
        }
    }
    
    // 写入完成后，延时执行第二次召唤数据操作
    ui->textBrowserLog->append("<font color='#FF8000'>写入定值完成，正在执行召唤数据刷新显示值...</font>");
    QTimer::singleShot(1000, this, [this]() {
        this->on_pushButtonSend_clicked();
        ui->textBrowserLog->append("<font color='#008000'>定值写入操作全部完成！</font>");
    });
}

// 写入比对不一致的定值到设备（使用功能码0x10）
void SetValueDialog::writeInconsistentValuesToDevice(const QList<ParseItem> &inconsistentItems)
{
    int deviceAddress = ui->spinBoxDeviceAddress->value();
    
    if (inconsistentItems.isEmpty()) {
        return;
    }
    
    // 准备写入数据，按地址分组处理
    QMap<quint16, QList<ParseItem>> addressItemsMap; // 地址 -> 项目列表
    QMap<quint16, QPair<quint16, QString>> addressValueMap; // 地址 -> (值, 描述)
    
    // ui->textBrowserLog->append(QString("<font color='#0080FF'>========== 开始处理比对不一致的定值项 ==========</font>"));
    
    // 第一步：按地址分组
    for (const ParseItem &item : inconsistentItems) {
        // 解析地址
        bool addressOk;
        quint16 address = item.address.toUShort(&addressOk, 10);  // 先尝试十进制
        if (!addressOk) {
            address = item.address.toUShort(&addressOk, 16);  // 再尝试十六进制
        }
        if (!addressOk) {
            continue;
        }
        
        addressItemsMap[address].append(item);
    }
    
    // 第二步：处理每个地址的所有项目
    for (auto addrIt = addressItemsMap.begin(); addrIt != addressItemsMap.end(); ++addrIt) {
        quint16 address = addrIt.key();
        QList<ParseItem> items = addrIt.value();
        
        // 获取原始16位寄存器数据
        quint16 originalValue = 0;
        if (!lastRegisterData.isEmpty()) {
            int registerIndex = address - 40001;  // 地址映射修正
            if (registerIndex >= 0 && registerIndex < 52) {
                int byteOffset = registerIndex * 2;
                if (byteOffset + 1 < lastRegisterData.size()) {
                    originalValue = (static_cast<quint8>(lastRegisterData[byteOffset]) << 8) |
                                  static_cast<quint8>(lastRegisterData[byteOffset + 1]);
                }
            }
        }
        
        // 初始化最终值为原始值
        quint16 finalValue = originalValue;
        QStringList descriptions;
        
        // 处理该地址的所有项目
        for (const ParseItem &item : items) {
            // 解析期望数据
            bool ok;
            double expectedValue = item.expectedData.toDouble(&ok);
            if (!ok) {
                continue;
            }
            
            // 根据处理方式进行正确的反算
            QString reverseFormula;
        
            // 根据解析方式和处理方式确定反算逻辑
            if (item.parseMethod.contains("取低字节") && item.processMethod == "除以处理参数") {
                // 对于"取低字节除以参数"的处理方式
                // 保持高字节不变，将期望值乘以处理参数作为新的低字节
                quint8 highByte = (finalValue >> 8) & 0xFF;  // 保持当前高字节
                quint8 newLowByte = static_cast<quint8>(expectedValue * item.processParam);
                finalValue = (static_cast<quint16>(highByte) << 8) | newLowByte;
                reverseFormula = QString("高字节保持0x%1, 低字节=%2×%3=0x%4, 组合=0x%5")
                               .arg(highByte, 2, 16, QChar('0')).toUpper()
                               .arg(expectedValue, 0, 'f', 4)
                               .arg(item.processParam, 0, 'f', 4)
                               .arg(newLowByte, 2, 16, QChar('0')).toUpper()
                               .arg(finalValue, 4, 16, QChar('0')).toUpper();
            }
            else if (item.parseMethod.contains("取高字节") && item.processMethod == "除以处理参数") {
                // 对于"取高字节除以参数"的处理方式
                // 保持低字节不变，将期望值乘以处理参数作为新的高字节
                quint8 lowByte = finalValue & 0xFF;  // 保持当前低字节
                quint8 newHighByte = static_cast<quint8>(expectedValue * item.processParam);
                finalValue = (static_cast<quint16>(newHighByte) << 8) | lowByte;
                reverseFormula = QString("低字节保持0x%1, 高字节=%2×%3=0x%4, 组合=0x%5")
                               .arg(lowByte, 2, 16, QChar('0')).toUpper()
                               .arg(expectedValue, 0, 'f', 4)
                               .arg(item.processParam, 0, 'f', 4)
                               .arg(newHighByte, 2, 16, QChar('0')).toUpper()
                               .arg(finalValue, 4, 16, QChar('0')).toUpper();
            }
            else if (item.processMethod == "除以处理参数") {
                // 对于整个16位数据除以参数的处理方式
                finalValue = static_cast<quint16>(expectedValue * item.processParam);
                reverseFormula = QString("%1×%2=%3")
                               .arg(expectedValue, 0, 'f', 4)
                               .arg(item.processParam, 0, 'f', 4)
                               .arg(finalValue);
            }
            else if (item.processMethod == "乘以处理参数") {
                // 对于乘以处理参数的处理方式
                finalValue = static_cast<quint16>(expectedValue / item.processParam);
                reverseFormula = QString("%1÷%2=%3")
                               .arg(expectedValue, 0, 'f', 4)
                               .arg(item.processParam, 0, 'f', 4)
                               .arg(finalValue);
            }
            else if (item.processMethod == "D1(智能除法)") {
                // D1智能除法的反算：根据InP项的显示数据值决定反算规则
                double referenceValue = 1.0; // 默认值
                int referenceCategory = 1; // 默认分类
                bool foundInP = false;
                
                // 调试输出：开始查找InP项（仅在调试模式下）
                if (enableDebugLog) {
                    qDebug() << "[DEBUG] 开始D1智能除法反算，当前项:" << item.description << "期望值:" << expectedValue;
                }
                
                // 查找InP项的期望数据（expectedData，即CSV表格中的期望数据）
                for (const ParseItem &refItem : parseItems) {
                    if (enableDebugLog) {
                        qDebug() << "[DEBUG] 检查项目:" << refItem.name << "expectedData:" << refItem.expectedData;
                    }
                    if (refItem.name == "CT一次额定电流（InP）" && !refItem.expectedData.isEmpty()) {
                        referenceValue = refItem.expectedData.toDouble();
                        foundInP = true;
                        
                        // 根据InP项的期望数据值范围确定分类
                        if (referenceValue < 10) {
                            referenceCategory = 1;
                        } else if (referenceValue >= 10 && referenceValue < 800) {
                            referenceCategory = 2;
                        } else {
                            referenceCategory = 3;
                        }
                        if (enableDebugLog) {
                            qDebug() << "[DEBUG] 找到InP项，referenceValue:" << referenceValue << "分类:" << referenceCategory;
                        }
                        break;
                    }
                }
                
                if (!foundInP && enableDebugLog) {
                    qDebug() << "[DEBUG] 未找到InP项，使用默认分类:" << referenceCategory;
                }
                
                // 根据分类决定反算规则
                if (referenceCategory == 1) {
                    // 除以100的反算：乘以100
                    double tempResult = expectedValue * 100.0;
                    finalValue = static_cast<quint16>(tempResult);
                    if (enableDebugLog) {
                        qDebug() << "[DEBUG] 分类1计算: " << expectedValue << "×100=" << tempResult << "转换为quint16=" << finalValue;
                    }
                    reverseFormula = QString("D1智能除法(InP期望值=%1): %2×100=%3")
                                   .arg(referenceValue, 0, 'f', 4)
                                   .arg(expectedValue, 0, 'f', 4)
                                   .arg(finalValue);
                } else if (referenceCategory == 2) {
                    // 除以10的反算：乘以10
                    double tempResult = expectedValue * 10.0;
                    finalValue = static_cast<quint16>(tempResult);
                    if (enableDebugLog) {
                        qDebug() << "[DEBUG] 分类2计算: " << expectedValue << "×10=" << tempResult << "转换为quint16=" << finalValue;
                    }
                    reverseFormula = QString("D1智能除法(InP期望值=%1): %2×10=%3")
                                   .arg(referenceValue, 0, 'f', 4)
                                   .arg(expectedValue, 0, 'f', 4)
                                   .arg(finalValue);
                } else {
                    // 直接转换的反算：直接使用期望值
                    finalValue = static_cast<quint16>(expectedValue);
                    if (enableDebugLog) {
                        qDebug() << "[DEBUG] 分类3计算: 直接使用" << expectedValue << "转换为quint16=" << finalValue;
                    }
                    reverseFormula = QString("D1智能除法(InP期望值=%1): 直接使用=%2")
                                   .arg(referenceValue, 0, 'f', 4)
                                   .arg(finalValue);
                }
                if (enableDebugLog) {
                    qDebug() << "[DEBUG] 最终反算结果: finalValue=" << finalValue << "公式:" << reverseFormula;
                }
            }
            else if (item.processMethod == "DM1(智能除法)") {
                // DM1智能除法的反算：根据InP1项的期望数据值决定反算规则
                double referenceValue = 1.0; // 默认值
                int referenceCategory = 1; // 默认分类
                bool foundInP1 = false;
                
                // 调试输出：开始查找InP1项（仅在调试模式下）
                if (enableDebugLog) {
                    qDebug() << "[DEBUG] 开始DM1智能除法反算，当前项:" << item.description << "期望值:" << expectedValue;
                }
                
                // 查找一次CT额定电流1项的期望数据（expectedData，即CSV表格中的期望数据）
                for (const ParseItem &refItem : parseItems) {
                    if (enableDebugLog) {
                        qDebug() << "[DEBUG] 检查项目:" << refItem.name << "expectedData:" << refItem.expectedData;
                    }
                    if (refItem.name == "一次CT额定电流1" && !refItem.expectedData.isEmpty()) {
                        referenceValue = refItem.expectedData.toDouble();
                        foundInP1 = true;
                        
                        // 根据InP1项的期望数据值范围确定分类
                        if (referenceValue < 10) {
                            referenceCategory = 1;
                        } else {
                            referenceCategory = 2;
                        }
                        if (enableDebugLog) {
                            qDebug() << "[DEBUG] 找到InP1项，referenceValue:" << referenceValue << "分类:" << referenceCategory;
                        }
                        break;
                    }
                }
                
                if (!foundInP1 && enableDebugLog) {
                    qDebug() << "[DEBUG] 未找到InP1项，使用默认分类:" << referenceCategory;
                }
                
                // 根据分类决定反算规则
                if (referenceCategory == 1) {
                    // 除以100的反算：乘以100
                    double tempResult = expectedValue * 100.0;
                    finalValue = static_cast<quint16>(tempResult);
                    if (enableDebugLog) {
                        qDebug() << "[DEBUG] DM1分类1计算: " << expectedValue << "×100=" << tempResult << "转换为quint16=" << finalValue;
                    }
                    reverseFormula = QString("DM1智能除法(InP1期望值=%1): %2×100=%3")
                                   .arg(referenceValue, 0, 'f', 4)
                                   .arg(expectedValue, 0, 'f', 4)
                                   .arg(finalValue);
                } else {
                    // 除以10的反算：乘以10
                    double tempResult = expectedValue * 10.0;
                    finalValue = static_cast<quint16>(tempResult);
                    if (enableDebugLog) {
                        qDebug() << "[DEBUG] DM1分类2计算: " << expectedValue << "×10=" << tempResult << "转换为quint16=" << finalValue;
                    }
                    reverseFormula = QString("DM1智能除法(InP1期望值=%1): %2×10=%3")
                                   .arg(referenceValue, 0, 'f', 4)
                                   .arg(expectedValue, 0, 'f', 4)
                                   .arg(finalValue);
                }
                if (enableDebugLog) {
                    qDebug() << "[DEBUG] DM1最终反算结果: finalValue=" << finalValue << "公式:" << reverseFormula;
                }
            }
            else if (item.processMethod == "DM2(智能除法)") {
                // DM2智能除法的反算：根据InP2项的期望数据值决定反算规则
                double referenceValue = 1.0; // 默认值
                int referenceCategory = 1; // 默认分类
                bool foundInP2 = false;
                
                // 调试输出：开始查找InP2项（仅在调试模式下）
                if (enableDebugLog) {
                    qDebug() << "[DEBUG] 开始DM2智能除法反算，当前项:" << item.description << "期望值:" << expectedValue;
                }
                
                // 查找一次CT额定电流2项的期望数据（expectedData，即CSV表格中的期望数据）
                for (const ParseItem &refItem : parseItems) {
                    if (enableDebugLog) {
                        qDebug() << "[DEBUG] 检查项目:" << refItem.name << "expectedData:" << refItem.expectedData;
                    }
                    if (refItem.name == "一次CT额定电流2" && !refItem.expectedData.isEmpty()) {
                        referenceValue = refItem.expectedData.toDouble();
                        foundInP2 = true;
                        
                        // 根据InP2项的期望数据值范围确定分类
                        if (referenceValue < 10) {
                            referenceCategory = 1;
                        } else {
                            referenceCategory = 2;
                        }
                        if (enableDebugLog) {
                            qDebug() << "[DEBUG] 找到InP2项，referenceValue:" << referenceValue << "分类:" << referenceCategory;
                        }
                        break;
                    }
                }
                
                if (!foundInP2 && enableDebugLog) {
                    qDebug() << "[DEBUG] 未找到InP2项，使用默认分类:" << referenceCategory;
                }
                
                // 根据分类决定反算规则
                if (referenceCategory == 1) {
                    // 除以100的反算：乘以100
                    double tempResult = expectedValue * 100.0;
                    finalValue = static_cast<quint16>(tempResult);
                    if (enableDebugLog) {
                        qDebug() << "[DEBUG] DM2分类1计算: " << expectedValue << "×100=" << tempResult << "转换为quint16=" << finalValue;
                    }
                    reverseFormula = QString("DM2智能除法(InP2期望值=%1): %2×100=%3")
                                   .arg(referenceValue, 0, 'f', 4)
                                   .arg(expectedValue, 0, 'f', 4)
                                   .arg(finalValue);
                } else {
                    // 除以10的反算：乘以10
                    double tempResult = expectedValue * 10.0;
                    finalValue = static_cast<quint16>(tempResult);
                    if (enableDebugLog) {
                        qDebug() << "[DEBUG] DM2分类2计算: " << expectedValue << "×10=" << tempResult << "转换为quint16=" << finalValue;
                    }
                    reverseFormula = QString("DM2智能除法(InP2期望值=%1): %2×10=%3")
                                   .arg(referenceValue, 0, 'f', 4)
                                   .arg(expectedValue, 0, 'f', 4)
                                   .arg(finalValue);
                }
                if (enableDebugLog) {
                    qDebug() << "[DEBUG] DM2最终反算结果: finalValue=" << finalValue << "公式:" << reverseFormula;
                }
            }
            else if (item.parseMethod.contains("取高字节")) {
                // 对于"取高字节"且处理方式为"原始数据"的情况
                // 保持低字节不变，将期望值作为新的高字节
                quint8 lowByte = finalValue & 0xFF;  // 保持当前低字节
                quint8 newHighByte = static_cast<quint8>(expectedValue);
                finalValue = (static_cast<quint16>(newHighByte) << 8) | lowByte;
                reverseFormula = QString("低字节保持0x%1, 高字节=0x%2, 组合=0x%3")
                               .arg(lowByte, 2, 16, QChar('0')).toUpper()
                               .arg(newHighByte, 2, 16, QChar('0')).toUpper()
                               .arg(finalValue, 4, 16, QChar('0')).toUpper();
            }
            else if (item.parseMethod.contains("取低字节")) {
                // 对于"取低字节"且处理方式为"原始数据"的情况
                // 保持高字节不变，将期望值作为新的低字节
                quint8 highByte = (finalValue >> 8) & 0xFF;  // 保持当前高字节
                quint8 newLowByte = static_cast<quint8>(expectedValue);
                finalValue = (static_cast<quint16>(highByte) << 8) | newLowByte;
                reverseFormula = QString("高字节保持0x%1, 低字节=0x%2, 组合=0x%3")
                               .arg(highByte, 2, 16, QChar('0')).toUpper()
                               .arg(newLowByte, 2, 16, QChar('0')).toUpper()
                               .arg(finalValue, 4, 16, QChar('0')).toUpper();
            }
            else {
                // 默认处理方式：原始数据，直接使用期望值（针对整个16位数据）
                finalValue = static_cast<quint16>(expectedValue);
                reverseFormula = QString("直接使用期望值=%1")
                               .arg(expectedValue, 0, 'f', 4);
            }
            
            descriptions.append(QString("%1(期望:%2->%3)")
                              .arg(item.description)
                              .arg(item.expectedData)
                              .arg(reverseFormula));
        }
        
        // 将最终计算结果存入地址映射
        QString description = QString("%1(地址:%2,原始:0x%3->最终:0x%4)")
                             .arg(descriptions.join("; "))
                             .arg(address)
                             .arg(originalValue, 4, 16, QChar('0')).toUpper()
                             .arg(finalValue, 4, 16, QChar('0')).toUpper();
        
        addressValueMap[address] = qMakePair(finalValue, description);
    }

    
    if (addressValueMap.isEmpty()) {
        QMessageBox::warning(this, "错误", "没有有效的写入数据！");
        return;
    }
    
    // 使用功能码0x10写多个寄存器
    // 起始地址固定为0x0000，寄存器个数根据实际读取的数据确定
    quint16 startAddress = 0x0000;
    quint16 registerCount = 0;  // 动态确定寄存器数量
    
    // 创建寄存器数据数组，使用从设备读取的实际数据作为基础
    QList<quint16> allRegisterValues;
    if (!lastRegisterData.isEmpty()) {
        // 根据实际读取的数据确定寄存器数量
        registerCount = lastRegisterData.size() / 2;
        // 从lastRegisterData中恢复所有寄存器的原始值
        for (int i = 0; i < registerCount; ++i) {
            int byteOffset = i * 2;
            if (byteOffset + 1 < lastRegisterData.size()) {
                quint16 registerValue = (static_cast<quint8>(lastRegisterData[byteOffset]) << 8) |
                                      static_cast<quint8>(lastRegisterData[byteOffset + 1]);
                allRegisterValues.append(registerValue);
            } else {
                allRegisterValues.append(0);
            }
        }
    } else {
        // 如果没有读取数据，无法确定寄存器数量，返回错误
        QMessageBox::warning(this, "错误", "没有读取到设备数据，无法确定寄存器数量！");
        return;
    }
    
    // 将写入值放入对应的寄存器位置
    QStringList itemDescriptions;
    for (auto it = addressValueMap.begin(); it != addressValueMap.end(); ++it) {
        quint16 address = it.key();
        quint16 value = it.value().first;
        QString description = it.value().second;
        
        // 检查地址是否在有效范围内（40001-40052）
        if (address >= 40001 && address <= 40001 + registerCount - 1) {
            int index = address - 40001; // 40001对应索引0
            allRegisterValues[index] = value;
            itemDescriptions.append(description);
        } else {
            // ui->textBrowserLog->append(QString("<font color='#FF0000'>警告: 地址 %1 超出写入范围 [40001-%2]</font>")
            //                          .arg(address)
            //                          .arg(40001 + registerCount - 1));
        }
    }
    
    if (itemDescriptions.isEmpty()) {
        QMessageBox::warning(this, "错误", "所有地址都超出写入范围！");
        return;
    }
    
    // 显示关键的寄存器数据对比信息（简化版）
    if (enableUILog) {
        ui->textBrowserLog->append(QString("<font color='#0080FF'>========== 寄存器数据对比分析 ==========</font>"));
        
        int modifiedCount = 0;
        // 统计修改的寄存器数量
        for (auto it = addressValueMap.begin(); it != addressValueMap.end(); ++it) {
            quint16 address = it.key();
            quint16 newValue = it.value().first;
            
            if (address >= 40001 && address <= 40001 + registerCount - 1) {
                int index = address - 40001;
                quint16 originalValue = 0;
                
                // 获取原始寄存器值
                if (!lastRegisterData.isEmpty()) {
                    int byteOffset = index * 2;
                    if (byteOffset + 1 < lastRegisterData.size()) {
                        originalValue = (static_cast<quint8>(lastRegisterData[byteOffset]) << 8) |
                                      static_cast<quint8>(lastRegisterData[byteOffset + 1]);
                    }
                }
                
                if (originalValue != newValue) {
                    modifiedCount++;
                    // 只显示有变化的寄存器的关键信息
                    ui->textBrowserLog->append(QString("寄存器%1: %2→%3 (%4)")
                                             .arg(address)
                                             .arg(originalValue)
                                             .arg(newValue)
                                             .arg(it.value().second));
                }
            }
        }
        
        if (modifiedCount == 0) {
            ui->textBrowserLog->append("<font color='#008000'>所有寄存器数据无需修改</font>");
        } else {
            ui->textBrowserLog->append(QString("<font color='#FF8000'>共需修改 %1 个寄存器</font>").arg(modifiedCount));
        }
    }
    
    // 详细调试信息（仅在调试模式下显示）
    if (enableDebugLog) {
        ui->textBrowserLog->append(QString("<font color='#800080'>详细寄存器对比:</font>"));
        for (auto it = addressValueMap.begin(); it != addressValueMap.end(); ++it) {
            quint16 address = it.key();
            quint16 newValue = it.value().first;
            QString description = it.value().second;
            
            if (address >= 40001 && address <= 40001 + registerCount - 1) {
                int index = address - 40001;
                quint16 originalValue = 0;
                
                // 获取原始寄存器值
                if (!lastRegisterData.isEmpty()) {
                    int byteOffset = index * 2;
                    if (byteOffset + 1 < lastRegisterData.size()) {
                        originalValue = (static_cast<quint8>(lastRegisterData[byteOffset]) << 8) |
                                      static_cast<quint8>(lastRegisterData[byteOffset + 1]);
                    }
                }
                
                ui->textBrowserLog->append(QString("  地址%1: 原始=%2(0x%3) 新值=%4(0x%5) %6 [%7]")
                                         .arg(address)
                                         .arg(originalValue)
                                         .arg(originalValue, 4, 16, QChar('0')).toUpper()
                                         .arg(newValue)
                                         .arg(newValue, 4, 16, QChar('0')).toUpper()
                                         .arg(originalValue == newValue ? "无变化" : "已修改")
                                         .arg(description));
            }
        }
    }
    
    // 显示即将写入的寄存器数据详情
    // 关键报文信息（简化版）
    if (enableUILog) {
        ui->textBrowserLog->append(QString("<font color='#0080FF'>========== 准备下发报文 ==========</font>"));
        ui->textBrowserLog->append(QString("设备地址:%1 功能码:0x10 起始地址:%2 寄存器数:%3")
                                 .arg(deviceAddress)
                                 .arg(startAddress)
                                 .arg(registerCount));
    }
    
    // 详细报文参数（仅在调试模式下）
    if (enableDebugLog) {
        ui->textBrowserLog->append(QString("<font color='#800080'>详细报文参数:</font>"));
        ui->textBrowserLog->append(QString("  设备地址: %1 (0x%2)")
                                 .arg(deviceAddress)
                                 .arg(deviceAddress, 2, 16, QChar('0')).toUpper());
        ui->textBrowserLog->append(QString("  功能码: 16 (0x10) - 写多个寄存器"));
        ui->textBrowserLog->append(QString("  起始地址: %1 (0x%2)")
                                 .arg(startAddress)
                                 .arg(startAddress, 4, 16, QChar('0')).toUpper());
        ui->textBrowserLog->append(QString("  寄存器数量: %1 (0x%2)")
                                 .arg(registerCount)
                                 .arg(registerCount, 4, 16, QChar('0')).toUpper());
        ui->textBrowserLog->append(QString("  数据字节数: %1")
                                 .arg(registerCount * 2));
        ui->textBrowserLog->append("");
    }
    
    // 显示所有寄存器的详细信息（包括未修改的）
    // ui->textBrowserLog->append(QString("<font color='#800080'>完整寄存器数据详情:</font>"));
    // for (int i = 0; i < allRegisterValues.size(); ++i) {
    //     quint16 regAddress = i + 1; // 地址从1开始
    //     bool isModified = addressValueMap.contains(regAddress);
    //     QString status = isModified ? "<font color='#FF0000'>[已修改]</font>" : "[原始]";
    //     
    //     ui->textBrowserLog->append(QString("  寄存器[%1] 地址:%2 值:%3 (0x%4) %5")
    //                              .arg(i)
    //                              .arg(regAddress)
    //                              .arg(allRegisterValues[i])
    //                              .arg(allRegisterValues[i], 4, 16, QChar('0')).toUpper()
    //                              .arg(status));
    // }
    // ui->textBrowserLog->append("");
    
    // 创建写入帧（使用功能码0x10 - 写多个寄存器）
    QByteArray frame = createWriteValueFrame(deviceAddress, 0x10, QString("%1").arg(startAddress, 4, 16, QChar('0')), allRegisterValues);
    
    if (frame.isEmpty()) {
        QMessageBox::warning(this, "错误", "创建写入帧失败！");
        return;
    }
    
    // 详细分析报文内容（仅在调试模式下）
    if (enableDebugLog) {
        ui->textBrowserLog->append(QString("<font color='#800080'>报文内容分析:</font>"));
        if (frame.size() >= 9) {
            ui->textBrowserLog->append(QString("  设备地址: 0x%1")
                                     .arg(static_cast<quint8>(frame[0]), 2, 16, QChar('0')).toUpper());
            ui->textBrowserLog->append(QString("  功能码: 0x%1")
                                     .arg(static_cast<quint8>(frame[1]), 2, 16, QChar('0')).toUpper());
            ui->textBrowserLog->append(QString("  起始地址: 0x%1%2")
                                     .arg(static_cast<quint8>(frame[2]), 2, 16, QChar('0'))
                                     .arg(static_cast<quint8>(frame[3]), 2, 16, QChar('0')).toUpper());
            ui->textBrowserLog->append(QString("  寄存器数量: 0x%1%2")
                                     .arg(static_cast<quint8>(frame[4]), 2, 16, QChar('0'))
                                     .arg(static_cast<quint8>(frame[5]), 2, 16, QChar('0')).toUpper());
            ui->textBrowserLog->append(QString("  字节数: 0x%1")
                                     .arg(static_cast<quint8>(frame[6]), 2, 16, QChar('0')).toUpper());
            ui->textBrowserLog->append(QString("  数据长度: %1 字节")
                                     .arg(frame.size() - 7 - (ui->checkBoxCRC16->isChecked() ? 2 : 0)));
            if (ui->checkBoxCRC16->isChecked() && frame.size() >= 2) {
                ui->textBrowserLog->append(QString("  CRC16: 0x%1%2")
                                         .arg(static_cast<quint8>(frame[frame.size()-1]), 2, 16, QChar('0'))
                                         .arg(static_cast<quint8>(frame[frame.size()-2]), 2, 16, QChar('0')).toUpper());
            }
        }
    }
    ui->textBrowserLog->append("");
    
    // 发送写入命令
    currentSerial->sendRawData(frame);
    
    // 记录发送的数据（关键信息）
    QString hexString;
    for (int j = 0; j < frame.size(); ++j) {
        hexString += QString("%1 ").arg(static_cast<quint8>(frame[j]), 2, 16, QChar('0')).toUpper();
    }
    
    QDateTime currentTime = QDateTime::currentDateTime();
    QString logMessage = currentTime.toString("[hh:mm:ss.zzz] ") + QString("TX: %1").arg(hexString.trimmed());
    ui->textBrowserLog->append(QString("<font color='#0000FF'>%1</font>").arg(logMessage));
    
    if (enableUILog) {
        ui->textBrowserLog->append(QString("<font color='#008000'>写入定值: %1</font>")
                                 .arg(itemDescriptions.join(", ")));
        ui->textBrowserLog->append(QString("<font color='#0080FF'>========== 报文发送完成 ==========</font>"));
        ui->textBrowserLog->append("");
    }
    
    // 显示写入完成信息
    QMessageBox::information(this, "写入完成", 
                           QString("已成功发送写入命令！\n\n写入项目：\n%1\n\n报文格式：06 10 0000 0034 68 + 数据 + CRC16")
                           .arg(itemDescriptions.join("\n")));
    
    // 写入完成后，延时执行第二次召唤数据操作
    ui->textBrowserLog->append("<font color='#FF8000'>写入定值完成，正在执行召唤数据刷新显示值...</font>");
    QTimer::singleShot(1000, this, [this]() {
        this->on_pushButtonSend_clicked();
        ui->textBrowserLog->append("<font color='#008000'>定值写入操作全部完成！</font>");
    });
}

// 创建写入定值的Modbus帧
QByteArray SetValueDialog::createWriteValueFrame(int stationId, int functionCode, const QString &address, const QList<quint16> &values)
{
    QByteArray frame;
    
    // 添加站号
    frame.append(static_cast<char>(stationId));
    
    // 添加功能码
    frame.append(static_cast<char>(functionCode));
    
    // 解析起始地址
    bool ok;
    quint16 startAddress = address.toUShort(&ok, 16);
    if (!ok) {
        return QByteArray(); // 地址解析失败
    }
    
    if (functionCode == 0x06) {
        // 写单个寄存器 (功能码06)
        if (values.size() != 1) {
            return QByteArray(); // 写单个寄存器只能有一个值
        }
        
        // 添加寄存器地址（大端序）
        frame.append(static_cast<char>((startAddress >> 8) & 0xFF));
        frame.append(static_cast<char>(startAddress & 0xFF));
        
        // 添加寄存器值（大端序）
        quint16 value = values[0];
        frame.append(static_cast<char>((value >> 8) & 0xFF));
        frame.append(static_cast<char>(value & 0xFF));
    }
    else if (functionCode == 0x10) {
        // 写多个寄存器 (功能码16)
        // 添加起始地址（大端序）
        frame.append(static_cast<char>((startAddress >> 8) & 0xFF));
        frame.append(static_cast<char>(startAddress & 0xFF));
        
        // 添加寄存器数量（大端序）
        quint16 registerCount = values.size();
        frame.append(static_cast<char>((registerCount >> 8) & 0xFF));
        frame.append(static_cast<char>(registerCount & 0xFF));
        
        // 添加字节数
        quint8 byteCount = registerCount * 2;
        frame.append(static_cast<char>(byteCount));
        
        // 添加寄存器值（大端序）
        for (quint16 value : values) {
            frame.append(static_cast<char>((value >> 8) & 0xFF));
            frame.append(static_cast<char>(value & 0xFF));
        }
    }
    else {
        return QByteArray(); // 不支持的功能码
    }
    
    // 添加CRC校验（如果启用）
    if (ui->checkBoxCRC16->isChecked()) {
        quint16 crc = CrcUtils::calculateCRC16(frame);
        frame.append(static_cast<char>(crc & 0xFF));        // CRC低字节
        frame.append(static_cast<char>((crc >> 8) & 0xFF)); // CRC高字节
    }
    
    return frame;
}