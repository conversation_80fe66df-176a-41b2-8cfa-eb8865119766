QT       += core gui serialport charts printsupport axcontainer

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++17

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    main.cpp \
    mainwindow.cpp \
    mappingwindow.cpp \
    modbusserial.cpp \
    modbusserialbase.cpp \
    parseitemdialog.cpp \
    setvaluedialog.cpp \
    valuefileselectiondialog.cpp \
    dataconversionrulesdialog.cpp \
    datamappingrulesdialog.cpp \
    crcutils.cpp \
    serialmanager.cpp
    # testcontroller.cpp  # REMOVED

HEADERS += \
    mainwindow.h \
    mappingwindow.h \
    modbusserial.h \
    modbusserialbase.h \
    modbustestcase.h \
    parseitemdialog.h \
    setvaluedialog.h \
    valuefileselectiondialog.h \
    dataconversionrulesdialog.h \
    datamappingrulesdialog.h \
    crcutils.h \
    serialmanager.h
    # testcontroller.h  # REMOVED

FORMS += \
    mainwindow.ui \
    mappingwindow.ui \
    setvaluedialog.ui

RESOURCES += \
    Resources.qrc \
    icons.qrc

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
