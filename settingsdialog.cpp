#include "settingsdialog.h"
#include "ui_settingsdialog.h"
#include <QSettings>
#include <QMessageBox>

SettingsDialog::SettingsDialog(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::SettingsDialog)
{
    ui->setupUi(this);
    loadDODISettings();
}

SettingsDialog::~SettingsDialog()
{
    delete ui;
}

void SettingsDialog::loadDODISettings()
{
    QSettings settings("JcSoft", "JcSoft");
    
    // 加载DO配置
    ui->lineEditDO1->setText(settings.value("DO/DO1", "").toString());
    ui->lineEditDO2->setText(settings.value("DO/DO2", "").toString());
    ui->lineEditDO3->setText(settings.value("DO/DO3", "").toString());
    ui->lineEditDO4->setText(settings.value("DO/DO4", "").toString());
    
    // 加载DI配置
    ui->lineEditDI1->setText(settings.value("DI/DI1", "").toString());
    ui->lineEditDI2->setText(settings.value("DI/DI2", "").toString());
    ui->lineEditDI3->setText(settings.value("DI/DI3", "").toString());
    ui->lineEditDI4->setText(settings.value("DI/DI4", "").toString());
}

void SettingsDialog::saveDODISettings()
{
    QSettings settings("JcSoft", "JcSoft");
    
    // 保存DO配置
    settings.setValue("DO/DO1", ui->lineEditDO1->text());
    settings.setValue("DO/DO2", ui->lineEditDO2->text());
    settings.setValue("DO/DO3", ui->lineEditDO3->text());
    settings.setValue("DO/DO4", ui->lineEditDO4->text());
    
    // 保存DI配置
    settings.setValue("DI/DI1", ui->lineEditDI1->text());
    settings.setValue("DI/DI2", ui->lineEditDI2->text());
    settings.setValue("DI/DI3", ui->lineEditDI3->text());
    settings.setValue("DI/DI4", ui->lineEditDI4->text());
    
    settings.sync();
}

void SettingsDialog::on_pushButtonSaveDODI_clicked()
{
    saveDODISettings();
    QMessageBox::information(this, tr("保存成功"), tr("DO-DI配置已保存"));
    accept();
}

void SettingsDialog::on_pushButtonCancelDODI_clicked()
{
    reject();
}