#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QDebug>
#include <QMessageBox>
#include <QSerialPortInfo>
#include <QTimer>
#include <QDateTime>
#include <QFileDialog>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QTableWidgetItem>
#include <QComboBox>
#include <QHeaderView>
#include <QAbstractScrollArea>
#include <stdexcept>
#include <cctype>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , m_serialManager(new SerialManager(this))
    , timerAutoRead<PERSON>(nullptr)
    , timerAutoReadB(nullptr)
{
    ui->setupUi(this);
    InitialSetting();

    // Connect signals from the SerialManager
    connect(m_serialManager, &SerialManager::connectionStateChanged, this, &MainWindow::handleSerialConnectionChanged);
    connect(m_serialManager, &SerialManager::errorOccurred, this, &MainWindow::handleSerialError);
    connect(m_serialManager, &SerialManager::dataSent, this, [this](const QString &portId, const QString &message) {
        QDateTime currentTime = QDateTime::currentDateTime();
        QString timeString = currentTime.toString("hh:mm:ss.zzz");
        QString logMessage = QString("[%1] %2 TX: %3").arg(timeString).arg(portId).arg(message);

        // 发送数据用蓝色显示
        QString coloredMessage = QString("<span style='color: #1976D2; font-weight: bold;'>%1</span>").arg(logMessage);

        if (portId == "A") {
            ui->textBrowserA->append(coloredMessage);
        } else if (portId == "B") {
            ui->textBrowserB->append(coloredMessage);
        }
    });
    
    connect(m_serialManager, &SerialManager::dataReceived, this, [this](const QString &portId, const QString &message) {
        QDateTime currentTime = QDateTime::currentDateTime();
        QString timeString = currentTime.toString("hh:mm:ss.zzz");
        QString logMessage = QString("[%1] %2 %3").arg(timeString).arg(portId).arg(message);

        // 接收数据用绿色显示
        QString coloredMessage = QString("<span style='color: #388E3C; font-weight: bold;'>%1</span>").arg(logMessage);

        if (portId == "A") {
            ui->textBrowserA->append(coloredMessage);
        } else if (portId == "B") {
            ui->textBrowserB->append(coloredMessage);
        }
    });
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::InitialSetting()
{
    // 初始化串口配置
    SearchSerialPorts();
    
    // 设置波特率选项
    QStringList baudRates = {"9600", "19200", "38400", "57600", "115200"};
    ui->comboBoxBaudA->addItems(baudRates);
    ui->comboBoxBaudB->addItems(baudRates);
    ui->comboBoxBaudA->setCurrentText("115200");
    ui->comboBoxBaudB->setCurrentText("115200");
    
    // 设置校验位选项
    QStringList parities = {"None", "Even", "Odd"};
    ui->comboBoxParityA->addItems(parities);
    ui->comboBoxParityB->addItems(parities);
    
    // 设置数据位选项
    QStringList dataBits = {"7", "8"};
    ui->comboBoxDataA->addItems(dataBits);
    ui->comboBoxDataB->addItems(dataBits);
    ui->comboBoxDataA->setCurrentText("8");
    ui->comboBoxDataB->setCurrentText("8");
    
    // 设置停止位选项
    QStringList stopBits = {"1", "2"};
    ui->comboBoxStopA->addItems(stopBits);
    ui->comboBoxStopB->addItems(stopBits);
    
    // 设置窗口标题
    setWindowTitle(tr("JcSoft - 双串口通信工具"));
}

void MainWindow::SearchSerialPorts()
{
    ui->comboBoxPortA->clear();
    ui->comboBoxPortB->clear();

    const auto ports = m_serialManager->getAvailablePorts();
    ui->comboBoxPortA->addItems(ports);
    ui->comboBoxPortB->addItems(ports);
}

void MainWindow::on_actionExit_triggered()
{
    close();
}

void MainWindow::on_actionMapping_triggered()
{
    try {
        qDebug() << "打开TEST配置界面";
        MappingWindow* mappingWindow = new MappingWindow(this);
        
        // 传递串口对象
        mappingWindow->setSerialPorts(m_serialManager->getSerialA(), m_serialManager->getSerialB());
        
        mappingWindow->setWindowTitle(tr("TEST配置"));
        mappingWindow->setAttribute(Qt::WA_DeleteOnClose);
        mappingWindow->show();
        mappingWindow->raise();
        mappingWindow->activateWindow();
    } catch (const std::exception& e) {
        qDebug() << "打开TEST配置界面异常: " << e.what();
        QMessageBox::critical(this, tr("错误"), tr("打开TEST配置界面时发生错误: %1").arg(e.what()));
    } catch (...) {
        qDebug() << "打开TEST配置界面发生未知异常";
        QMessageBox::critical(this, tr("错误"), tr("打开TEST配置界面时发生未知错误"));
    }
}

void MainWindow::on_actionSetValue_triggered()
{
    try {
        qDebug() << "打开定值召唤界面";
        
        SetValueDialog* setValueDialog = new SetValueDialog(this);
        setValueDialog->setWindowTitle(tr("定值召唤"));
        setValueDialog->setWindowFlags(Qt::Window | Qt::WindowMinimizeButtonHint | Qt::WindowMaximizeButtonHint | Qt::WindowCloseButtonHint);
        
        // 设置串口
        setValueDialog->setSerialPorts(m_serialManager->getSerialA(), m_serialManager->getSerialB());
        
        setValueDialog->setAttribute(Qt::WA_DeleteOnClose);
        setValueDialog->show();
        setValueDialog->raise();
        setValueDialog->activateWindow();
    } catch (const std::exception& e) {
        qDebug() << "打开定值召唤界面异常: " << e.what();
        QMessageBox::critical(this, tr("错误"), tr("打开定值召唤界面时发生错误: %1").arg(e.what()));
    } catch (...) {
        qDebug() << "打开定值召唤界面发生未知异常";
        QMessageBox::critical(this, tr("错误"), tr("打开定值召唤界面时发生未知错误"));
    }
}

// 串口连接相关槽函数
void MainWindow::on_actionConnectA_triggered()
{
    QString portName = ui->comboBoxPortA->currentText();
    int baudRate = ui->comboBoxBaudA->currentText().toInt();
    QString parity = ui->comboBoxParityA->currentText();
    int dataBits = ui->comboBoxDataA->currentText().toInt();
    QString stopBits = ui->comboBoxStopA->currentText();
    
    if (m_serialManager->connectPort("A", portName, baudRate, parity, dataBits, stopBits)) {
        ui->actionConnectA->setEnabled(false);
        ui->actionDisconnectA->setEnabled(true);
        ui->statusbar->showMessage(tr("串口A连接成功"), 3000);
    } else {
        QMessageBox::warning(this, tr("连接失败"), tr("无法连接串口A"));
    }
}

void MainWindow::on_actionConnectB_triggered()
{
    QString portName = ui->comboBoxPortB->currentText();
    int baudRate = ui->comboBoxBaudB->currentText().toInt();
    QString parity = ui->comboBoxParityB->currentText();
    int dataBits = ui->comboBoxDataB->currentText().toInt();
    QString stopBits = ui->comboBoxStopB->currentText();
    
    if (m_serialManager->connectPort("B", portName, baudRate, parity, dataBits, stopBits)) {
        ui->actionConnectB->setEnabled(false);
        ui->actionDisconnectB->setEnabled(true);
        ui->statusbar->showMessage(tr("串口B连接成功"), 3000);
    } else {
        QMessageBox::warning(this, tr("连接失败"), tr("无法连接串口B"));
    }
}

void MainWindow::on_actionDisconnectA_triggered()
{
    m_serialManager->disconnectPort("A");
    ui->actionConnectA->setEnabled(true);
    ui->actionDisconnectA->setEnabled(false);
    ui->statusbar->showMessage(tr("串口A已断开"), 3000);
}

void MainWindow::on_actionDisconnectB_triggered()
{
    m_serialManager->disconnectPort("B");
    ui->actionConnectB->setEnabled(true);
    ui->actionDisconnectB->setEnabled(false);
    ui->statusbar->showMessage(tr("串口B已断开"), 3000);
}

// 数据发送槽函数
void MainWindow::on_pushButtonSendA_clicked()
{
    QString data = ui->lineEditSendDataA->text();
    if (data.isEmpty()) {
        QMessageBox::warning(this, tr("警告"), tr("请输入要发送的数据"));
        return;
    }
    
    if (!m_serialManager->isConnected("A")) {
        QMessageBox::warning(this, tr("警告"), tr("串口A未连接"));
        return;
    }
    
    QByteArray byteData = QByteArray::fromHex(data.remove(' ').toLatin1());
    m_serialManager->sendData("A", byteData);
}

void MainWindow::on_pushButtonSendB_clicked()
{
    QString data = ui->lineEditSendDataB->text();
    if (data.isEmpty()) {
        QMessageBox::warning(this, tr("警告"), tr("请输入要发送的数据"));
        return;
    }
    
    if (!m_serialManager->isConnected("B")) {
        QMessageBox::warning(this, tr("警告"), tr("串口B未连接"));
        return;
    }
    
    QByteArray byteData = QByteArray::fromHex(data.remove(' ').toLatin1());
    m_serialManager->sendData("B", byteData);
}

// 清空接收区域函数已删除（UI中无对应按钮）

// 信号处理函数
void MainWindow::handleSerialConnectionChanged(const QString &portId, bool connected)
{
    QString message = QString("串口%1 %2").arg(portId).arg(connected ? "已连接" : "已断开");
    ui->statusbar->showMessage(message, 3000);
}

void MainWindow::handleSerialError(const QString &portId, const QString &error)
{
    QString message = QString("串口%1错误: %2").arg(portId).arg(error);
    ui->statusbar->showMessage(message, 5000);
    QMessageBox::warning(this, tr("串口错误"), message);
}
