#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QDebug>

#include "mappingwindow.h"
#include "setvaluedialog.h"
#include <QMessageBox>
#include <QDateTime>
#include <QSerialPortInfo>
#include <QDebug>
#include <QTimer>
#include <QFileDialog>
#include <QJsonDocument>
#include <QJsonArray>
#include <QJsonObject>
#include <QFile>
#include <stdexcept>
#include <cctype> // Added for std::isxdigit

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , m_serialManager(new SerialManager(this))
    , m_testController(new TestController(m_serialManager, this))
    , timerAutoReadA(nullptr)
    , timerAutoReadB(nullptr)
    , testCases()
{
    ui->setupUi(this);
    InitialSetting();
    initTestListUI();

    // Connect signals from the SerialManager
    connect(m_serialManager, &SerialManager::connectionStateChanged, this, &MainWindow::handleSerialConnectionChanged);
    connect(m_serialManager, &SerialManager::errorOccurred, this, &MainWindow::handleSerialError);
    connect(m_serialManager, &SerialManager::dataSent, this, [this](const QString &portId, const QString &message) {
        QDateTime currentTime = QDateTime::currentDateTime();
        QString text = currentTime.toString("[hh:mm:ss.zzz] ") + message;
        if (portId == "A") {
            ui->textBrowserA->append(QString("<font color='#008000'>%1</font>").arg(text)); // Deep Green for A sent
        } else if (portId == "B") {
            ui->textBrowserB->append(QString("<font color='#800080'>%1</font>").arg(text)); // Purple for B sent
        }
    });
    connect(m_serialManager, &SerialManager::dataReceived, this, &MainWindow::handleSerialMessage);

    // Connect signals from the TestController
    connect(m_testController, &TestController::testCaseFinished, this, &MainWindow::handleTestCaseFinished);
    connect(m_testController, &TestController::allTestsFinished, this, &MainWindow::handleAllTestsFinished);
    connect(m_testController, &TestController::logMessage, this, &MainWindow::logTestMessage);
}

MainWindow::~MainWindow()
{
    delete ui;
}



void MainWindow::on_actionMapping_triggered()
{
    try {
        qDebug() << "打开TEST配置界面";
        MappingWindow* mappingWindow = new MappingWindow(this); // Pass parent
        // mappingWindow->setSerialPorts(m_serialManager); // TODO: Pass SerialManager if needed
        mappingWindow->setWindowTitle(tr("TEST配置"));
        mappingWindow->setAttribute(Qt::WA_DeleteOnClose);
        mappingWindow->show();
        mappingWindow->raise();
        mappingWindow->activateWindow();
    } catch (const std::exception& e) {
        qDebug() << "打开TEST配置界面异常: " << e.what();
        QMessageBox::critical(this, tr("错误"), tr("打开TEST配置界面时发生错误: %1").arg(e.what()));
    } catch (...) {
        qDebug() << "打开TEST配置界面发生未知异常";
        QMessageBox::critical(this, tr("错误"), tr("打开TEST配置界面时发生未知错误"));
    }
}

void MainWindow::on_actionSetValue_triggered()
{
    try {
        qDebug() << "打开定值召唤界面";
        
        // 创建并显示SetValueDialog界面
        SetValueDialog* setValueDialog = new SetValueDialog(this);
        setValueDialog->setWindowTitle(tr("定值召唤"));
        // setValueDialog->setWindowModality(Qt::WindowModal); // 注释掉模态设置，允许主窗口同时操作
        setValueDialog->setWindowFlags(Qt::Window | Qt::WindowMinimizeButtonHint | Qt::WindowMaximizeButtonHint | Qt::WindowCloseButtonHint);
        
        // 设置串口
        // setValueDialog->setSerialPorts(m_serialManager); // TODO: Pass SerialManager if needed
        
        // 设置属性，使窗口关闭时自动删除
        setValueDialog->setAttribute(Qt::WA_DeleteOnClose);
        
        // 显示窗口
        setValueDialog->show();
        
    } catch (const std::exception& e) {
        qDebug() << "打开定值召唤界面异常: " << e.what();
        QMessageBox::critical(this, tr("错误"), tr("打开定值召唤界面时发生错误: %1").arg(e.what()));
    } catch (...) {
        qDebug() << "打开定值召唤界面发生未知异常";
        QMessageBox::critical(this, tr("错误"), tr("打开定值召唤界面时发生未知错误"));
    }
}

void MainWindow::on_actionImportConfig_triggered()
{
    try {
        // 查找已存在的映射窗口
        MappingWindow* mappingWindow = findChild<MappingWindow*>();
        
        // 如果窗口不存在，则创建一个新的
        if (!mappingWindow) {
            qDebug() << "创建新的TEST配置界面用于导入配置";
            mappingWindow = new MappingWindow(this);
            mappingWindow->setWindowTitle(tr("TEST配置"));
            mappingWindow->setWindowModality(Qt::WindowModal);
            mappingWindow->setWindowFlags(Qt::Dialog | Qt::WindowSystemMenuHint | Qt::WindowTitleHint | Qt::WindowCloseButtonHint);
            mappingWindow->setAttribute(Qt::WA_DeleteOnClose);
            mappingWindow->show();
        }
        
        // 触发导入配置功能
        mappingWindow->importConfig();
    } catch (const std::exception& e) {
        qDebug() << "导入配置异常: " << e.what();
        QMessageBox::critical(this, tr("错误"), tr("导入配置时发生错误: %1").arg(e.what()));
    } catch (...) {
        qDebug() << "导入配置发生未知异常";
        QMessageBox::critical(this, tr("错误"), tr("导入配置时发生未知错误"));
    }
}

void MainWindow::on_actionExportConfig_triggered()
{
    try {
        // 查找已存在的映射窗口
        MappingWindow* mappingWindow = findChild<MappingWindow*>();
        
        // 如果窗口不存在，则创建一个新的
        if (!mappingWindow) {
            qDebug() << "创建新的TEST配置界面用于导出配置";
            mappingWindow = new MappingWindow(this);
            mappingWindow->setWindowTitle(tr("TEST配置"));
            mappingWindow->setWindowModality(Qt::WindowModal);
            mappingWindow->setWindowFlags(Qt::Dialog | Qt::WindowSystemMenuHint | Qt::WindowTitleHint | Qt::WindowCloseButtonHint);
            mappingWindow->setAttribute(Qt::WA_DeleteOnClose);
            mappingWindow->show();
        }
        
        // 触发导出配置功能
        mappingWindow->exportConfig();
    } catch (const std::exception& e) {
        qDebug() << "导出配置异常: " << e.what();
        QMessageBox::critical(this, tr("错误"), tr("导出配置时发生错误: %1").arg(e.what()));
    } catch (...) {
        qDebug() << "导出配置发生未知异常";
        QMessageBox::critical(this, tr("错误"), tr("导出配置时发生未知错误"));
    }
}

void MainWindow::InitialSetting()
{
    // 设置窗口图标
    setWindowIcon(QIcon(":/icons/settings.svg")); // 修改图标路径以匹配 qrc 文件
    
    // 连接测试定时器信号
    connect(ui->checkBoxUseCRC16, &QCheckBox::toggled, this, &MainWindow::on_checkBoxUseCRC16_toggled);

    // 初始化波特率选项
    QList<QString> baudList = {"1200", "2400", "4800", "9600", "19200", "38400", "57600", "115200"};
    ui->comboBoxBaudA->addItems(baudList);
    ui->comboBoxBaudA->setCurrentText("9600");
    ui->comboBoxBaudB->addItems(baudList);
    ui->comboBoxBaudB->setCurrentText("9600");

    // 初始化数据位选项
    QList<QString> dataList = {"5", "6", "7", "8"};
    ui->comboBoxDataA->addItems(dataList);
    ui->comboBoxDataA->setCurrentText("8");
    ui->comboBoxDataB->addItems(dataList);
    ui->comboBoxDataB->setCurrentText("8");

    // 初始化校验位选项
    QList<QString> parityList = {"None", "Odd", "Even", "Mark", "Space"};
    ui->comboBoxParityA->addItems(parityList);
    ui->comboBoxParityA->setCurrentText("None");
    ui->comboBoxParityB->addItems(parityList);
    ui->comboBoxParityB->setCurrentText("None");

    // 初始化停止位选项
    QList<QString> stopList = {"1", "1.5", "2"};
    ui->comboBoxStopA->addItems(stopList);
    ui->comboBoxStopA->setCurrentText("1");
    ui->comboBoxStopB->addItems(stopList);
    ui->comboBoxStopB->setCurrentText("1");

    // 搜索可用串口
    SearchSerialPorts();
    
    // 初始化Modbus测试列表
    initModbusTestListUI();
    
    // 连接Modbus测试列表相关信号槽
    connect(ui->pushButtonAddModbusTest, &QPushButton::clicked, this, &MainWindow::on_pushButtonAddModbusTest_clicked);
    connect(ui->pushButtonRemoveModbusTest, &QPushButton::clicked, this, &MainWindow::on_pushButtonRemoveModbusTest_clicked);
    connect(ui->pushButtonCopyModbusTest, &QPushButton::clicked, this, &MainWindow::on_pushButtonCopyModbusTest_clicked);
    connect(ui->pushButtonImportModbus, &QPushButton::clicked, this, &MainWindow::on_pushButtonImportModbus_clicked);
    connect(ui->pushButtonExportModbus, &QPushButton::clicked, this, &MainWindow::on_pushButtonExportModbus_clicked);
    connect(ui->pushButtonModbusTest, &QPushButton::clicked, this, &MainWindow::on_pushButtonModbusTest_clicked);
    connect(ui->pushButtonClearModbusTests, &QPushButton::clicked, this, &MainWindow::on_pushButtonClearModbusTests_clicked);
    connect(ui->tableWidgetModbusTests, &QTableWidget::itemSelectionChanged, this, &MainWindow::on_tableWidgetModbusTests_itemSelectionChanged);
    connect(ui->tableWidgetModbusTests, &QTableWidget::itemChanged, this, &MainWindow::on_tableWidgetModbusTests_itemChanged);
}

void MainWindow::SearchSerialPorts()
{
    ui->comboBoxPortA->clear();
    ui->comboBoxPortB->clear();

    const auto ports = m_serialManager->getAvailablePorts();
    ui->comboBoxPortA->addItems(ports);
    ui->comboBoxPortB->addItems(ports);
}

void MainWindow::on_actionExit_triggered()
{
    close();
}

void MainWindow::on_actionConnectA_triggered()
{
    m_serialManager->connectPort("A",
                                 ui->comboBoxPortA->currentText(),
                                 ui->comboBoxBaudA->currentText().toInt(),
                                 ui->comboBoxParityA->currentText(),
                                 ui->comboBoxDataA->currentText().toInt(),
                                 ui->comboBoxStopA->currentText());
}

void MainWindow::on_actionConnectB_triggered()
{
    m_serialManager->connectPort("B",
                                 ui->comboBoxPortB->currentText(),
                                 ui->comboBoxBaudB->currentText().toInt(),
                                 ui->comboBoxParityB->currentText(),
                                 ui->comboBoxDataB->currentText().toInt(),
                                 ui->comboBoxStopB->currentText());
}

void MainWindow::on_actionDisconnectA_triggered()
{
    m_serialManager->disconnectPort("A");
    if (timerAutoReadA && timerAutoReadA->isActive()) {
        timerAutoReadA->stop();
    }
}

void MainWindow::on_actionDisconnectB_triggered()
{
    m_serialManager->disconnectPort("B");
    if (timerAutoReadB && timerAutoReadB->isActive()) {
        timerAutoReadB->stop();
    }
}

void MainWindow::on_actionRefresh_triggered()
{
    SearchSerialPorts();
}



// 初始化测试列表UI
void MainWindow::initTestListUI()
{
    // 设置表格列标题 - 与UART配置界面保持一致
    QStringList headers;
    headers << tr("序号") << tr("测试描述") << tr("串口A发送") << tr("串口A期望接收") << tr("串口A接收") 
           << tr("串口B发送") << tr("串口B期望接收") << tr("串口B接收") << tr("测试结果");
    ui->tableWidgetTests->setColumnCount(headers.size());
    ui->tableWidgetTests->setHorizontalHeaderLabels(headers);
    
    // 优化表格显示
    ui->tableWidgetTests->horizontalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents);
    ui->tableWidgetTests->verticalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents);
    ui->tableWidgetTests->setSizeAdjustPolicy(QAbstractScrollArea::AdjustToContents);
    ui->tableWidgetTests->setWordWrap(true);
    ui->tableWidgetTests->setMinimumHeight(200); // Display more rows
    ui->tableWidgetTests->setStyleSheet(
        "QTableWidget { gridline-color: #d0d0d0; }"
        "QTableWidget::item { padding: 4px; }"
        "QHeaderView::section { background-color: #f0f0f0; padding: 4px; }"
    );
}

// 初始化Modbus测试列表UI
void MainWindow::initModbusTestListUI()
{
    // 设置表格列标题
    QStringList headers;
    headers << tr("序号") << tr("测试描述") << tr("串口") << tr("站号") << tr("功能码") 
           << tr("发送数据") << tr("期望响应") << tr("实际响应") << tr("测试结果");
    ui->tableWidgetModbusTests->setColumnCount(headers.size());
    ui->tableWidgetModbusTests->setHorizontalHeaderLabels(headers);
    
    // 优化表格显示
    ui->tableWidgetModbusTests->horizontalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents);
    ui->tableWidgetModbusTests->verticalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents);
    ui->tableWidgetModbusTests->setSizeAdjustPolicy(QAbstractScrollArea::AdjustToContents);
    ui->tableWidgetModbusTests->setWordWrap(true);
    ui->tableWidgetModbusTests->setStyleSheet(
        "QTableWidget { gridline-color: #d0d0d0; }"
        "QTableWidget::item { padding: 4px; }"
        "QHeaderView::section { background-color: #f0f0f0; padding: 4px; }"
    );
}

// 更新表格行 - 与UART配置界面保持一致
void MainWindow::updateTableRow(int row, const ModbusTestCase &testCase)
{
    // 将ModbusTestCase转换为与TestCase结构体兼容的格式
    ui->tableWidgetTests->setItem(row, 0, new QTableWidgetItem(QString::number(row + 1)));
    
    // 检查是否是B串口的测试用例
    bool isPortB = testCase.description.startsWith("[B]");
    QString description = testCase.description;
    if (isPortB) {
        // 移除[B]标记
        description = description.mid(3);
    }
    ui->tableWidgetTests->setItem(row, 1, new QTableWidgetItem(description));
    
    // 直接使用原始数据，不添加前缀
    QString sentData = testCase.sendData;
    
    if (isPortB) {
        // B串口数据
        // 串口A发送、期望接收和实际接收 - 初始为空
        ui->tableWidgetTests->setItem(row, 2, new QTableWidgetItem(""));
        ui->tableWidgetTests->setItem(row, 3, new QTableWidgetItem(""));
        ui->tableWidgetTests->setItem(row, 4, new QTableWidgetItem(""));
        
        // 串口B发送
        ui->tableWidgetTests->setItem(row, 5, new QTableWidgetItem(sentData));
        
        // 串口B期望接收
        ui->tableWidgetTests->setItem(row, 6, new QTableWidgetItem(testCase.expectedResponse));
        
        // 串口B实际接收 - 初始为空
        ui->tableWidgetTests->setItem(row, 7, new QTableWidgetItem(""));
    } else {
        // A串口数据
        // 串口A发送
        ui->tableWidgetTests->setItem(row, 2, new QTableWidgetItem(sentData));
        
        // 串口A期望接收
        ui->tableWidgetTests->setItem(row, 3, new QTableWidgetItem(testCase.expectedResponse));
        
        // 串口A实际接收 - 初始为空
        ui->tableWidgetTests->setItem(row, 4, new QTableWidgetItem(""));
        
        // 串口B发送、期望接收和实际接收 - 初始为空
        ui->tableWidgetTests->setItem(row, 5, new QTableWidgetItem(""));
        ui->tableWidgetTests->setItem(row, 6, new QTableWidgetItem(""));
        ui->tableWidgetTests->setItem(row, 7, new QTableWidgetItem(""));
    }
    
    // 测试结果
    ui->tableWidgetTests->setItem(row, 8, new QTableWidgetItem(testCase.result));
}

// 更新Modbus测试表格行
void MainWindow::updateModbusTableRow(int row, const ModbusTestCase &testCase)
{
    // 序号
    ui->tableWidgetModbusTests->setItem(row, 0, new QTableWidgetItem(QString::number(row + 1)));
    
    // 测试描述
    ui->tableWidgetModbusTests->setItem(row, 1, new QTableWidgetItem(testCase.description));

    // 串口选择
    QComboBox *comboBoxSerialPort = new QComboBox();
    comboBoxSerialPort->addItem(tr("串口A"));
    comboBoxSerialPort->addItem(tr("串口B"));

    // 如果ModbusTestCase中存储了串口信息，在这里设置comboBoxSerialPort的当前选项
    if (!testCase.targetSerialPort.isEmpty()) {
        if (testCase.targetSerialPort == "A") {
            comboBoxSerialPort->setCurrentIndex(0);
        } else if (testCase.targetSerialPort == "B") {
            comboBoxSerialPort->setCurrentIndex(1);
        }
    } else {
        // 默认选择串口A
        comboBoxSerialPort->setCurrentIndex(0);
    }
    ui->tableWidgetModbusTests->setCellWidget(row, 2, comboBoxSerialPort);
    // 连接信号，当选择更改时更新testCases中的对应项
    connect(comboBoxSerialPort, QOverload<int>::of(&QComboBox::currentIndexChanged),
            [this, row](int index) {
        if (row < testCases.size()) {
            testCases[row].targetSerialPort = (index == 0) ? "A" : "B";
        }
    });

    // 站号 - 十六进制显示
    ui->tableWidgetModbusTests->setItem(row, 3, new QTableWidgetItem(QString::number(testCase.stationId, 16).toUpper().rightJustified(2, '0'))); 
    
    // 功能码 - 十六进制显示
    ui->tableWidgetModbusTests->setItem(row, 4, new QTableWidgetItem(QString::number(testCase.functionCode, 16).toUpper().rightJustified(2, '0')));
    
    // 发送数据
    // 如果 sendData 为空，则尝试使用 sentDataA (去掉站号和功能码部分)
    QString displaySendData = testCase.sendData;
    if (displaySendData.isEmpty() && !testCase.sentDataA.isEmpty()) {
        QStringList parts = testCase.sentDataA.split(' ');
        if (parts.length() > 2) {
            displaySendData = parts.mid(2).join(' ');
        }
    }
    ui->tableWidgetModbusTests->setItem(row, 5, new QTableWidgetItem(displaySendData));
    
    // 期望响应
    // 如果 expectedResponse 为空，则尝试使用 expectReceiveA
    QString displayExpectedResponse = testCase.expectedResponse;
    if (displayExpectedResponse.isEmpty() && !testCase.expectReceiveA.isEmpty()) {
        displayExpectedResponse = testCase.expectReceiveA;
    }
    ui->tableWidgetModbusTests->setItem(row, 6, new QTableWidgetItem(displayExpectedResponse));
    
    // 实际响应 - 初始为空
    ui->tableWidgetModbusTests->setItem(row, 7, new QTableWidgetItem(""));
    
    // 测试结果
    ui->tableWidgetModbusTests->setItem(row, 8, new QTableWidgetItem(testCase.result));
}

// Modbus测试列表相关槽函数实现
void MainWindow::on_pushButtonAddModbusTest_clicked()
{
    // 创建新的测试用例并填充默认值
    ModbusTestCase testCase;
    testCase.description = QString("Modbus测试用例 %1").arg(ui->tableWidgetModbusTests->rowCount() + 1);
    testCase.stationId = 1; // 默认站号为1
    testCase.functionCode = 3; // 默认读保持寄存器
    testCase.sendData = "00 00 00 01"; // 旧格式的默认发送数据
    testCase.expectedResponse = ""; // 旧格式的默认期望响应
    testCase.timeout = 1000; // 默认1秒超时
    // testCase.crcType = "CRC16"; // 不再使用crcType
    testCase.result = "未测试";
    testCase.targetSerialPort = "A"; // 默认目标串口为A

    // 新格式字段的默认值
    testCase.sentDataA = QString("%1 %2 %3").arg(QString::number(testCase.stationId, 16).toUpper().rightJustified(2, '0'))
                                     .arg(QString::number(testCase.functionCode, 16).toUpper().rightJustified(2, '0'))
                                     .arg(testCase.sendData); // sentDataA 包含站号、功能码和数据
    testCase.sentDataB = "";
    testCase.expectReceiveA = "";
    testCase.expectReceiveB = "";
    testCase.actualReceiveA = "";
    testCase.actualReceiveB = "";
    
    // 计算默认的CRC16
    QByteArray dataForCrc;
    QStringList hexBytes = testCase.sentDataA.split(' ');
    for (const QString &byteStr : hexBytes) {
        bool ok;
        dataForCrc.append(static_cast<char>(byteStr.toInt(&ok, 16)));
    }
    if (!dataForCrc.isEmpty()) {
        quint16 crc_val = CrcUtils::calculateCRC16(dataForCrc);
        // 将quint16 CRC值转换为高低字节反转的16进制字符串
        testCase.crc16 = QString("%1%2").arg((crc_val & 0xFF), 2, 16, QChar('0')).arg(((crc_val >> 8) & 0xFF), 2, 16, QChar('0')).toUpper();
    } else {
        testCase.crc16 = "";
    }
    
    // 添加到测试用例列表
    testCases.append(testCase);
    
    // 更新表格
    int row = ui->tableWidgetModbusTests->rowCount();
    ui->tableWidgetModbusTests->insertRow(row);
    updateModbusTableRow(row, testCase);
    
    // 选择新添加的行
    ui->tableWidgetModbusTests->selectRow(row);
}

void MainWindow::on_pushButtonRemoveModbusTest_clicked()
{
    int row = ui->tableWidgetModbusTests->currentRow();
    if (row >= 0) {
        // 从测试用例列表中移除
        if (row < testCases.size()) {
            testCases.removeAt(row);
        }
        
        // 从表格中移除
        ui->tableWidgetModbusTests->removeRow(row);
        
        // 更新序号
        for (int i = 0; i < ui->tableWidgetModbusTests->rowCount(); ++i) {
            ui->tableWidgetModbusTests->item(i, 0)->setText(QString::number(i + 1));
        }
    }
}

void MainWindow::on_pushButtonCopyModbusTest_clicked()
{
    int row = ui->tableWidgetModbusTests->currentRow();
    if (row >= 0 && row < testCases.size()) {
        // 复制当前选中的测试用例
        ModbusTestCase testCase = testCases.at(row);
        testCase.description += tr(" (复制)");
        testCase.result = tr("未测试");
        
        // 添加到测试用例列表
        testCases.append(testCase);
        
        // 更新表格
        int newRow = ui->tableWidgetModbusTests->rowCount();
        ui->tableWidgetModbusTests->insertRow(newRow);
        updateModbusTableRow(newRow, testCase);
        
        // 选择新添加的行
        ui->tableWidgetModbusTests->selectRow(newRow);
    }
}

void MainWindow::on_pushButtonImportModbus_clicked()
{
    // 打开文件对话框选择要导入的JSON文件
    QString filePath = QFileDialog::getOpenFileName(this, tr("导入Modbus测试配置"), "", tr("JSON文件 (*.json)"));
    if (filePath.isEmpty()) {
        return;
    }
    
    try {
        // 读取JSON文件
        QFile file(filePath);
        if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
            QMessageBox::critical(this, tr("错误"), tr("无法打开文件: %1").arg(filePath));
            return;
        }
        
        QByteArray jsonData = file.readAll();
        file.close();
        
        // 解析JSON
        QJsonDocument doc = QJsonDocument::fromJson(jsonData);
        if (doc.isNull()) {
            QMessageBox::critical(this, tr("错误"), tr("无效的JSON格式"));
            return;
        }
        
        // 清空当前测试用例
        testCases.clear();
        ui->tableWidgetModbusTests->setRowCount(0);
        
        // 处理两种可能的JSON格式：数组格式或MappingWindow的对象格式
        if(doc.isArray()) {
            // 原始格式：直接是测试用例数组
            QJsonArray array = doc.array();
            importModbusTestCasesFromArray(array);
        } else if(doc.isObject()) {
            // MappingWindow格式：包含mapping和testSteps两个键的对象
            QJsonObject rootObj = doc.object();
            
            // 检查是否包含testSteps键
            if(rootObj.contains("testSteps")) {
                QJsonArray testStepsArray = rootObj["testSteps"].toArray();
                importModbusTestStepsFromMappingWindow(testStepsArray);
            } else {
                QMessageBox::warning(this, tr("错误"), tr("JSON文件中未找到测试步骤数据"));
                return;
            }
        } else {
            QMessageBox::warning(this, tr("错误"), tr("不支持的JSON文件格式"));
            return;
        }
        
        QMessageBox::information(this, tr("导入成功"), tr("成功导入 %1 个测试用例").arg(testCases.size()));
    } catch (const std::exception& e) {
        QMessageBox::critical(this, tr("错误"), tr("导入测试用例时发生错误: %1").arg(e.what()));
    } catch (...) {
        QMessageBox::critical(this, tr("错误"), tr("导入测试用例时发生未知错误"));
    }
}

// 从标准Modbus测试用例数组导入
void MainWindow::importModbusTestCasesFromArray(const QJsonArray &array)
{
    for (int i = 0; i < array.size(); ++i) {
        QJsonObject obj = array[i].toObject();
        
        ModbusTestCase testCase;
        testCase.description = obj.value("description").toString();
        testCase.actualReceiveA = obj.value("actualReceiveA").toString();
        testCase.actualReceiveB = obj.value("actualReceiveB").toString();
        testCase.expectReceiveA = obj.value("expectReceiveA").toString();
        testCase.expectReceiveB = obj.value("expectReceiveB").toString();
        testCase.sentDataA = obj.value("sentDataA").toString();
        testCase.sentDataB = obj.value("sentDataB").toString();
        testCase.crc16 = obj.value("crc16").toString();
        testCase.sendData = obj.value("sendData").toString(); // 保留对旧格式sendData的兼容
        testCase.expectedResponse = obj.value("expectedResponse").toString(); // 保留对旧格式expectedResponse的兼容

        testCase.timeout = obj.value("timeout").toInt(1000);
        testCase.crcType = obj.value("crcType").toString("CRC16"); // 旧字段，可能需要移除或映射
        testCase.result = tr("未测试");
        testCase.targetSerialPort = obj.value("targetSerialPort").toString("A");

        // 解析站号和功能码的逻辑
        parseStationIdAndFunctionCode(testCase, obj);
        
        // 如果 sendData 字段为空，但 sentDataA 不为空，则根据 stationId 和 functionCode 填充 sendData
        // 这是为了兼容旧的 Modbus 处理逻辑，它可能依赖于合并后的 sendData
        if (testCase.sendData.isEmpty() && !testCase.sentDataA.isEmpty()) {
            QStringList hexBytesA = testCase.sentDataA.split(' ');
            if (hexBytesA.length() > 2) {
                testCase.sendData = hexBytesA.mid(2).join(' ');
            } else {
                testCase.sendData = ""; // 如果sentDataA中只有站号和功能码
            }
        }
        // 如果 expectedResponse 字段为空，但 expectReceiveA 不为空，则填充 expectedResponse
        if (testCase.expectedResponse.isEmpty() && !testCase.expectReceiveA.isEmpty()){
            testCase.expectedResponse = testCase.expectReceiveA;
        }

        // 自动生成CRC16校验码（Modbus协议必须有CRC16校验）
        generateModbusCRC16(testCase);
        
        // 添加到测试用例列表
        testCases.append(testCase);
        
        // 更新表格
        int row = ui->tableWidgetModbusTests->rowCount();
        ui->tableWidgetModbusTests->insertRow(row);
        updateModbusTableRow(row, testCase);
    }
}

// 从MappingWindow的测试步骤数组导入Modbus测试用例
void MainWindow::importModbusTestStepsFromMappingWindow(const QJsonArray &testStepsArray)
{
    for(int i = 0; i < testStepsArray.size(); i++) {
        QJsonObject stepObj = testStepsArray[i].toObject();
        
        // 只导入controller和protection类型的步骤，跳过info类型
        QString type = stepObj["type"].toString();
        if(type == "info") continue;
        
        ModbusTestCase testCase;
        testCase.description = stepObj["notes"].toString();
        testCase.timeout = 1000; // 默认值
        testCase.crcType = "CRC16"; // 默认值
        testCase.result = tr("未测试");
        
        // 根据步骤类型设置发送和接收数据
        QString sendData = stepObj["sendData"].toString();
        QString expectReceiveData = stepObj["expectReceiveData"].toString();
        
        if(type == "controller") {
            // 控制器步骤使用串口A
            testCase.sentDataA = sendData;
            testCase.expectReceiveA = expectReceiveData;
            testCase.targetSerialPort = "A";
        } else if(type == "protection") {
            // 保护装置步骤使用串口B
            testCase.sentDataB = sendData;
            testCase.expectReceiveB = expectReceiveData;
            testCase.targetSerialPort = "B";
            
            // 添加一个标记，表示这是B串口的数据
            testCase.description = "[B]" + testCase.description;
        }
        
        // 解析站号、功能码和数据
        if (!sendData.isEmpty()) {
            QStringList hexBytes = sendData.split(' ');
            if (hexBytes.length() >= 2) {
                // 第一个字节是站号，第二个字节是功能码
                bool ok1, ok2;
                testCase.stationId = hexBytes[0].toInt(&ok1, 16);
                testCase.functionCode = hexBytes[1].toInt(&ok2, 16);
                
                if (!ok1) testCase.stationId = 1; // 默认值
                if (!ok2) testCase.functionCode = 3; // 默认值
                
                // 剩余的是数据部分
                if (hexBytes.length() > 2) {
                    testCase.sendData = hexBytes.mid(2).join(' ');
                } else {
                    testCase.sendData = "";
                }
            } else {
                // 数据不足，使用默认值
                testCase.stationId = 1;
                testCase.functionCode = 3;
                testCase.sendData = sendData;
            }
        } else {
            // 没有数据，使用默认值
            testCase.stationId = 1;
            testCase.functionCode = 3;
            testCase.sendData = "";
        }
        
        // 设置期望响应
        testCase.expectedResponse = expectReceiveData;
        
        // 自动生成CRC16校验码（Modbus协议必须有CRC16校验）
        generateModbusCRC16(testCase);
        
        // 添加到测试用例列表
        testCases.append(testCase);
        
        // 更新表格
        int row = ui->tableWidgetModbusTests->rowCount();
        ui->tableWidgetModbusTests->insertRow(row);
        updateModbusTableRow(row, testCase);
    }
}

// 解析站号和功能码的辅助函数
void MainWindow::parseStationIdAndFunctionCode(ModbusTestCase &testCase, const QJsonObject &obj)
{
    // 优先从JSON字段读取站号和功能码 (stationId, functionCode)
    bool stationIdOk = false;
    QJsonValue stationIdVal = obj.value("stationId");
    int stationIdFromJson = 0;
    if (stationIdVal.isString()) {
        stationIdFromJson = stationIdVal.toString().toInt(&stationIdOk);
    } else if (stationIdVal.isDouble()) {
        stationIdFromJson = stationIdVal.toInt();
        stationIdOk = true;
    }

    bool functionCodeOk = false;
    QJsonValue functionCodeVal = obj.value("functionCode");
    int functionCodeFromJson = 0;
    if (functionCodeVal.isString()) {
        functionCodeFromJson = functionCodeVal.toString().toInt(&functionCodeOk);
    } else if (functionCodeVal.isDouble()) {
        functionCodeFromJson = functionCodeVal.toInt();
        functionCodeOk = true;
    }

    if (stationIdOk && !stationIdVal.isNull()) { // 仅检查 stationIdVal 是否为 null
        testCase.stationId = stationIdFromJson;
    } else {
         // 如果 stationId 为空或解析失败，尝试从 sentDataA 解析
        QStringList hexBytesA = testCase.sentDataA.split(' ');
        if (!hexBytesA.isEmpty() && !hexBytesA[0].isEmpty()) {
            bool ok;
            int val = hexBytesA[0].toInt(&ok, 16);
            if (ok) testCase.stationId = val;
            else testCase.stationId = 1; // 默认值
        } else {
            testCase.stationId = 1; // 默认值
        }
    }

    if (functionCodeOk && !functionCodeVal.isNull()) { // 仅检查 functionCodeVal 是否为 null
        testCase.functionCode = functionCodeFromJson;
    } else {
        // 如果 functionCode 为空或解析失败，尝试从 sentDataA 解析
        QStringList hexBytesA = testCase.sentDataA.split(' ');
        if (hexBytesA.length() > 1 && !hexBytesA[1].isEmpty()) {
            bool ok;
            int val = hexBytesA[1].toInt(&ok, 16);
            if (ok) testCase.functionCode = val;
            else testCase.functionCode = 3; // 默认值
        } else {
            testCase.functionCode = 3; // 默认值
        }
    }
}

// 自动生成Modbus CRC16校验码的辅助函数
void MainWindow::generateModbusCRC16(ModbusTestCase &testCase)
{
    // 确定要计算CRC16的数据源
    QString dataSource;
    if (!testCase.sentDataA.isEmpty()) {
        dataSource = testCase.sentDataA;
    } else if (!testCase.sentDataB.isEmpty()) {
        dataSource = testCase.sentDataB;
    } else {
        // 如果两个都为空，尝试构造完整的Modbus帧
        if (!testCase.sendData.isEmpty()) {
            dataSource = QString("%1 %2 %3")
                .arg(QString::number(testCase.stationId, 16).toUpper().rightJustified(2, '0'))
                .arg(QString::number(testCase.functionCode, 16).toUpper().rightJustified(2, '0'))
                .arg(testCase.sendData);
        } else {
            // 没有数据可以计算CRC16
            testCase.crc16 = "";
            return;
        }
    }
    
    // 如果数据为空，不计算CRC16
    if (dataSource.trimmed().isEmpty()) {
        testCase.crc16 = "";
        return;
    }
    
    // 将十六进制字符串转换为字节数组
    QByteArray dataToSend;
    QStringList hexBytes = dataSource.split(' ');
    for (const QString &byteStr : hexBytes) {
        if (!byteStr.trimmed().isEmpty()) {
            bool ok;
            int byteValue = byteStr.toInt(&ok, 16);
            if (ok) {
                dataToSend.append(static_cast<char>(byteValue));
            }
        }
    }
    
    if (!dataToSend.isEmpty()) {
        // 使用CrcUtils::calculateCRC16Bytes函数获取正确字节顺序的CRC16值
    QByteArray crcBytes = CrcUtils::calculateCRC16Bytes(dataToSend);
        if (crcBytes.size() >= 2) {
            // 将QByteArray CRC值转换为16进制字符串（Modbus CRC16格式）
            testCase.crc16 = QString("%1 %2")
                .arg(static_cast<quint8>(crcBytes[0]), 2, 16, QChar('0'))
                .arg(static_cast<quint8>(crcBytes[1]), 2, 16, QChar('0'))
                .toUpper();
        } else {
            testCase.crc16 = "";
        }
    } else {
        testCase.crc16 = "";
    }
}

void MainWindow::on_pushButtonExportModbus_clicked()
{
    if (testCases.isEmpty()) {
        QMessageBox::warning(this, tr("警告"), tr("没有测试用例可导出"));
        return;
    }
    
    // 打开文件对话框选择保存位置
    QString filePath = QFileDialog::getSaveFileName(this, tr("导出Modbus测试配置"), "", tr("JSON文件 (*.json)"));
    if (filePath.isEmpty()) {
        return;
    }
    
    try {
        // 创建JSON数组
        QJsonArray array;
        for (const ModbusTestCase &testCase : testCases) {
            QJsonObject obj;
            obj["description"] = testCase.description;
            obj["stationId"] = testCase.stationId; // 导出时保持数字类型
            obj["functionCode"] = testCase.functionCode; // 导出时保持数字类型
            obj["sendData"] = testCase.sendData; // 旧格式，兼容性保留
            obj["expectedResponse"] = testCase.expectedResponse; // 旧格式，兼容性保留
            obj["timeout"] = testCase.timeout;
            // obj["crcType"] = testCase.crcType; // crcType 不再导出，由 crc16 替代
            obj["targetSerialPort"] = testCase.targetSerialPort;

            // 新格式字段
            obj["actualReceiveA"] = testCase.actualReceiveA;
            obj["actualReceiveB"] = testCase.actualReceiveB;
            obj["expectReceiveA"] = testCase.expectReceiveA;
            obj["expectReceiveB"] = testCase.expectReceiveB;
            obj["sentDataA"] = testCase.sentDataA;
            obj["sentDataB"] = testCase.sentDataB;
            obj["crc16"] = testCase.crc16;
            
            array.append(obj);
        }
        
        // 创建JSON文档
        QJsonDocument doc(array);
        
        // 写入文件
        QFile file(filePath);
        if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            QMessageBox::critical(this, tr("错误"), tr("无法写入文件: %1").arg(filePath));
            return;
        }
        
        file.write(doc.toJson());
        file.close();
        
        QMessageBox::information(this, tr("导出成功"), tr("成功导出 %1 个测试用例").arg(testCases.size()));
    } catch (const std::exception& e) {
        QMessageBox::critical(this, tr("错误"), tr("导出测试用例时发生错误: %1").arg(e.what()));
    } catch (...) {
        QMessageBox::critical(this, tr("错误"), tr("导出测试用例时发生未知错误"));
    }
}

void MainWindow::on_pushButtonModbusTest_clicked()
{
    if (testCases.isEmpty()) {
        QMessageBox::warning(this, tr("警告"), tr("没有测试用例可执行"));
        return;
    }
    
    if (!m_serialManager->isConnected("A") && !m_serialManager->isConnected("B")) {
        QMessageBox::warning(this, tr("警告"), tr("请先连接串口"));
        return;
    }
    
    m_testController->setTestCases(testCases);
    m_testController->startTests(ui->spinBoxDelay->value());
    ui->pushButtonModbusTest->setText(tr("停止测试"));
}

void MainWindow::on_pushButtonClearModbusTests_clicked()
{
    // 确认是否清空测试用例
    QMessageBox::StandardButton reply = QMessageBox::question(this, tr("确认"),
                                                           tr("确定要清空所有Modbus测试用例吗？"),
                                                           QMessageBox::Yes | QMessageBox::No);
    if (reply == QMessageBox::Yes) {
        // 清空表格
        ui->tableWidgetModbusTests->setRowCount(0);
        
        // 清空测试用例列表
        testCases.clear();
        
        // 更新UI状态
        ui->pushButtonRemoveModbusTest->setEnabled(false);
        ui->pushButtonCopyModbusTest->setEnabled(false);
        
        QMessageBox::information(this, tr("提示"), tr("已清空所有Modbus测试用例"));
    }
}

void MainWindow::on_pushButtonClearTests_clicked()
{
    // 确认是否清空测试用例
    QMessageBox::StandardButton reply = QMessageBox::question(this, tr("确认"),
                                                           tr("确定要清空所有UART测试用例吗？"),
                                                           QMessageBox::Yes | QMessageBox::No);
    if (reply == QMessageBox::Yes) {
        // 清空表格
        ui->tableWidgetTests->setRowCount(0);
        
        // 更新UI状态
        ui->pushButtonRemoveTest->setEnabled(false);
        ui->pushButtonCopyTest->setEnabled(false);
        
        QMessageBox::information(this, tr("提示"), tr("已清空所有UART测试用例"));
    }
}

void MainWindow::on_pushButtonGenerateModbusReport_clicked()
{
    // 检查是否有测试用例
    if (ui->tableWidgetModbusTests->rowCount() == 0) {
        QMessageBox::warning(this, tr("警告"), tr("没有测试用例，无法生成报告"));
        return;
    }
    
    // 选择保存报告的文件
    QString fileName = QFileDialog::getSaveFileName(this, tr("保存测试报告"), 
                                                 QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss") + "_ModbusTestReport",
                                                 tr("HTML文件 (*.html)"));
    if (fileName.isEmpty()) {
        return;
    }
    
    // 生成HTML报告内容
    QString htmlContent = "<!DOCTYPE html>\n"
                         "<html>\n"
                         "<head>\n"
                         "<meta charset=\"UTF-8\">\n"
                         "<title>Modbus测试报告</title>\n"
                         "<style>\n"
                         "body { font-family: Arial, sans-serif; margin: 20px; }\n"
                         "h1 { color: #333366; }\n"
                         "table { border-collapse: collapse; width: 100%; margin-top: 20px; }\n"
                         "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n"
                         "th { background-color: #f2f2f2; }\n"
                         "tr:nth-child(even) { background-color: #f9f9f9; }\n"
                         ".pass { background-color: #dff0d8; color: #3c763d; }\n"
                         ".fail { background-color: #f2dede; color: #a94442; }\n"
                         ".notested { background-color: #fcf8e3; color: #8a6d3b; }\n"
                         "</style>\n"
                         "</head>\n"
                         "<body>\n"
                         "<h1>Modbus测试报告</h1>\n"
                         "<p>生成时间: " + QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss") + "</p>\n"
                         "<table>\n"
                         "<tr>\n"
                         "<th>序号</th>\n"
                         "<th>测试描述</th>\n"
                         "<th>站号</th>\n"
                         "<th>功能码</th>\n"
                         "<th>发送数据</th>\n"
                         "<th>期望响应</th>\n"
                         "<th>实际响应</th>\n"
                         "<th>测试结果</th>\n"
                         "</tr>\n";
    
    // 添加测试用例数据
    int passCount = 0;
    int failCount = 0;
    int notTestedCount = 0;
    
    for (int row = 0; row < ui->tableWidgetModbusTests->rowCount(); ++row) {
        QString result = ui->tableWidgetModbusTests->item(row, 7)->text();
        QString resultClass = "notested";
        
        if (result == tr("通过")) {
            resultClass = "pass";
            passCount++;
        } else if (result == tr("失败")) {
            resultClass = "fail";
            failCount++;
        } else {
            notTestedCount++;
        }
        
        htmlContent += "<tr>\n"
                      "<td>" + QString::number(row + 1) + "</td>\n"
                      "<td>" + ui->tableWidgetModbusTests->item(row, 1)->text() + "</td>\n"
                      "<td>" + ui->tableWidgetModbusTests->item(row, 2)->text() + "</td>\n"
                      "<td>" + ui->tableWidgetModbusTests->item(row, 3)->text() + "</td>\n"
                      "<td>" + ui->tableWidgetModbusTests->item(row, 4)->text() + "</td>\n"
                      "<td>" + ui->tableWidgetModbusTests->item(row, 5)->text() + "</td>\n"
                      "<td>" + (ui->tableWidgetModbusTests->item(row, 6) ? ui->tableWidgetModbusTests->item(row, 6)->text() : "") + "</td>\n"
                      "<td class=\"" + resultClass + "\">" + result + "</td>\n"
                      "</tr>\n";
    }
    
    // 添加统计信息
    htmlContent += "</table>\n"
                  "<h2>测试统计</h2>\n"
                  "<p>总测试用例数: " + QString::number(ui->tableWidgetModbusTests->rowCount()) + "</p>\n"
                  "<p>通过: " + QString::number(passCount) + " (" + 
                  QString::number(passCount * 100.0 / ui->tableWidgetModbusTests->rowCount(), 'f', 2) + "%)</p>\n"
                  "<p>失败: " + QString::number(failCount) + " (" + 
                  QString::number(failCount * 100.0 / ui->tableWidgetModbusTests->rowCount(), 'f', 2) + "%)</p>\n"
                  "<p>未测试: " + QString::number(notTestedCount) + " (" + 
                  QString::number(notTestedCount * 100.0 / ui->tableWidgetModbusTests->rowCount(), 'f', 2) + "%)</p>\n"
                  "</body>\n"
                  "</html>";
    
    // 保存HTML文件
    QFile file(fileName);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream stream(&file);
        //stream.setCodec("UTF-8");
        stream << htmlContent;
        file.close();
        
        QMessageBox::information(this, tr("成功"), tr("测试报告已成功保存到: %1").arg(fileName));
    } else {
        QMessageBox::critical(this, tr("错误"), tr("无法保存测试报告: %1").arg(file.errorString()));
    }
}

void MainWindow::on_tableWidgetModbusTests_itemSelectionChanged()
{
    // 获取当前选中的行
    int row = ui->tableWidgetModbusTests->currentRow();
    if (row >= 0 && row < testCases.size()) {
        // 更新按钮状态
        ui->pushButtonRemoveModbusTest->setEnabled(true);
        ui->pushButtonCopyModbusTest->setEnabled(true);
    } else {
        ui->pushButtonRemoveModbusTest->setEnabled(false);
        ui->pushButtonCopyModbusTest->setEnabled(false);
    }
}

void MainWindow::on_tableWidgetModbusTests_itemChanged(QTableWidgetItem *item)
{
    if (!item) return;
    
    int row = item->row();
    int col = item->column();
    
    // 确保行号有效
    if (row < 0 || row >= testCases.size()) return;
    
    // 根据列号更新测试用例的相应字段
    bool commandChanged = false;
    switch (col) {
    case 1: // 测试描述
        testCases[row].description = item->text();
        break;
    case 2: // 站号
        testCases[row].stationId = item->text().toInt();
        commandChanged = true;
        break;
    case 3: // 功能码
        testCases[row].functionCode = item->text().toInt();
        commandChanged = true;
        break;
    case 4: // 发送数据 (原始数据部分)
        testCases[row].sendData = item->text();
        commandChanged = true;
        break;
    case 5: // 期望响应
        testCases[row].expectedResponse = item->text();
        break;
    case 7: // 测试结果
        testCases[row].result = item->text();
        break;
    default:
        break;
    }

    // 如果命令相关字段（站号、功能码、发送数据）发生变化，则更新 sentDataA 和 CRC16
    if (commandChanged) {
        // 重新构建 sentDataA
        // 注意：这里的实现方式需要与添加测试用例时的逻辑保持一致
        // 确保站号和功能码是两位十六进制数
        QString stationIdHex = QString("%1").arg(testCases[row].stationId, 2, 16, QChar('0')).toUpper();
        QString functionCodeHex = QString("%1").arg(testCases[row].functionCode, 2, 16, QChar('0')).toUpper();
        
        // 清理sendData中的额外空格，并确保其为十六进制字符串
        QStringList sendDataParts = testCases[row].sendData.split(' ', Qt::SkipEmptyParts);
        QString cleanedSendData = sendDataParts.join(' ');

        testCases[row].sentDataA = QString("%1 %2 %3").arg(stationIdHex).arg(functionCodeHex).arg(cleanedSendData).trimmed();

        // 重新计算 CRC16
        QByteArray dataForCrc;
        QStringList hexBytes = testCases[row].sentDataA.split(' ');
        for (const QString &byteStr : hexBytes) {
            bool ok;
            dataForCrc.append(static_cast<char>(byteStr.toInt(&ok, 16)));
        }
        if (!dataForCrc.isEmpty()) {
            quint16 crc_val = CrcUtils::calculateCRC16(dataForCrc);
            testCases[row].crc16 = QString("%1%2").arg((crc_val & 0xFF), 2, 16, QChar('0')).arg(((crc_val >> 8) & 0xFF), 2, 16, QChar('0')).toUpper();
        } else {
            testCases[row].crc16 = "";
        }
        
        // 更新表格中 sentDataA (如果显示) 和 CRC16 列
        // 假设 sentDataA 在第6列，CRC16 在第8列 (根据UI调整)
        // 注意：直接修改表格项可能会再次触发 itemChanged 信号，需要小心处理或临时断开信号槽连接
        // 一个更安全的方法是调用 updateModbusTableRow 来刷新整行
        updateModbusTableRow(row, testCases[row]); 
    }
}

// 添加测试用例
void MainWindow::on_pushButtonAddTest_clicked()
{
    // 保存当前编辑框中的值
    QString sentDataA = ui->lineEditSendDataA->text(); // 使用正确的控件名称
    QString expectReceiveA = ""; // 没有对应的期望接收控件，设为空字符串
    
    int row = ui->tableWidgetTests->rowCount();
    ui->tableWidgetTests->insertRow(row);
    
    // 创建新的测试用例并填充默认值
    ModbusTestCase testCase;
    testCase.description = QString("测试用例 %1").arg(row + 1);
    testCase.stationId = 01; // 默认站号为1
    testCase.functionCode = 03; // 默认读保持寄存器
    testCase.sendData = sentDataA; // 使用当前编辑框中的值
    testCase.expectedResponse = expectReceiveA; // 使用当前编辑框中的值
    testCase.timeout = 1000; // 默认1秒超时
    testCase.crcType = "CRC16";
    testCase.result = "未测试";
    
    // 添加到测试用例列表
    testCases.append(testCase);
    
    // 更新表格行
    updateTableRow(row, testCase);
    
    // 选择新添加的行
    ui->tableWidgetTests->selectRow(row);
    
    // 启用相关按钮
    ui->pushButtonCopyTest->setEnabled(true);
    ui->pushButtonRemoveTest->setEnabled(true);
}

// 移除测试用例
void MainWindow::on_pushButtonRemoveTest_clicked()
{
    int row = ui->tableWidgetTests->currentRow();
    if (row >= 0) {
        // 从测试用例列表中移除
        if (row < testCases.size()) {
            testCases.removeAt(row);
        }
        
        ui->tableWidgetTests->removeRow(row);
        
        // 如果删除后没有测试用例，禁用相关按钮
        if (ui->tableWidgetTests->rowCount() == 0) {
            // 注意：UI中不存在这些按钮，暂时注释掉
            // ui->pushButtonCopyTest->setEnabled(false);
            // ui->pushButtonRemoveTest->setEnabled(false);
        }
    }
}

// 复制测试用例
void MainWindow::on_pushButtonCopyTest_clicked()
{
    int row = ui->tableWidgetTests->currentRow();
    if (row >= 0 && row < testCases.size()) {
        // 获取当前选中的测试用例
        ModbusTestCase testCase = testCases[row];
        testCase.description = testCase.description + " (复制)";
        testCase.result = "未测试";
        
        // 添加到测试用例列表
        testCases.append(testCase);
        
        // 添加新行
        int newRow = ui->tableWidgetTests->rowCount();
        ui->tableWidgetTests->insertRow(newRow);
        updateTableRow(newRow, testCase);
        
        // 选择新添加的行
        ui->tableWidgetTests->selectRow(newRow);
    }
}

// 更新测试用例表格
void MainWindow::updateTestCaseTable()
{
    // 清空当前表格
    ui->tableWidgetTests->setRowCount(0);
    
    // 添加测试用例到表格
    for (int i = 0; i < testCases.size(); ++i) {
        int row = ui->tableWidgetTests->rowCount();
        ui->tableWidgetTests->insertRow(row);
        updateTableRow(row, testCases[i]);
    }
}

// 导入测试配置
void MainWindow::on_pushButtonImport_clicked()
{
    try {
        QString fileName = QFileDialog::getOpenFileName(this, tr("导入UART配置"), "", tr("JSON文件 (*.json)"));
        if(fileName.isEmpty()) {
            return;
        }

        QFile file(fileName);
        if(!file.open(QIODevice::ReadOnly)) {
            QMessageBox::warning(this, tr("错误"), tr("无法打开文件: %1").arg(file.errorString()));
            return;
        }

        QByteArray data = file.readAll();
        QJsonDocument doc = QJsonDocument::fromJson(data);
        if(doc.isNull()) {
            QMessageBox::warning(this, tr("错误"), tr("无效的JSON文件格式"));
            return;
        }
        
        // 清空当前表格和测试用例列表
        ui->tableWidgetTests->setRowCount(0);
        testCases.clear();
        
        // 处理两种可能的JSON格式：数组格式或MappingWindow的对象格式
        if(doc.isArray()) {
            // 原始格式：直接是测试用例数组
            QJsonArray array = doc.array();
            importTestCasesFromArray(array);
        } else if(doc.isObject()) {
            // MappingWindow格式：包含mapping和testSteps两个键的对象
            QJsonObject rootObj = doc.object();
            
            // 检查是否包含testSteps键
            if(rootObj.contains("testSteps")) {
                QJsonArray testStepsArray = rootObj["testSteps"].toArray();
                importTestStepsFromMappingWindow(testStepsArray);
            } else {
                QMessageBox::warning(this, tr("错误"), tr("JSON文件中未找到测试步骤数据"));
                return;
            }
        } else {
            QMessageBox::warning(this, tr("错误"), tr("不支持的JSON文件格式"));
            return;
        }
        
        QMessageBox::information(this, tr("导入成功"), tr("成功导入测试配置"));
    } catch (const std::exception& e) {
        qDebug() << "导入配置异常: " << e.what();
        QMessageBox::critical(this, tr("错误"), tr("导入配置时发生错误: %1").arg(e.what()));
    } catch (...) {
        qDebug() << "导入配置发生未知异常";
        QMessageBox::critical(this, tr("错误"), tr("导入配置时发生未知错误"));
    }
}

// 从标准测试用例数组导入
void MainWindow::importTestCasesFromArray(const QJsonArray &array)
{
    for(int i = 0; i < array.size(); i++) {
        QJsonObject obj = array[i].toObject();
        
        ModbusTestCase testCase;
        testCase.description = obj["description"].toString();
        
        // 尝试从不同格式的JSON中获取数据
        if(obj.contains("stationId")) {
            // Modbus格式
            testCase.stationId = obj["stationId"].toInt(1);
            testCase.functionCode = obj["functionCode"].toInt(3);
            testCase.sendData = obj["sendData"].toString();
            testCase.expectedResponse = obj["expectedResponse"].toString();
        } else if(obj.contains("sentDataA") || obj.contains("sentDataB")) {
            // TestConfig格式 - 直接兼容UART配置界面
            // 检查是否有串口B数据
            if(obj.contains("sentDataB") && !obj["sentDataB"].toString().isEmpty()) {
                // 这是串口B的数据
                QString sentDataB = obj["sentDataB"].toString();
                // 添加[B]标记表示这是B串口的数据
                testCase.description = "[B]" + testCase.description;
                testCase.sendData = sentDataB;
                testCase.expectedResponse = obj["expectReceiveB"].toString();
            } else {
                // 这是串口A的数据
                QString sentDataA = obj["sentDataA"].toString();
                // 尝试从sentDataA中提取站号和功能码
                QRegularExpression regex("站号:(\\d+)\\s*功能码:(\\d+)\\s*数据:(.+)");
                QRegularExpressionMatch match = regex.match(sentDataA);
                
                if (match.hasMatch()) {
                    testCase.stationId = match.captured(1).toInt();
                    testCase.functionCode = match.captured(2).toInt();
                    testCase.sendData = match.captured(3);
                } else {
                    // 如果无法解析，使用默认值
                    testCase.stationId = 01; // 默认值
                    testCase.functionCode = 03; // 默认值
                    testCase.sendData = sentDataA;
                }
                
                testCase.expectedResponse = obj["expectReceiveA"].toString();
            }
        }
        
        testCase.timeout = obj["timeout"].toInt(1000);
        testCase.crcType = obj["crcType"].toString("CRC16");
        testCase.result = "未测试";
        
        // 添加到测试用例列表
        testCases.append(testCase);
        
        // 添加到表格
        int row = ui->tableWidgetTests->rowCount();
        ui->tableWidgetTests->insertRow(row);
        updateTableRow(row, testCase);
    }
}

// 从MappingWindow的测试步骤数组导入
void MainWindow::importTestStepsFromMappingWindow(const QJsonArray &testStepsArray)
{
    for(int i = 0; i < testStepsArray.size(); i++) {
        QJsonObject stepObj = testStepsArray[i].toObject();
        
        // 跳过info类型的步骤
        QString type = stepObj["type"].toString();
        if(type == "info") continue;
        
        ModbusTestCase testCase;
        testCase.description = stepObj["notes"].toString();
        testCase.stationId = 01; // 默认值
        testCase.functionCode = 03; // 默认值
        
        // 根据步骤类型设置发送和接收数据
        // 支持多种类型变体：controller, controller_coma, protection, protection_comb等
        if(type == "controller" || type == "controller_coma" || type.startsWith("controller")) {
            // 控制器步骤使用串口A
            testCase.sendData = stepObj["sendData"].toString();
            testCase.expectedResponse = stepObj["expectReceiveData"].toString();
        } else if(type == "protection" || type == "protection_comb" || type.startsWith("protection")) {
            // 保护装置步骤使用串口B - 在新的表格结构中，这些数据会放在B串口相关列
            // 由于ModbusTestCase结构体限制，我们仍然使用sendData和expectedResponse字段
            // 但在updateTableRow中会正确显示到B串口相关列
            testCase.sendData = stepObj["sendData"].toString();
            testCase.expectedResponse = stepObj["expectReceiveData"].toString();
            
            // 添加一个标记，表示这是B串口的数据
            testCase.description = "[B]" + testCase.description;
        } else {
            // 对于其他未知类型，默认作为串口A处理
            testCase.sendData = stepObj["sendData"].toString();
            testCase.expectedResponse = stepObj["expectReceiveData"].toString();
        }
        
        testCase.timeout = 1000; // 默认值
        testCase.crcType = "CRC16"; // 默认值
        testCase.result = "未测试";
        
        // 添加到测试用例列表
        testCases.append(testCase);
        
        // 添加到表格
        int row = ui->tableWidgetTests->rowCount();
        ui->tableWidgetTests->insertRow(row);
        updateTableRow(row, testCase);
    }
}

// 导出测试配置
void MainWindow::on_pushButtonExport_clicked()
{
    if (testCases.isEmpty()) {
        QMessageBox::warning(this, "导出警告", "没有测试用例可以导出");
        return;
    }
    
    QString fileName = QFileDialog::getSaveFileName(this, "导出Modbus测试配置", "", "JSON文件 (*.json)");
    if (fileName.isEmpty()) {
        return;
    }
    
    // 收集所有测试用例
    QJsonArray testCasesArray;
    for (const ModbusTestCase &testCase : testCases) {
        QJsonObject testObj;
        testObj["description"] = testCase.description;
        testObj["stationId"] = testCase.stationId;
        testObj["functionCode"] = testCase.functionCode;
        testObj["sendData"] = testCase.sendData;
        testObj["expectedResponse"] = testCase.expectedResponse;
        testObj["timeout"] = testCase.timeout; // 使用测试用例中的超时时间
        testObj["crcType"] = testCase.crcType;
        
        testCasesArray.append(testObj);
    }
    
    QJsonDocument doc(testCasesArray);
    QByteArray jsonData = doc.toJson(QJsonDocument::Indented);
    
    QFile file(fileName);
    if (!file.open(QIODevice::WriteOnly)) {
        QMessageBox::warning(this, "导出错误", "无法写入文件：" + file.errorString());
        return;
    }
    
    file.write(jsonData);
    file.close();
    
    QMessageBox::information(this, "导出成功", QString("成功导出 %1 个测试用例").arg(ui->tableWidgetTests->rowCount()));
}

// 表格选择变更
void MainWindow::on_tableWidgetTests_itemSelectionChanged()
{
    int row = ui->tableWidgetTests->currentRow();
    if (row >= 0) {
        // 启用复制和删除按钮
        // 注意：UI中不存在这些按钮，暂时注释掉
        // ui->pushButtonCopyTest->setEnabled(true);
        // ui->pushButtonRemoveTest->setEnabled(true);
        
        // 更新编辑区域
        // 串口A发送数据在第2列 (索引从0开始)
        QTableWidgetItem *sentAItem = ui->tableWidgetTests->item(row, 2);
        if (sentAItem) {
            ui->lineEditSendDataA->setText(sentAItem->text());
        }
        // 串口B发送数据在第5列
        QTableWidgetItem *sentBItem = ui->tableWidgetTests->item(row, 5);
        if (sentBItem) {
            ui->lineEditSendDataB->setText(sentBItem->text());
        }
    } else {
        // 禁用复制和删除按钮
        // 注意：UI中不存在这些按钮，暂时注释掉
        // ui->pushButtonCopyTest->setEnabled(false);
        // ui->pushButtonRemoveTest->setEnabled(false);
    }
}

// 表格内容变更
void MainWindow::on_tableWidgetTests_itemChanged(QTableWidgetItem *item)
{
    // 更新测试用例数据
    Q_UNUSED(item); // 添加未使用参数标记，消除警告
}

// Slot for CRC16 checkbox toggled
void MainWindow::on_checkBoxUseCRC16_toggled(bool checked)
{
    // Iterate over all rows in the table
    for (int row = 0; row < ui->tableWidgetTests->rowCount(); ++row) {
        // Update for Port A
        QTableWidgetItem *sentAItem = ui->tableWidgetTests->item(row, 2); // Column for Port A Send
        if (sentAItem) {
            QString currentTextInCellA = sentAItem->text().trimmed();
            QString baseHexDataA = currentTextInCellA;

            // Try to strip a *valid* CRC from the end of currentTextInCellA
            if (currentTextInCellA.length() >= 6) {
                QString suffixA = currentTextInCellA.right(6);
                if (suffixA[0].isSpace() &&
                    std::isxdigit(suffixA[1].toLatin1()) && std::isxdigit(suffixA[2].toLatin1()) &&
                    suffixA[3].isSpace() &&
                    std::isxdigit(suffixA[4].toLatin1()) && std::isxdigit(suffixA[5].toLatin1())) {
                    
                    bool hex1OkA, hex2OkA;
                    ushort crcByte1A = suffixA.mid(1, 2).toUShort(&hex1OkA, 16);
                    ushort crcByte2A = suffixA.mid(4, 2).toUShort(&hex2OkA, 16);

                    if (hex1OkA && hex2OkA) {
                        QString potentialDataPartA = currentTextInCellA.left(currentTextInCellA.length() - 6).trimmed();
                        if (!potentialDataPartA.isEmpty()) {
                            QByteArray dataBytesForVerificationA = hexStringToByteArray(potentialDataPartA);
                            if (!dataBytesForVerificationA.isEmpty()) {
                                quint16 calculatedCrcForPotentialDataPartA = CrcUtils::calculateCRC16(dataBytesForVerificationA);
                                quint16 crcInSuffixA = (static_cast<quint16>(crcByte1A) << 8) | static_cast<quint16>(crcByte2A);
                                if (calculatedCrcForPotentialDataPartA == crcInSuffixA) {
                                    baseHexDataA = potentialDataPartA; // A valid CRC was found and stripped
                                }
                            }
                        } else { // currentTextInCellA was only " XX YY"
                             QByteArray dataBytesForVerificationA = hexStringToByteArray(potentialDataPartA); // empty
                             quint16 calculatedCrcForPotentialDataPartA = CrcUtils::calculateCRC16(dataBytesForVerificationA);
                             quint16 crcInSuffixA = (static_cast<quint16>(crcByte1A) << 8) | static_cast<quint16>(crcByte2A);
                             if (calculatedCrcForPotentialDataPartA == crcInSuffixA) { // e.g. CRC of empty string
                                 baseHexDataA = potentialDataPartA;
                             }
                        }
                    }
                }
            }

            if (checked) { // Checkbox is being turned ON or is ON
                if (baseHexDataA.isEmpty()) {
                    sentAItem->setText(baseHexDataA); // Set to empty if base data is empty
                } else {
                    QByteArray dataBytesA = hexStringToByteArray(baseHexDataA);
                    // calculateCRC16 should handle empty QByteArray correctly, but we prevent calling it if baseHexDataA is empty
                    quint16 crcA = CrcUtils::calculateCRC16(dataBytesA);
                    QString crcHexA = QString(" %1 %2")
                                     .arg(((crcA >> 8) & 0xFF), 2, 16, QChar('0'))
                                     .arg((crcA & 0xFF), 2, 16, QChar('0'))
                                     .toUpper();
                    sentAItem->setText(baseHexDataA + crcHexA);
                }
            } else { // Checkbox is being turned OFF
                sentAItem->setText(baseHexDataA); // Display the data part (CRC stripped if valid, else original)
            }
        }

    // Update for Port B
    QTableWidgetItem *sentBItem = ui->tableWidgetTests->item(row, 5); // Column for Port B Send
    if (sentBItem) {
        QString currentTextInCellB = sentBItem->text().trimmed();
        QString baseHexDataB = currentTextInCellB;

        // Try to strip a *valid* CRC from the end of currentTextInCellB
        if (currentTextInCellB.length() >= 6) {
            QString suffixB = currentTextInCellB.right(6);
            if (suffixB[0].isSpace() &&
                std::isxdigit(suffixB[1].toLatin1()) && std::isxdigit(suffixB[2].toLatin1()) &&
                suffixB[3].isSpace() &&
                std::isxdigit(suffixB[4].toLatin1()) && std::isxdigit(suffixB[5].toLatin1())) {
                
                bool hex1OkB, hex2OkB;
                ushort crcByte1B = suffixB.mid(1, 2).toUShort(&hex1OkB, 16);
                ushort crcByte2B = suffixB.mid(4, 2).toUShort(&hex2OkB, 16);

                if (hex1OkB && hex2OkB) {
                    QString potentialDataPartB = currentTextInCellB.left(currentTextInCellB.length() - 6).trimmed();
                    if (!potentialDataPartB.isEmpty()) {
                        QByteArray dataBytesForVerificationB = hexStringToByteArray(potentialDataPartB);
                        if (!dataBytesForVerificationB.isEmpty()) {
                            quint16 calculatedCrcForPotentialDataPartB = CrcUtils::calculateCRC16(dataBytesForVerificationB);
                            quint16 crcInSuffixB = (static_cast<quint16>(crcByte1B) << 8) | static_cast<quint16>(crcByte2B);
                            if (calculatedCrcForPotentialDataPartB == crcInSuffixB) {
                                baseHexDataB = potentialDataPartB; // A valid CRC was found and stripped
                            }
                        }
                    } else { // currentTextInCellB was only " XX YY"
                        QByteArray dataBytesForVerificationB = hexStringToByteArray(potentialDataPartB); // empty
                        quint16 calculatedCrcForPotentialDataPartB = CrcUtils::calculateCRC16(dataBytesForVerificationB);
                        quint16 crcInSuffixB = (static_cast<quint16>(crcByte1B) << 8) | static_cast<quint16>(crcByte2B);
                        if (calculatedCrcForPotentialDataPartB == crcInSuffixB) { // e.g. CRC of empty string
                            baseHexDataB = potentialDataPartB;
                        }
                    }
                }
            }
        }

        if (checked) { // Checkbox is being turned ON or is ON
            if (baseHexDataB.isEmpty()) {
                sentBItem->setText(baseHexDataB); // Set to empty if base data is empty
            } else {
                QByteArray dataBytesB = hexStringToByteArray(baseHexDataB);
                quint16 crcB = CrcUtils::calculateCRC16(dataBytesB);
                QString crcHexB = QString(" %1 %2")
                                 .arg(((crcB >> 8) & 0xFF), 2, 16, QChar('0'))
                                 .arg((crcB & 0xFF), 2, 16, QChar('0'))
                                 .toUpper();
                sentBItem->setText(baseHexDataB + crcHexB);
            }
        } else { // Checkbox is being turned OFF
            sentBItem->setText(baseHexDataB);
        }
    }
    }

    // Update the line edits based on the currently selected row, if any
    int currentRow = ui->tableWidgetTests->currentRow();
    if (currentRow >= 0) {
        QTableWidgetItem *currentSentAItem = ui->tableWidgetTests->item(currentRow, 2);
        if (currentSentAItem) {
            ui->lineEditSendDataA->setText(currentSentAItem->text());
        }
        QTableWidgetItem *currentSentBItem = ui->tableWidgetTests->item(currentRow, 5);
        if (currentSentBItem) {
            ui->lineEditSendDataB->setText(currentSentBItem->text());
        }
    }
}

// 实现缺少的函数
void MainWindow::on_pushButtonReadB_clicked()
{
    // 读取串口B数据
    if (m_serialManager->isConnected("B")) {
        // 读取操作
        QMessageBox::information(this, tr("读取"), tr("读取串口B数据"));
    } else {
        QMessageBox::warning(this, tr("错误"), tr("串口B未连接"));
    }
}

void MainWindow::on_pushButtonWriteB_clicked()
{
    // 写入串口B数据
    if (m_serialManager->isConnected("B")) {
        // 写入操作
        QMessageBox::information(this, tr("写入"), tr("写入串口B数据"));
    } else {
        QMessageBox::warning(this, tr("错误"), tr("串口B未连接"));
    }
}

// 描述文本变更 - 使用空实现，因为控件不存在
void MainWindow::on_lineEditDescription_textChanged(const QString &text)
{
    Q_UNUSED(text); // 添加未使用参数标记，消除警告
    // 原实现已移除，因为控件不存在
}

// 站号变更 - 使用空实现，因为控件不存在
void MainWindow::on_spinBoxStationA_valueChanged(int value)
{
    Q_UNUSED(value); // 添加未使用参数标记，消除警告
    // 原实现已移除，因为控件不存在
}

// 发送数据变更 - 使用空实现，因为控件不存在
void MainWindow::on_lineEditSentA_textChanged(const QString &text)
{
    Q_UNUSED(text); // 添加未使用参数标记，消除警告
    // 原实现已移除，因为控件不存在
}

// 期望接收数据变更 - 使用空实现，因为控件不存在
void MainWindow::on_lineEditHopeReceiveA_textChanged(const QString &text)
{
    Q_UNUSED(text); // 添加未使用参数标记，消除警告
    // 原实现已移除，因为控件不存在
}

// 自动读取B串口数据复选框点击事件
void MainWindow::on_checkBoxAutoB_clicked(bool checked)
{
    // 自动读取B串口数据
    if (checked) {
        if (m_serialManager->isConnected("B")) {
            // 如果定时器不存在，创建一个
            if (!timerAutoReadB) {
                timerAutoReadB = new QTimer(this);
                connect(timerAutoReadB, &QTimer::timeout, this, [this]() {
                    // 定时读取操作
                    if (m_serialManager->isConnected("B")) {
                        // 执行读取操作
                    }
                });
            }
            
            // 启动定时器，每秒读取一次
            timerAutoReadB->start(1000);
        } else {
            // 如果串口未连接，取消选中状态
            QMessageBox::warning(this, tr("错误"), tr("串口B未连接，无法启动自动读取"));
            // 这里不能直接访问checkBoxAutoB，因为它不存在
        }
    } else {
        // 停止定时器
        if (timerAutoReadB && timerAutoReadB->isActive()) {
            timerAutoReadB->stop();
        }
    }
}

// 开始测试
void MainWindow::on_pushButtonTest_clicked()
{
    if (ui->tableWidgetTests->rowCount() == 0) {
        QMessageBox::warning(this, "测试警告", "没有测试用例可以执行");
        return;
    }
    
    if (!m_serialManager->isConnected("A") || !m_serialManager->isConnected("B")) {
        QMessageBox::warning(this, "测试警告", "请确保串口A和串口B都已连接");
        return;
    }
    
    if (m_testController->isRunning()) {
        m_testController->stopTests();
    } else {
        m_testController->setTestCases(testCases);
        m_testController->startTests(ui->spinBoxDelay->value());
        ui->pushButtonTest->setText(tr("停止测试"));
    }
}

void MainWindow::handleTestCaseFinished(int index, const ModbusTestCase &testCase, bool passed)
{
    // This function is called when the TestController finishes a test case.
    // We need to update the UI accordingly.
    
    // Find the correct table to update
    QTableWidget* table = ui->tableWidgetModbusTests; // Assume Modbus tests for now
    // A more robust solution would be to know which test list is active
    
    if (index < table->rowCount()) {
        // Update the result column
        QTableWidgetItem* resultItem = table->item(index, 8);
        if (!resultItem) {
            resultItem = new QTableWidgetItem();
            table->setItem(index, 8, resultItem);
        }
        resultItem->setText(testCase.result);
        resultItem->setBackground(passed ? QColor(211, 235, 211) : QColor(255, 224, 224));

        // Update actual received data
        QTableWidgetItem* actualAItem = table->item(index, 4); // Adjust column index if necessary
        if (!actualAItem) {
            actualAItem = new QTableWidgetItem();
            table->setItem(index, 4, actualAItem);
        }
        actualAItem->setText(testCase.actualReceiveA);

        QTableWidgetItem* actualBItem = table->item(index, 7); // Adjust column index if necessary
        if (!actualBItem) {
            actualBItem = new QTableWidgetItem();
            table->setItem(index, 7, actualBItem);
        }
        actualBItem->setText(testCase.actualReceiveB);
    }
}

void MainWindow::handleAllTestsFinished()
{
    ui->pushButtonTest->setText(tr("开始测试"));
    ui->pushButtonModbusTest->setText(tr("开始Modbus测试"));
    QMessageBox::information(this, "测试完成", "所有测试用例已执行完毕");
}

void MainWindow::logTestMessage(const QString &message)
{
    // We can add a new log window or use the status bar.
    ui->statusbar->showMessage(message, 3000);
}

void MainWindow::handleSerialMessage(const QString &portId, const QString &message)
{
    QDateTime currentTime = QDateTime::currentDateTime();
    QString text = currentTime.toString("[hh:mm:ss.zzz] ") + message;

    if (portId == "A") {
        ui->textBrowserA->append(QString("<font color='#0000FF'>%1</font>").arg(text)); // Blue for A received
    } else if (portId == "B") {
        ui->textBrowserB->append(QString("<font color='#FF8C00'>%1</font>").arg(text)); // Dark Orange for B received
    }
}

// 将十六进制字符串转换为字节数组
QByteArray MainWindow::hexStringToByteArray(const QString &hexString)
{
    QByteArray result;
    QString hexStr = hexString.simplified().replace(" ", "");
    
    for (int i = 0; i < hexStr.length(); i += 2) {
        QString byteStr = hexStr.mid(i, 2);
        bool ok;
        char byte = static_cast<char>(byteStr.toInt(&ok, 16));
        if (ok) {
            result.append(byte);
        }
    }
    
    return result;
}

// 将字节数组转换为十六进制字符串
QString MainWindow::byteArrayToHexString(const QByteArray &data)
{
    QString result;
    for (const char &byte : data) {
        result += QString("%1 ").arg(static_cast<quint8>(byte) & 0xFF, 2, 16, QChar('0')).toUpper();
    }
    return result.trimmed();
}

// 计算CRC16校验码 - 使用CrcUtils类
// calculateCRC16 和 calculateCRC16Bytes 函数已移至 CrcUtils 类

// 计算CRC32校验码
QByteArray MainWindow::calculateCRC32(const QByteArray &data)
{
    // 简化实现，实际应使用标准CRC32算法
    quint32 crc = 0xFFFFFFFF;
    for (int i = 0; i < data.size(); ++i) {
        crc ^= static_cast<quint8>(data[i]);
        for (int j = 0; j < 8; ++j) {
            if (crc & 1) {
                crc = (crc >> 1) ^ 0xEDB88320;
            } else {
                crc = crc >> 1;
            }
        }
    }
    crc = ~crc;
    
    QByteArray result;
    result.append(static_cast<char>(crc & 0xFF));
    result.append(static_cast<char>((crc >> 8) & 0xFF));
    result.append(static_cast<char>((crc >> 16) & 0xFF));
    result.append(static_cast<char>((crc >> 24) & 0xFF));
    return result;
}

// 创建Modbus帧
QByteArray MainWindow::createModbusFrame(int stationId, int functionCode, const QByteArray &data, int crcType)
{
    QByteArray frame;
    frame.append(static_cast<char>(stationId));
    frame.append(static_cast<char>(functionCode));
    frame.append(data);
    
    // 添加CRC校验
    // 检查是否需要添加CRC校验，如果crcType为0且CRC16校验框未选中，则不添加校验
    if (crcType == 0 && ui->checkBoxUseCRC16 && !ui->checkBoxUseCRC16->isChecked()) {
        // 不添加CRC校验
    } else if (crcType == 16 || (crcType == 0 && ui->checkBoxUseCRC16 && ui->checkBoxUseCRC16->isChecked())) {
        // 添加CRC16校验
        frame.append(CrcUtils::calculateCRC16Bytes(frame));
    } else {
        // 添加CRC32校验
        frame.append(calculateCRC32(frame));
    }
    
    return frame;
}

void MainWindow::on_pushButtonReadA_clicked()
{
    if (!m_serialManager->isConnected("A")) {
        QMessageBox::warning(this, "错误", "串口A未连接，请先连接串口");
        return;
    }
    
    try {
        // 使用默认值
        int startAddress = 0;
        int count = 1;
        
        // 构建Modbus读取命令 (功能码03 - 读保持寄存器)
        QByteArray data;
        data.append(0x01); // 站号
        data.append(0x03); // 功能码
        data.append((startAddress >> 8) & 0xFF); // 起始地址高字节
        data.append(startAddress & 0xFF); // 起始地址低字节
        data.append((count >> 8) & 0xFF); // 数量高字节
        data.append(count & 0xFF); // 数量低字节
        
        // 发送数据
        if (!m_serialManager->sendData("A", data)) {
            QMessageBox::warning(this, tr("错误"), tr("发送读取命令失败"));
        }
    } catch (const std::exception& e) {
        handleSerialError("A", QString("读取异常: %1").arg(e.what()));
    }
}

void MainWindow::on_pushButtonWriteA_clicked()
{
    if (!m_serialManager->isConnected("A")) {
        QMessageBox::warning(this, "错误", "串口A未连接，请先连接串口");
        return;
    }
    
    try {
        // 使用默认值
        int startAddress = 0;
        int value = 0;
        
        // 构建Modbus写入命令 (功能码06 - 写单个寄存器)
        QByteArray data;
        data.append(0x01); // 站号
        data.append(0x06); // 功能码
        data.append((startAddress >> 8) & 0xFF); // 起始地址高字节
        data.append(startAddress & 0xFF); // 起始地址低字节
        data.append((value >> 8) & 0xFF); // 值高字节
        data.append(value & 0xFF); // 值低字节
        
        // 发送数据
        if (!m_serialManager->sendData("A", data)) {
            QMessageBox::warning(this, tr("错误"), tr("发送写入命令失败"));
        }
    } catch (const std::exception& e) {
        handleSerialError("A", QString("写入异常: %1").arg(e.what()));
    }
}

// 串口B读取功能已移除

// 串口B写入功能已移除

void MainWindow::on_checkBoxAutoA_clicked(bool checked)
{
    if (!m_serialManager->isConnected("A")) {
        QMessageBox::warning(this, "错误", "串口A未连接，请先连接串口");
        return;
    }
    
    if (checked) {
        // 创建定时器（如果不存在）
        if (!timerAutoReadA) {
            timerAutoReadA = new QTimer(this);
            connect(timerAutoReadA, &QTimer::timeout, this, [this]() {
                if (m_serialManager->isConnected("A")) {
                    try {
                        // 使用默认值
                        int startAddress = 0;
                        int count = 1;
                        
                        // 构建Modbus读取命令 (功能码03 - 读保持寄存器)
                        QByteArray data;
                        data.append(0x01); // 站号
                        data.append(0x03); // 功能码
                        data.append((startAddress >> 8) & 0xFF); // 起始地址高字节
                        data.append(startAddress & 0xFF); // 起始地址低字节
                        data.append((count >> 8) & 0xFF); // 数量高字节
                        data.append(count & 0xFF); // 数量低字节
                        
                        // 发送数据
                        m_serialManager->sendData("A", data);
                    } catch (const std::exception& e) {
                        handleSerialError("A", QString("自动读取异常: %1").arg(e.what()));
                        timerAutoReadA->stop();
                    }
                } else {
                    timerAutoReadA->stop();
                }
            });
        }
        
        // 启动定时器（每秒读取一次）
        timerAutoReadA->start(1000);
    } else {
        // 停止定时器
        if (timerAutoReadA && timerAutoReadA->isActive()) {
            timerAutoReadA->stop();
        }
    }
}

// 串口B自动读取功能已移除

void MainWindow::handleSerialError(const QString &portId, const QString &error)
{
    QString timeStr = QDateTime::currentDateTime().toString("[hh:mm:ss.zzz]");
    QString text = QString("%1 %2").arg(timeStr, error);
    if (portId == "A") {
        ui->textBrowserA->append(QString("<font color='red'>%1</font>").arg(text));
        ui->statusbar->showMessage("串口A: " + error);
    } else if (portId == "B") {
        ui->textBrowserB->append(QString("<font color='red'>%1</font>").arg(text));
        ui->statusbar->showMessage("串口B: " + error);
    }
}

void MainWindow::handleSerialConnectionChanged(const QString &portId, bool connected)
{
    if (portId == "A") {
        ui->actionConnectA->setEnabled(!connected);
        ui->actionDisconnectA->setEnabled(connected);
        ui->comboBoxPortA->setEnabled(!connected);
        ui->comboBoxBaudA->setEnabled(!connected);
        ui->comboBoxDataA->setEnabled(!connected);
        ui->comboBoxParityA->setEnabled(!connected);
        ui->comboBoxStopA->setEnabled(!connected);
        ui->statusbar->showMessage(connected ? tr("串口A已连接") : tr("串口A已断开"));
    } else if (portId == "B") {
        ui->actionConnectB->setEnabled(!connected);
        ui->actionDisconnectB->setEnabled(connected);
        ui->comboBoxPortB->setEnabled(!connected);
        ui->comboBoxBaudB->setEnabled(!connected);
        ui->comboBoxDataB->setEnabled(!connected);
        ui->comboBoxParityB->setEnabled(!connected);
        ui->comboBoxStopB->setEnabled(!connected);
        ui->statusbar->showMessage(connected ? tr("串口B已连接") : tr("串口B已断开"));
    }
}

QByteArray MainWindow::parseHexString(const QString &hexString)
{
    QByteArray result;
    QString cleanedString = hexString.simplified().remove(' ');
    
    // 确保字符串长度为偶数
    if (cleanedString.length() % 2 != 0) {
        cleanedString.chop(1);
    }
    
    bool ok;
    for (int i = 0; i < cleanedString.length(); i += 2) {
        QString byteStr = cleanedString.mid(i, 2);
        uint8_t byte = byteStr.toUInt(&ok, 16);
        if (ok) {
            result.append(byte);
        }
    }
    
    return result;
}

void MainWindow::on_pushButtonSendA_clicked()
{
    try {
        if (!m_serialManager->isConnected("A")) {
            QMessageBox::warning(this, tr("错误"), tr("串口A未连接"));
            return;
        }
        
        // 获取发送框中的数据并处理
        QString hexString = ui->lineEditSendDataA->text().trimmed();
        if (hexString.isEmpty()) {
            QMessageBox::warning(this, tr("错误"), tr("请输入要发送的数据"));
            return;
        }
        
        QByteArray data = parseHexString(hexString);
        if (data.isEmpty()) {
            QMessageBox::warning(this, tr("错误"), tr("数据格式错误，请输入十六进制数据，例如：01 03 00 00 00 01"));
            return;
        }
        
        // 不再重复添加CRC16校验，因为在选中CRC16校验框时，发送数据栏已经包含了校验码
        // 直接发送数据
        if (!m_serialManager->sendData("A", data)) {
            QMessageBox::warning(this, tr("错误"), tr("发送数据失败"));
        }
    } catch (const std::exception& e) {
        handleSerialError("A", QString("发送数据异常: %1").arg(e.what()));
    }
}

void MainWindow::on_pushButtonSendB_clicked()
{
    try {
        if (!m_serialManager->isConnected("B")) {
            QMessageBox::warning(this, tr("错误"), tr("串口B未连接"));
            return;
        }
        
        // 获取发送框中的数据并处理
        QString hexString = ui->lineEditSendDataB->text().trimmed();
        if (hexString.isEmpty()) {
            QMessageBox::warning(this, tr("错误"), tr("请输入要发送的数据"));
            return;
        }
        
        QByteArray data = parseHexString(hexString);
        if (data.isEmpty()) {
            QMessageBox::warning(this, tr("错误"), tr("数据格式错误，请输入十六进制数据，例如：01 03 00 00 00 01"));
            return;
        }
        
        // 不再重复添加CRC16校验，因为在选中CRC16校验框时，发送数据栏已经包含了校验码
        // 直接发送数据
        if (!m_serialManager->sendData("B", data)) {
            QMessageBox::warning(this, tr("错误"), tr("发送数据失败"));
        }
    } catch (const std::exception& e) {
        handleSerialError("B", QString("发送数据异常: %1").arg(e.what()));
    }
}
