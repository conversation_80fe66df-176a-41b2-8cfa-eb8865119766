MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  d� �vhh �  Q  � & , D   :     %        @                       �    W  `                                             �  �           �             �  x                           �r  (                   ��  p                          .text    D      D                    `.data   �    `      J              @  �.rdata  �   p      L              @  @/4          �      ^              @  �.pdata     �      `              @  @.xdata  �   �      h              @  @.bss    �   �                      �  �.idata  �   �      p              @  @.tls        �      �              @  �.reloc  x    �      �              @  B/14     P          �              @  B/29     b        �              @  B/41     �    0     �              @  B/55     �    @     �              @  B/67     H    P     �              @  B/80     ~    `     �              @  B/91     �   p     �              @  B                                                                                                                                                                                                                                                                                                                                                                                                                                                                                UH��H�MH�UL�E D�M(�]�UH��H�� �T  ���  H��f  � ��t�   �3  �
�   ��2  �N2  H�Gg  ���N2  H�g  ���n  H��e  � ��uH�9g  H���'  �    H�� ]�UH��H��0H�'g  � �w�  H��f  �H�g�  H�D$ A��L�@�  H�1�  H��H�#�  H���O2  �)�  �H��0]�UH��H��0�E��   H��e  �    �=   �E����E�H��0]�UH��H��0�E��   H��e  �     �   �E����E�H��0]�UH��H��pH�E�    �E�0   �E�eH� H�E�H�E�H�@H�E��E�    �!H�E�H;E�u	�E�   �E��  H���  ��H�me  H�E�H�E�H�E�H�E�    H�M�H�E�H�U��H�
H�E�H�}� u�H�Fe  � ��u�   �]1  �?H�,e  � ��u(H�e  �    H�Re  H��H�8e  H���81  �
��     H��d  � ��u&H��d  H��H��d  H���1  H��d  �    �}� uH��d  H�E�H�E�    H�U�H�E�H�H��c  H� H��tH��c  H� A�    �   �    ���d$  H��d  H��H�s�  ��H�2d  H�H����H����/  �+  �	�  H��  ���s  �  H��c  H� H��  H�H�
�  H�ج  �ά  I�ȉ��'  �֬  �Ԭ  ��u
�Ƭ  ���+0  ���  ��u��/  ���  H��p]�UH��H�� H�Yc  �    H�\c  �    H�_c  �    H��b  H�E�H�E�� f=MZt
�    �   H�E��@<Hc�H�E�H�H�E�H�E�� =PE  t
�    �   H�E�H��H�E�H�E�� ��=  t	=  t)�VH�E�@\��w�    �HH�E苀�   �������4H�E�H�E�H�E��@l��w�    �H�E����   ��������    H�� ]�USH��HH�l$@�M H�U(�E ��H�H��H���/  H�E�H�E(H� H�E��E�    �   �E�H�H��    H�E�H�H� H����.  H��H�E��E�H�H��    H�E�H�H�E�H���.  H��E�H�H��    H�E�H�H��E�H�H��    H�E�H�H� H�M�I��H���k.  �E��E�;E �h����E�H�H��    H�E�H�H�     H�E(H�U�H��H��H[]�UH��H�� H�MH�EH����-  H��t�    ������H�� ]Ð��������������ff.�     @ 1��ff.�     f�UWVSH��(H�l$ H�5Z  H���#�  H��H��tkH���2�  H�=�  H��Y  H��H��  ��H��Y  H��H����H��I  H��tH��  H�
�y  ��H�
6   H��([^_]����f�     H�Y���H�5B���H�{I  �f�     UH��H�� H�aI  H��t	H�
Uy  ��H�
��  H��tH�� ]H�%K�   H�� ]Ð�UH��H��@H�Mf�E���H�E�    �RH�U�H�EH���/  � ��f1E��E�    �$�E�����t�E�f��f5�f�E��f�m��E��}�~�H�E�H�EH����.  H9E�����u��E�H��@]�USH��XH�l$PH�M H�U(H�E H���5  H�E�    �`H�E�H�M�H�U(A�   I��H���;  H�E�H���?  A�   �    H���,  �E�H�U�H�E H���i4  H�E�H���  H�E�H�E(H����  H9E�����u��+H��H�E�H���X  �H��H�E H����4  H��H���$*  H�E H��X[]ÐUH��H�� H�MH�EH�� H���  H�EH���  �H�� ]�UAWAVAUATWVSH��  H��$�   �0  H��W  H��H��]  H���  H��H��]  H���  H�}A�   f��GG G0f�G@f�G@��H��H���  H���  ��H���  I��H�tW  H���5  �E�H�G H���  I��H�fW  H���5  �H��H����  �E� �E� H�wHI��f��FF F0f�F@f�F@��H��H���  I��H�W  H���B5  �E�H�F H���  I��H�W  H���!5  �E� �E� H��HI��f��FF F0f�F@f�F@�1H��H���  I��H��V  H����4  �E�H�F H���  I��H��V  H���4  �E� �E� H��HI��f��FF F0f�F@f�F@��H��H���  I��H��V  H���l4  �E�H�N H���  I��H�xV  H���K4  �E� �E� H��HI��f��FF F0f�F@f�F@@AH��H���  I��H�EV  H���4  �E�H�N H���  I��H�1V  H����3  �E� �E� H��HI��f��FF F0f�F@H��H���  I��H��U  H���3  �E�H�N H���  I��H��U  H���{3  �E� �E� H��HA�   H�EI��A�   H���  H���  ��L�u�L�}�H���  H�U�H�E�I��H���(  H���  H���  �H�]H�ð  H�EH9�tH��HH���r�����H���  H����,  �ƅ�  H�E�H���  H���  H���  H�E�H���  H���  H�E��  H�E�H���/  H���  H��T  H��H��Y  H����  H��H���  H����  H��H��T  H����  H��H��Y  H���Y  H��T  H��H��Y  H���  H��H���  H�� H���z  H��H��Y  H���  H���  H�P H�E�H������H�E�H�������f���  H�gT  H��H�-Y  H���=  H��3  H���  H��4  H���  H��H���  �@@�����  H��H��X  H���  H�T  H��H��X  H����
  H��3  H���]  H�~4  H���N  H�����  ���-  H��H��X  H���3  ���  ���  ���  f�����  H��S  H��H�TX  H���d
  H�%3  H����
  H�ù0   ��3  ��H���.
  H�ù   �3  ��H���
  H�����  ���
  H��H�eS  H���
  H��2  H���
  H�ù0   �`3  ��H����  H�ù   �)3  ��H���  H�����  ���>
  H��H��W  H���<
  H���  �@@f9��  �����  H��R  H��H�`W  H���p  H�����   t	H��R  �H��R  H���L  H��H�:W  H����  ���  ����tƅ�   H�E�H���.  H�E�H���  H�U�H�E�H���  �������H�nR  H��H��V  H����  H��H��V  H���b  ���   t-H�RR  H��H��V  H���  H��H��V  H���.  �+H�]R  H��H�kV  H���{  H��H�iV  H���  �    H�E�H���j  ����  H��H���  H���i  �I��H�]H�ð  H�EH9�tH��HH��������L��}� tH�F H���4  �H�À}� tH��H���  �H�À}� tH�F H���  �H�À}� tH��H����
  �H�À}� tH�F H����
  �H�À}� tH��H����
  �H�À}� tH�F H���
  �H�À}� tH��H���
  �H�À}� tH�F H���
  �H�À}� tH��H���j
  �H�À}� tH�F H���S
  �}� tH��H���B
  �H��H���  H���'  �H��H��t0�   L)�H��H��H��H�H��H�H9�tH��HH���������H��H����   H��H�E�H���+  �H��H�E�H���   H��H���   H�Ĉ  [^_A\A]A^A_]ÐUVSH��@H�l$@H�M H��H�H�SH�E�H�U�L�E0H�E H�U0H���  H�E�H���,  H��H�E�H���  H��H�E A��I��H���N  �H��H�E H���  H��H���   �H��@[^]ÐUH��H��@H�MH�EH����  H�UH�RH�MH�	H�M�H�U�H�E�H�U�H�E�H���  �H�EH���$  �H��@]ÐUH��H��0H�MH�UH�E�H���  H�E�H��0]�UH��H��0H�MH�EH�PH�E�H���  H�E�H��0]�USH��(H�l$ H�M H�U(H�E H���w  H�H�E(H���h  H� H9���H��([]�UH��H�MH�EH� H�PHH�EH�H�E]�UH��H�MH�EH� ]ÐUH��H�M�]ÐUH��H��0H�MH�EH�E�H�E�H���������H��0]�UH��H�� H�MH�UH�EH�UH����  �H�� ]ÐUH��H�� H�MH�EH�PH�EH� H)�H��H�9��8��8�H��H��H�EH�H�EI��H����  H�EH���[����H�� ]�UH��H�MH�EH� ]ÐUSH��(H�l$ H�M H�E H�������H��H�E H����  H��H��H��H�H��H�H��([]ÐUSH��XH�l$PH�M H�U(L�E0H�E(H�E�H�E0H�E�H�E�H�E�H�E�H�E�H�E�H+E�H��H��H�9��8��8�H�H�E�H�] H�E H���   H��H�E�H���U  H��H���  H�U H�H�E H�H�U�H��H��H�H��H�H�E H�PH�E H���.   H��H�E L� H�U0H�E(I��H���  H�U H�B�H��X[]ÐUH��H�MH�E]�UH��H�MH�UH�EH�H�EH��]ÐUH��H�MH�E]�UH��H��@H�MH�UH�EH�E�H�EH�E�H�E�H�E�H�E�H�E���H�EH���  �H��@]ÐUH��H��@H�MH�UL�E H�} t0H�EH�E�H�EH�E�H�E H�E�H�M�H�U�H�E�I��H���.  ��H��@]�UH��H�MH�EH�@]�USH��HH�l$@H�M H�U(H�E(H�E�H�E�H�E�H�E�H�E萐H�E�H���	  H;E ��H�E�H����������tH� L  H���  H�E H��H[]ÐUH��H��0H�MH�UH�} t)H�EH�E�H�EH�E�H�U�H�E�A�    H���  ���    H��0]�UH��H�� H�MH�UL�E L�M(H�M H�UH�EI��H���   H�� ]�UH��H�� H�MH�UH�UH�EH����   �H�� ]ÐUH��H�MH�EH�     H�EH�@    H�EH�@    �]�UH��H��PH�MH��q�q�H�E�H�EH�E�H�E�H�E�H�E�H�E�H��q�q���H�E�H�U�H�E�H���)  H� H��P]�UH��H��0H�MH�UL�E �E� �E� H�M H�UH�EI��H����   H��0]�UH��H�� H�MH�U�H�EH���  H���  H�EHH�EH;Euݐ�H�� ]ÐUH��H�� H�MH�UL�E H�U H��H��H�H��H��H�EH���  �H�� ]�UH��H��0H�MH�UL�E H�EH�E�H��q�q�H;E����������tH���8��8�H;Es�j  �}  H�UH��H��H�H��H���  �H��0]�UH��H�� H�MH�UL�E H�M H�UH�EI��H���3   H�� ]�UH��H�MH�E]�UH��H�� H�MH�EH��������H�� ]�USH��8H�l$0H�M H�U(L�E0H�E0H�E��%H�E�H������H��H�E H����   H�E HH�E�HH�E H;E(u�H�E��0H���H  H�U�H�E0H���\����#  H���#  H��H���h  H��8[]ÐUSH��(H�l$ H�M H�U(H�E H�U(H���]  H�E H�� H�U(H�� H���E  H�E(�P@H�E f�P@�H��H�E H���  H��H����  �H��([]�UATWVSH�� H�l$ H�M0H�U8H�]0H�ڹH   �I)  H��A�   H�E8H���7   H��H���I����H��E��tH��H���)  H��H���  �H�� [^_A\]�UH��H�MH�E]Ð���������������%��  ���%�  ���%ڨ  ���%ʨ  ���%��  ���%��  ���%��  ���%��  ���%z�  ���%j�  ���%Z�  ���%B�  ���%2�  ���%"�  ���%�  ���%�  ���%�  ���%�  ���%ҧ  ���%§  ���%��  ���%��  ���%��  ���%��  ���%r�  ���%b�  ���%R�  ���%B�  ���%2�  ���%"�  ���%�  ���%�  ���%�  ���     UH��H�� �H��3  H� ��H��3  H��H��3  H��3  H� H��uӐ�H�� ]�UH��H��0H��I  H� �E��}��u%�E�    ��E��E��PH��I  ��H��H��u�E��E��H��I  �U�H���Ѓm��}� u�H�X���H��������H��0]�UH��H�� �ۓ  ��u�͓     �g����H�� ]ÐUH��    ]Ð����UH��H��0H�M�UL�E H��H  � ��t
H��H  �    �}t#�}uH�M �UH�EI��H���a  �   �FH��T  H�E�H�E��"H�E�H�E�H�E�H� H��t	H�E�H� ��H�E�H�VT  H9E�uѸ   H��0]�UH��H�MH�} u�    ��    ]�UH��H�� H�M�UL�E �}t
�} t�   �H�M �UH�EI��H���  �   H�� ]Ð��������USH��   H�l$P)u )}D)E H�MPH�EP� ��wp��H��    H�	F  �H�H��E  H���H��D  H�E��MH��D  H�E��@H��D  H�E��3H�E  H�E��&H�!E  H�E��H�<E  H�E��H�eE  H�E��H�EP�D@ H�EP�xH�EP�pH�EPH�X�   �,  H��H�E��DD$0�|$(�t$ I��I��H�E  H���  �    (u (}D(E H�Ĉ   []ÐUH����]Ð������USH��8H�l$0H�M H�U(L�E0L�M8H�E(H�E��   �  I��A�   �   H��D  H���&  H�]��   �x  H��H�E I��H���>  ��  �UH��H��`H�M�E�    �   H�
1�  �E�Hc�H��H��H�H��H�H�@H9ErVH�
	�  �E�Hc�H��H��H�H��H�H�HL��  �E�Hc�H��H��H�H��L�H�@ �@��H�H9E�B  �E����  9E��o���H�EH���&  H�E�H�}� uH�EH��H�D  H������H�
s�  �E�Hc�H��H��H�H��H�H�E�H�B H�
L�  �E�Hc�H��H��H�H��H��     �  H��H�E��@A��L��  �E�Hc�H��H��H�H��L�J�	H�PH�
��  �E�Hc�H��H��H�H��H�H�@H�U�A�0   H��H�9�  ��H��u=H�
��  �E�Hc�H��H��H�H��H�H�PH�E��@I�Љ�H�5C  H�������E��@��   �E����   �E�=�   ��   �E����   �E��u	�E�   ��E�@   H�
,�  �E�Hc�H��H��H�H��H�H�E�H�BH�
�  �E�Hc�H��H��H�H��H�H�E�H�BH�
ގ  �E�Hc�H��H��H�H��H�I��H�U�H�E��M�M��A��H��H��  �Ѕ�uH�ġ  �Љ�H�yB  H����������  �����  ��H��`]�UH��H��0�E�    �   H�
V�  �E�Hc�H��H��H�H��Hȋ ����   H�
.�  �E�Hc�H��H��H�H��H�D�H�

�  �E�Hc�H��H��H�H��H�H�HL��  �E�Hc�H��H��H�H��L�H�@H�U�I��E��H��H��H�)�  �����E����  9E��D�����H��0]�UH��H�� H�MH�UL�E H�}  t%H�EH���"���H�M H�UH�EI��H���  ��H�� ]�UH��H�ĀH�MH�UL�E H�EH+EH�E�H�EH�E�H�}��P  H�}�~%H�E�� ��uH�E��@��uH�E��@��uH�E�H�E�� ��uH�E��@��tYH�E�H�E��@H�E�@��H�E H�H�E�H�E��H�E� ЉE�H�E�H�U�A�   H��� ���H�E�H�E�H;Er��  H�E��@��tH�E��@��H�y@  H������H�E�H��H�E��q  H�E��@��H�E H�H�E�H�E�� ��H�E H�H�E�H�E�H� H�E�H�E��@����@��   ��@��   �� tw�� ��   ��t
��t8�   H�E�� ��H�E�H�E�%�   H����   H�E�H
 ���H�E��   H�E�� ��H�E�H�E�% �  H��ttH�E�H
  ��H�E��dH�E�� ��H�E�H�E�%   �H��tMH�E�H�    ����H	�H�E��6H�E�H� H�E��*H�E�    H�E��@����H�u?  H���e��������H�M�H�E�� ��H�E H�H��H)�H�E�H�U�H�E�H�H�E�H�E��@%�   �Eԃ}�?wp�EԺ   ��H��H��H��H�EȋEԃ�H��������H��H��H�E�H�E�H9E�|
H�E�H9E�~+H�U�L�E�H�M��E�H�T$ M��I�ȉ�H��>  H������H�E��@����@tc��@wu�� tA�� wk��t��t�_H�E�H�U�A�   H�������GH�E�H�U�A�   H���|����/H�E�H�U�A�   H���d����H�E�H�U�A�   H���L����H�E�H�E�H;E�������H��]�UH��H��0�Ɖ  ����   ���  �����  �y  �E��E�Hc�H��H��H�H��H��H��H����
  H)�H�D$ H��H��H��H�[�  �Y�      L��>  H��>  H��H��>  H�������������H��]Ð���UH��H��P�MH�U�U �](H��  H��t>�E�E�H�EH�E��E �E��E(�E��E0�E�H�؈  H�E�H������H��P]�UH��H�� H�MH�EH���  H�EH����
  �H�� ]Ð�����������UH��H��0H�M�E�    �E�    H�EH� � %��� =CCG uH�EH� �@����u
�������  H�EH� � =�  ���  =�  �sC=  ���   =  ��p  =  ��\  =  ��Z  =  ��F  =  �t5�C  t��?��
�5  ��H��    H��<  �H�H�}<  H���    �   �a
  H�E�H�}�u�   �   �G
  �E�������   H�}� ��   H�E�   ���E������   �    �   �

  H�E�H�}�u�   �   ��	  �E������   H�}� ��   H�E�   ���E������n�E�   �    �   �	  H�E�H�}�u#�   �   �	  �}� t������E������-H�}� t&H�E�   ���E�������E������
��������}� uH���  H��tH���  H�EH���҉E��E�H��0]Ð������������UH��H��0�MH�U���  ��u�    �{�   �   �  H�E�H�}� u������ZH�E��U�H�E�H�UH�PH�C�  H��H��  ��H�`�  H�E�H�PH�E�H�M�  H��  H��H��  �и    H��0]�UH��H��0�M��  ��u
�    �   H�م  H��H���  ��H�E�    H��  H�E��UH�E�� 9Eu6H�}� uH�E�H�@H�ǅ  �H�E�H�PH�E�H�PH�E�H����  �H�E�H�E�H�E�H�@H�E�H�}� u�H�U�  H��H�C�  �и    H��0]�UH��H��0�X�  ����   H�!�  H��H�ߗ  ��H�>�  H�E��FH�E�� ��H��  ��H�E�H�ė  �Ѕ�uH�}� tH�E�H�PH�E�H����H�E�H�@H�E�H�}� u�H���  H��H���  ����H��0]�UH��H��0H�M�UL�E �}��   �}��   �}��   �}��   �} t3�}��   �v�  ��uH�C�  H��H�)�  ���U�     �}������H�  ��ulH�D�  H�E�� H�E�H�@H�E�H�E�H���N  H�E�H�E�H�}� u�H��      ���      H�˃  H��H���  ���������s������   H��0]Ð��UH��H�� H�MH�EH�E�H�E�� f=MZt�    �NH�E��@<Hc�H�E�H�H�E�H�E�� =PE  t�    �%H�E�H��H�E�H�E�� f=t�    ��   H�� ]�UH��H�� H�MH�UH�E�@<Hc�H�EH�H�E��E�    H�E��@��H�E�H�H��H�E��6H�E��@��H9ErH�E��PH�E��@Љ�H9EsH�E���E�H�E�(H�E��@��9E�r��    H�� ]�UH��H��@H�MH�EH����  H��v
�    �   H�l7  H�E�H�E�H��������u�    �vH�E�@<Hc�H�E�H�H�E��E�    H�E��@��H�E�H�H��H�E��)H�E�H�UA�   H���{  ��uH�E���E�H�E�(H�E��@��9E�rǸ    H��@]�UH��H��0H�MH��6  H�E�H�E�H��������u�    �H�EH+E�H�E�H�U�H�E�H���W���H��0]�UH��H��0H�v6  H�E�H�E�H��������u�    � H�E��@<Hc�H�E�H�H�E�H�E��@��H��0]�UH��H��@H�MH�"6  H�E�H�E�H���b�����u�    �xH�E�@<Hc�H�E�H�H�E��E�    H�E��@��H�E�H�H��H�E��+H�E��@$%    ��tH�} uH�E��#H�m�E�H�E�(H�E��@��9E�rŸ    H��@]�UH��H��0H�z5  H�E�H�E�H��������u�    �H�E�H��0]�UH��H��@H�MH�B5  H�E�H�E�H��������u�    �=H�EH+E�H�E�H�U�H�E�H�������H�E�H�}� u�    �H�E�@$������H��@]�UH��H��P�MH��4  H�E�H�E�H��������u
�    �   H�E��@<Hc�H�E�H�H�E�H�E苀�   �E�}� u�    �|�U�H�E�H���B���H�E�H�}� u�    �[�U�H�E�H�H�E�H�}� u�    �?H�E��@��uH�E��@��t#�} H�E��@��H�E�H���mH�E��ǐ�    H��P]Ð���%��  ���     QPH=   H�L$rH��   H�	 H-   H=   w�H)�H�	 XYÐ�������������UH��H�4  H� ]�UH��H��3  H� ]�UH��H��H�MH��~  H�E�H�EH�E�H�U�H�E�H�H��H��]�UH��H��~  ]�UH��H�� �M�0   H���UH��H�H�H��H�H�� ]Ð����%B�  ���%B�  ���%J�  ���%J�  ���%J�  ���%J�  ���%J�  ���%Z�  ���%Z�  ���%Z�  ���%Z�  ���%Z�  ���%Z�  ���%Z�  ���%Z�  ���%Z�  ���%Z�  ���%Z�  ���%Z�  ���%Z�  ���%Z�  ���%Z�  ���%��  ���%r�  ���%b�  ���%R�  ���%B�  ���%2�  ���%"�  ���%�  ���%�  ���%�  ���%�  ���%ҏ  ���%  ���%��  ��UH��H�MH�UH�E�H�E� 8���]Ð��������������UH��H��0H�MH�E�    �H�E��E� H�UH�E�H�H�E�H����������u�H�E�H��0]Ð�������UH��H�MH�UH�EH�H�EH��]Ð��USH��(H�l$ H�M H�U(H�E H���1   H�H�E(H���"   H� H)�H��H��H��([]Ð��������������UH��H�MH�E]Ð�UH��H�MH�E]Ð�USH��8H�l$0H�M H�U(L�E0H�E H����   H��H�E H���   H)�H��H�E(H9�����tH�E0H������H�E H���r   H��H�E H���c   H�E�H�U(H�E�H���O  H� H�H�E�H�E H���9   H9E�rH�E H���G   H;E�sH�E H���5   �H�E�H��8[]Ð�������UH��H�MH�EH�PH�EH� H)�H��]ÐUH��H�� H�MH�EH�������H���   H�� ]Ð���������UH��H�MH�UH�EH�H�EH�]Ð���UH��H�� H�M�    ��tH�EH�������
H�EH��������H�� ]Ð���������UH��H�MH�U�]ÐUH��H��0H�MH�UH�} t)H�EH�E�H�EH�E�H�U�H�E�A�    H����  ���    H��0]Ð����UH��H��0H�MH�EH�E�H�E�H�E�H�EH���   �H��0]Ð��������������UH��H��0H�MH�EH�E�H�E�H����  ��H��0]Ð�������UH��H��@H�MH�UL�E H�} t0H�EH�E�H�EH�E�H�E H�E�H�M�H�U�H�E�I��H����   ��H��@]Ð�������������UH��H�MH�EH�     H�EH�@    H�EH�@    �]Ð�UH��H�MH�E]Ð�UH��H�� H�MH�EH��������H�� ]ÐUH��H�� H�MH�EH�PH�EH� H)�H��H�EH�H�EI��H���	���H�EH��������H�� ]Ð�����UH��H�M�]Ð����UH��H�� H�MH�UL�E H�U H�EH���$����H�� ]Ð����UH��H��0H�MH�UL�E H�EH�E�H��������H;E����������t�"���H�EH��������H��0]Ð��������������UH��H�M�]Ð����UH��H��PH�MH��������H�E�H�EH�E�H�E�H�E�H�E�H�E�H����������H�E�H�U�H�E�H���  H� H��P]Ð����UH��H�� H�MH�UL�E L�M(L�E(H�M H�UH�EM��I��H���*  H�� ]Ð���USH��   H��$�   H�M H�U(H�E L�F(  �   H������H�E�H�}� H�E H� H�E�H�E H�@H�E�H�E H���  H�E�H�E H���c  H�E�H�U�H�E�H���_���H�E�H�E H�U�H���K���H�E�H�E�H�E�L�E H�M�H�U�H��p���M��I��H����  H�E(H����  H��H�U�H�E�H�H���  H�U H�U�H�E�H�]�H�E�H���  H�U�H�U�H�E�H�E�H�E�H�E�H�¹   �  H��H�E�H���\  � ���H�E H�������H��L�E�H�U�H�E�I��H���_���H�E�H�E�H�E�H��p���H�E H�@H+E�H��x���H��p���H���i  H�E H�U�H�H�E H�U�H�PH�U�H�E�H�H�E H�P�H�ĸ   []Ð���UH��H��0H�MH�EH�PH�E�H�������H�E�H��0]Ð�����UH��H��0H�MH�UH�E�H������H�E�H��0]Ð���������USH��XH�l$PH�M H�U(H�E H�PH�E H�@H9�tyH�E H�@H�U H�U�H�E�H�E(H�E�H�E�H���  H�U�H�U�H�E�H�E�H�E�H�E�H�¹   �  H��H�E�H����  � ���H�E H�@H�PH�E H�P�H�U(H�E H�������H��X[]Ð������UH��H�� H�MH�EH���8����H�� ]ÐUH��H��@H�MH�EH������H�UH�RH�MH�	H�M�H�U�H�E�H�U�H�E�H���]  �H�EH��� ����H��@]Ð��������UH��H��0H�MH�EH�E�H�E�H��� �����H��0]Ð�������USH��hH�l$`H�M H�U(L�E0H�E(H�E�H�E0H�E��H�E�H�E�H�E�H�E�H�E�H+E�H�E�H�E�H��v7H�U�H�E A�    H���c���H��H�E H���\���H�U�H�E H���t����	H�E H�E��H�U H�E�H���!  H�E H���}���H��H�U0H�E(I��H������H�E�    H�U�H�E H������H�E�H����  �H��H�E�H����  H��H�������H��h[]Ð������UVSH��0H�l$0H�M H�U(L�E0H�] H�E H������H�U0I��H��H������H�}( uH�@#  H���H���H�E(H������H�U(H�H�E�H�M�H�U(H�E A��I��H���o����H��H�E H���.���H��H��������H��0[^]Ð���������UH��H��0H�M�UH�E�@�E�H�EH�H�E����  �E�H��0]Ð�����������UH��H��0H�M�UD�E H�E�@�E��E ���  ��H�EH��H���7  �U �E���Z  ��H�EH��H���x  �E�H��0]Ð��������������UH��H�MH�E]Ð�UVSH�� H�l$ H�M H�U(L�E0L�M8H�E0H�������H��H�E(H������H��H�E H������H�U8I��I��H��H���%   H�� [^]Ð������������UH��H�MH�E]Ð�UH��H��0H�MH�UL�E L�M(H�EH+EH�E�H�}� ~H�E�H�M H�UI�������H�U�H�E H�H��0]�UH��H�� H�MH�EA�J   �   H������H�EH�� ]Ð��UH��H�MH�UH�EH�H�EH� H9�sH�E�H�E]Ð����UH��H�MH�UH�EH�H�EH� H9�sH�E�H�E]Ð����UH��M�E]Ð���UH��H�MH�E]Ð�UH��ȈE�E]ÐUH��H�� H�MH�UH�UH�EH��� ����H�� ]Ð��������UH��H�� H�MH�E� @  H���c���H�EH�� ]Ð��������UH��H�� H�M�UH�E� �U���   H�U�H�EH�� ]ÐUH��M�U�E#E]Ð�������������UH��M�E��]Ð�UH��H�� H�M�UH�E� �U���   H�U�H�EH�� ]ÐUH��M�U�EE]Ð�������������UH��H�MH�UL�E L�M(H�EH�UH�H�EH�U H�PH�EH�U(H�P�]Ð�����UH��H��@H�MH�EH� H��t;H�EH�@H�UH�H�MH�IH�M�H�U�H�E�H�M�H�U�H�E�I��H���n�����H��@]Ð�����UH��H�MH�UH�EH�UH��]Ð�����UH��H�� H�MH�EH� H��tH�EH� H��������H�� ]Ð�UH��H�MH�U�]ÐUH��H�MH�UH�E]Ð�����������������������������                x� @           ��������        ����            ����                           ����            �B @           �B @           �B @   �B @    C @                                                                                                                                                                                                                                                                                                                                                           libgcc_s_dw2-1.dll __register_frame_info __deregister_frame_info                === Modbus CRC16 校验测试 === 读保持寄存器 010300000001 写单个寄存器 010600010003 读输入寄存器 010400000001 写多个寄存器 01100001000204000A0102 简单测试 0102 用户测试数据 DD050000FF00 
--- 测试:   --- 输入数据:  期望CRC16:  计算CRC16:      CRC字节序 (低字节在前):    测试结果:  ✓ 通过 ✗ 失败 
=== 测试总结 ===   ✓ 所有测试通过! CRC16实现符合Modbus标准   ✗ 部分测试失败! CRC16实现可能不符合Modbus标准  basic_string: construction from null is not valid vector::_M_realloc_append     cannot create std::vector larger than max_size()                      � @   � @   �� @   �� @           �, @                   Argument domain error (DOMAIN) Argument singularity (SIGN)      Overflow range error (OVERFLOW) Partial loss of significance (PLOSS)    Total loss of significance (TLOSS)      The result is too small to be represented (UNDERFLOW) Unknown error     _matherr(): %s in %s(%g, %g)  (retval=%g)
  V���������"���I���<���/���Mingw-w64 runtime failure:
     Address %p has no image-section   VirtualQuery failed for %d bytes at address %p          VirtualProtect failed with code 0x%x    Unknown pseudo relocation protocol version %d.
         Unknown pseudo relocation bit size %d.
       %d bit pseudo relocation at %p out of range, targeting %p, yielding the value %p.
      ����3���3���3���3���3�������3���:�����������    (7              P` @           `` @           �� @           @+ @           X� @              @           X� @           @� @           s @           @` @           �� @           �� @           �� @           �� @           �� @           �� @           �� @            � @           �� @           �� @           �� @           �� @           �� @           �� @           �� @           0` @           � @           p8 @            . @           �� @           GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0    GCC: (MinGW-W64 x86_64-msvcrt-posix-seh, built by Brecht Sanders, r3) 14.2.0               ��   v  @   ���������S @           ��������                � @                    @                   �, @   �- @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 �    �  �  �  �  �  �  %   �  %  T  D�  T  x  h�  x  |  t�  |  �  ��  �  �  ��  �  �  ��  �  �  ��  �  �  ��  �  �  ��  �  _  @�  _  7  L�  8  g  p�  g  �!  |�  �!  I"  �  J"  �"  ,�  �"  �"  @�  �"  �"  L�  �"  0#  X�  0#  P#  d�  P#  a#  l�  b#  m#  t�  n#  �#  |�  �#  �#  ��  �#  $  ��  $  +$  ��  ,$  q$  ��  r$  [%  ��  \%  j%  Ȳ  j%  �%  в  �%  �%  ز  �%  �%  �  �%  .&  �  .&  @&  ��  @&  �&   �  �&  �&  �  �&  ,'  �  ,'  S'  $�  T'  �'  0�  �'  �'  8�  �'  (  D�  (  S(  P�  T(  �(  \�  �(  )  h�  )  5)  t�  5)  C)  ��  C)  b)  ��  b)  �)  ��  �)  _*  ��  _*  �*  ܳ  �*  �*   �   ,  ?,  @�  ?,  �,  L�  �,  �,  X�  �,  �,  d�  �,  �-  l�  �-  �-  x�  �-  �-  ��   .  /  ��   /  )/  ��  0/  �/  ��  �/  2  ��  2  W3  ȶ  W3  �3  Զ  �3  (7  �  (7  �7  �  �7  :8  ��  :8  d8  �  p8  �:  �  �:  A;  �  A;  <  (�  <  �<  4�  �<  �=  @�  �=  >  L�  >  �>  X�  �>  {?  d�  {?  �?  p�  �?  @  |�  @  �@  ��  �@  �@  ��  �@  lA  ��  lA  MB  ��  �B  �B  ��  �B  �B  ��  �B  �B  ȷ  �B   C  Է   C  ,C  ܷ  PD  qD  ��  �D  �D  ,�  �D  �D  �  �D  1E  ��  @E  NE  �  PE  ^E  �  `E  (F  l�  0F  OF  D�  PF  vF  е  �F  �F  L�  �F  �F  ̰  �F  �F  ȵ  �F  ;G  ��  @G  qG  T�  �G  �G  �  �G  H   �  H  >H  �  @H  NH  ,�  PH  oH  �  pH  �H  `�  �H  �H  ȴ  �H  �H  ��   I  QI  �  `I  kI  �  pI  �I  �  �I  J  ��  J  �K  H�  �K  
L  x�  L  6L  ��  @L  �L  ��   M  M   �   M  wM  t�  �M  �M  8�  �M  �N  �  �N  fO  ��  pO  �O  �  �O  P  �   P  .P  $�  0P  �P  ��  �P  �P  ��  �P   Q  ,�   Q  -Q  ,�  0Q  [Q  ܵ  `Q  �Q  8�  �Q  �Q  8�  �Q  �Q  d�  �Q  �Q  ��  �Q  �Q  X�  �Q  R   �   R  OR  ��  PR  bR  ذ  pR  ~R  �  �R  �R  �  �R  �R  �  �R  
S  4�  S  jS  <�  pS  �S  д  �S  �S  ش  �S  �S  İ  �S  �S  ��  �S  �S  �                                                                                                                                                                                                                                              P2P  RP  	RP  0C         p8    	RP  0C     4  J  p8  J  �P  2P  E�0P2P        
%
B0`pP2P  PP2P  PPP2P  2P  RP  RP  2P  2P  PrP  U�0P�*  ��A� s� �   2P  �Q 0`
p	����P  �*  ��k4  �� �� �� �� �� �� �� �� �� �� �� �� �� ��� �	�� �
x� �0   Er0`P  �*  ��ah }  rP  �*  �� RP  RP  %B0PPPPRP  2P  2P  �*  �� P%B0PU�0PPPPrP  rP  PE�0PRP  2P  2P  P�P  RP  2P  2P  RP  2P  P2P  5b0P�*  ��
7Vi
s �       %B0P�*  ��  6M b  %
20`p�P  �*  ��CJ c  PRP  2P  2P  RP  RP  PPRP  2P  �*  �� rP  �*  �� U�0P5R0`P  �*  ��
K6� �    PPPP2P  �*  �� e�0P�*  ��`  �� �   PPrP  PPrP  �	 0P  2P  P5b0PRP  RP  %B0PRP  P2P  2P  P2P  PPP%20`P  �P  PRP  PRP  P2P  RP  2P  PRP  P2P  U� x h 	 0P  P5b0P�P  RP  2P  �P  RP  �P  2P  RP  RP  RP  RP  RP  2P  2P  rP  RP  RP  rP  RP  rP  �P  PPP  P2P                         h�          0�  ��  x�          ��  ��  ��          �   �  �          ��  x�                          H�              \�      ��      ��      (�      l�      ��      ��      ��      ��       �      H�      ��      ��      0�      ��      ��      �      L�      ��      ��      ��      ��      �      �      X�      ��      ��      $�      ��      ��      ��      ��      ��      ��              ��      �      $�      2�      B�      V�      h�      ��      ��      ��      ��      ��      ��      ��              �      �      *�      6�      D�      V�      j�      x�      ��      ��      ��      ��      ��      ��      ��      ��      ��      ��      ��      ��      ��      �      �      �       �              H�              \�      ��      ��      (�      l�      ��      ��      ��      ��       �      H�      ��      ��      0�      ��      ��      �      L�      ��      ��      ��      ��      �      �      X�      ��      ��      $�      ��      ��      ��      ��      ��      ��              ��      �      $�      2�      B�      V�      h�      ��      ��      ��      ��      ��      ��      ��              �      �      *�      6�      D�      V�      j�      x�      ��      ��      ��      ��      ��      ��      ��      ��      ��      ��      ��      ��      ��      �      �      �       �               _Unwind_Resume    <_ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE5c_strEv    E_ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE6lengthEv   G_ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE6substrEyy  H_ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE7_M_dataEv  x_ZNSolsEPFRSoS_E  y_ZNSolsEPFRSt8ios_baseS0_E    �_ZNSolsEi �_ZNSolsEt L_ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE10_M_disposeEv   O_ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE11_M_capacityEy  R_ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_Alloc_hiderC1EPcRKS3_ \_ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE13_M_local_dataEv    ]_ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE13_M_set_lengthEy    `_ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE13_S_copy_charsEPcPKcS7_ �_ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE7_M_dataEPc  �_ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE9_M_createERyy   �_ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEC1ERKS4_ �_ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEED1Ev �_ZSt17__throw_bad_allocv  �_ZSt19__throw_logic_errorPKc  �_ZSt20__throw_length_errorPKc �_ZSt28__throw_bad_array_new_lengthv   �_ZSt4cout �_ZSt4endlIcSt11char_traitsIcEERSt13basic_ostreamIT_T0_ES6_    E_ZStlsISt11char_traitsIcEERSt13basic_ostreamIcT_ES5_PKc   M_ZStlsIcSt11char_traitsIcEERSt13basic_ostreamIT_T0_ES6_St5_Setw   O_ZStlsIcSt11char_traitsIcEERSt13basic_ostreamIT_T0_ES6_St8_SetfillIS3_E   P_ZStlsIcSt11char_traitsIcESaIcEERSt13basic_ostreamIT_T0_ES7_RKNSt7__cxx1112basic_stringIS4_S5_T1_EE   �_ZdlPvy   �_Znwy �__cxa_begin_catch �__cxa_end_catch   �__cxa_rethrow �__gxx_personality_seh0    $DeleteCriticalSection JEnterCriticalSection  �FreeLibrary �GetLastError  �GetModuleHandleA  �GetProcAddress  �InitializeCriticalSection �LeaveCriticalSection  �LoadLibraryA  �SetUnhandledExceptionFilter �Sleep �TlsGetValue �VirtualProtect  �VirtualQuery  X __C_specific_handler  � __getmainargs � __initenv � __iob_func  � __set_app_type  � __setusermatherr  � _amsg_exit  � _cexit  � _commode  A_fmode  �_initterm �_onexit rabort �calloc  �exit  �fprintf �free  �fwrite  �malloc  �memcpy  signal  ,strlen  /strncmp :strtol  Svfprintf   �  libgcc_s_seh-1.dll  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  libstdc++-6.dll (�  (�  (�  (�  (�  (�  (�  (�  (�  (�  (�  (�  (�  (�  KERNEL32.dll    <�  <�  <�  <�  <�  <�  <�  <�  <�  <�  <�  <�  <�  <�  <�  <�  <�  <�  <�  <�  <�  <�  <�  <�  <�  msvcrt.dll                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               `     �p��������� p  P   ������ �� �0�@�P�`�p�����������Ц�� �� �0�@�P�`�p�����������Ч   �     `���������                                                                                                                                                                                                                                                                                                                                                                                                          ,             `B @   2                           (                                                                                                                                                                                                                                                                                                                                                                                                                                                                        $            `B @   2    %   r   �6      GNU C17 14.2.0 -march=nocona -msahf -mtune=generic -g -g -g -O2 -O2 -O2 -fbuilding-libgcc -fno-stack-protector �   r   \   char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char long double double float C  	short int ix86_tune_indices �   &�  X86_TUNE_SCHEDULE  X86_TUNE_PARTIAL_REG_DEPENDENCY X86_TUNE_SSE_PARTIAL_REG_DEPENDENCY X86_TUNE_SSE_PARTIAL_REG_FP_CONVERTS_DEPENDENCY X86_TUNE_SSE_PARTIAL_REG_CONVERTS_DEPENDENCY X86_TUNE_DEST_FALSE_DEP_FOR_GLC X86_TUNE_SSE_SPLIT_REGS X86_TUNE_PARTIAL_FLAG_REG_STALL X86_TUNE_MOVX X86_TUNE_MEMORY_MISMATCH_STALL 	X86_TUNE_FUSE_CMP_AND_BRANCH_32 
X86_TUNE_FUSE_CMP_AND_BRANCH_64 X86_TUNE_FUSE_CMP_AND_BRANCH_SOFLAGS X86_TUNE_FUSE_ALU_AND_BRANCH 
X86_TUNE_ACCUMULATE_OUTGOING_ARGS X86_TUNE_PROLOGUE_USING_MOVE X86_TUNE_EPILOGUE_USING_MOVE X86_TUNE_USE_LEAVE X86_TUNE_PUSH_MEMORY X86_TUNE_SINGLE_PUSH X86_TUNE_DOUBLE_PUSH X86_TUNE_SINGLE_POP X86_TUNE_DOUBLE_POP X86_TUNE_PAD_SHORT_FUNCTION X86_TUNE_PAD_RETURNS X86_TUNE_FOUR_JUMP_LIMIT X86_TUNE_SOFTWARE_PREFETCHING_BENEFICIAL X86_TUNE_LCP_STALL X86_TUNE_READ_MODIFY X86_TUNE_USE_INCDEC X86_TUNE_INTEGER_DFMODE_MOVES X86_TUNE_OPT_AGU X86_TUNE_AVOID_LEA_FOR_ADDR  X86_TUNE_SLOW_IMUL_IMM32_MEM !X86_TUNE_SLOW_IMUL_IMM8 "X86_TUNE_AVOID_MEM_OPND_FOR_CMOVE #X86_TUNE_SINGLE_STRINGOP $X86_TUNE_PREFER_KNOWN_REP_MOVSB_STOSB %X86_TUNE_MISALIGNED_MOVE_STRING_PRO_EPILOGUES &X86_TUNE_USE_SAHF 'X86_TUNE_USE_CLTD (X86_TUNE_USE_BT )X86_TUNE_AVOID_FALSE_DEP_FOR_BMI *X86_TUNE_ADJUST_UNROLL +X86_TUNE_ONE_IF_CONV_INSN ,X86_TUNE_AVOID_MFENCE -X86_TUNE_EXPAND_ABS .X86_TUNE_USE_HIMODE_FIOP /X86_TUNE_USE_SIMODE_FIOP 0X86_TUNE_USE_FFREEP 1X86_TUNE_EXT_80387_CONSTANTS 2X86_TUNE_GENERAL_REGS_SSE_SPILL 3X86_TUNE_SSE_UNALIGNED_LOAD_OPTIMAL 4X86_TUNE_SSE_UNALIGNED_STORE_OPTIMAL 5X86_TUNE_SSE_PACKED_SINGLE_INSN_OPTIMAL 6X86_TUNE_SSE_TYPELESS_STORES 7X86_TUNE_SSE_LOAD0_BY_PXOR 8X86_TUNE_INTER_UNIT_MOVES_TO_VEC 9X86_TUNE_INTER_UNIT_MOVES_FROM_VEC :X86_TUNE_INTER_UNIT_CONVERSIONS ;X86_TUNE_SPLIT_MEM_OPND_FOR_FP_CONVERTS <X86_TUNE_USE_VECTOR_FP_CONVERTS =X86_TUNE_USE_VECTOR_CONVERTS >X86_TUNE_SLOW_PSHUFB ?X86_TUNE_AVOID_4BYTE_PREFIXES @X86_TUNE_USE_GATHER_2PARTS AX86_TUNE_USE_SCATTER_2PARTS BX86_TUNE_USE_GATHER_4PARTS CX86_TUNE_USE_SCATTER_4PARTS DX86_TUNE_USE_GATHER_8PARTS EX86_TUNE_USE_SCATTER_8PARTS FX86_TUNE_AVOID_128FMA_CHAINS GX86_TUNE_AVOID_256FMA_CHAINS HX86_TUNE_AVOID_512FMA_CHAINS IX86_TUNE_V2DF_REDUCTION_PREFER_HADDPD JX86_TUNE_AVX256_UNALIGNED_LOAD_OPTIMAL KX86_TUNE_AVX256_UNALIGNED_STORE_OPTIMAL LX86_TUNE_AVX256_SPLIT_REGS MX86_TUNE_AVX128_OPTIMAL NX86_TUNE_AVX256_OPTIMAL OX86_TUNE_AVX512_SPLIT_REGS PX86_TUNE_AVX256_MOVE_BY_PIECES QX86_TUNE_AVX256_STORE_BY_PIECES RX86_TUNE_AVX512_MOVE_BY_PIECES SX86_TUNE_AVX512_STORE_BY_PIECES TX86_TUNE_DOUBLE_WITH_ADD UX86_TUNE_ALWAYS_FANCY_MATH_387 VX86_TUNE_UNROLL_STRLEN WX86_TUNE_SHIFT1 XX86_TUNE_ZERO_EXTEND_WITH_AND YX86_TUNE_PROMOTE_HIMODE_IMUL ZX86_TUNE_FAST_PREFIX [X86_TUNE_READ_MODIFY_WRITE \X86_TUNE_MOVE_M1_VIA_OR ]X86_TUNE_NOT_UNPAIRABLE ^X86_TUNE_PARTIAL_REG_STALL _X86_TUNE_PARTIAL_MEMORY_READ_STALL `X86_TUNE_PROMOTE_QIMODE aX86_TUNE_PROMOTE_HI_REGS bX86_TUNE_HIMODE_MATH cX86_TUNE_SPLIT_LONG_MOVES dX86_TUNE_USE_XCHGB eX86_TUNE_USE_MOV0 fX86_TUNE_NOT_VECTORMODE gX86_TUNE_AVOID_VECTOR_DECODE hX86_TUNE_BRANCH_PREDICTION_HINTS_TAKEN iX86_TUNE_BRANCH_PREDICTION_HINTS_NOT_TAKEN jX86_TUNE_QIMODE_MATH kX86_TUNE_PROMOTE_QI_REGS lX86_TUNE_EMIT_VZEROUPPER mX86_TUNE_SLOW_STC nX86_TUNE_USE_RCR oX86_TUNE_LAST p ix86_arch_indices �   �  X86_ARCH_CMOV  X86_ARCH_CMPXCHG X86_ARCH_CMPXCHG8B X86_ARCH_XADD X86_ARCH_BSWAP X86_ARCH_LAST  signed char __int128 __int128 unsigned _Float16 complex _Float16 complex float complex double  complex long double _Float128  complex _Float128 
func_ptr *=  �  �   __CTOR_LIST__ /�  __DTOR_LIST__ 0�  �    �    �  {�  	�� @   �  |�  	�� @                                                                                                                                                                                                                                                                                                                                                                                                                                   %   (   $ >  >!!I:!;9!  I  4 :!;9!I?<  4 G:!;9!
I  %   I  	 '  
 :;9I  !   ! I/                                                                                                                                                                                                                                                                                                                                                    X     .   �
          M   i   i    	`B @   � ""gY0uKgg0=L"" D     <   �
      �   &  6  Y  c  j  v                                                                                                                                                                                                                                                                                                                                                                 ���� x �      ,       `B @   2       A�A�n�A�                                                                                                                                                                                                                                                                                                                                                                                                                                                              ../../../libgcc/config/i386/cygwin.S R:\winlibs_staging_msvcrt64\gcc-14.2.0\build_mingw\x86_64-w64-mingw32\libgcc GNU AS 2.44                                                                                                                                                                                                                                                                                                                                                                                                   R:\winlibs_staging_msvcrt64\gcc-14.2.0\build_mingw\x86_64-w64-mingw32\libgcc ../../../libgcc/config/i386 cygwin.S R:\winlibs_staging_msvcrt64\gcc-14.2.0\build_mingw\x86_64-w64-mingw32\libgcc ../../../libgcc/libgcc2.c R:/winlibs_staging_msvcrt64/gcc-14.2.0/build_mingw/x86_64-w64-mingw32/libgcc ../../../libgcc ../../../libgcc/../gcc/config/i386 libgcc2.c i386.h gbl-ctors.h libgcc2.c                                                                                                                                 .file   T   ��  gcrtexe.c          argc           argv           envp           argret         mainret            k               v   $           �   (           �                                �              �   �                        �   �                        �   �                                                  /  �                        G  �           T  �                        l  �                        �  �           �        .l_endw           �  %      .l_start4      .l_end  J          �  T          �                           �  0                          p                        %  `                        ;  P                        Q  @                        g  �                        �  �                        �                          �  �                        �  x          
  �                        6  �                        ^                           �  P                        �  |      atexit  �          �  �                    .text          �  H             .data                            .bss           ,                     �  �                         �  �                     .xdata         �   
             .pdata         l                    �  �     M                 .file   k   ��  gcygming-crtbeg        �          obj     `                           �                           4  �      .text   �                    .data                           .bss    @      P                 .xdata  �      $                 .pdata  l      0                .rdata         A                     �  0     M                     K          .file     ��  g    |=                U  �C                     _ZnwyPv �C                           c  �C                     _ZdlPvS_�C          r  �6     6                   �  �6          �  PB                         �  PB          �  �B                           �B          .  pB                         L  pB          d  �B     /                   �  �B          �   B     /                   �   B          �  p?     4                     p?          .  �?     a                   ]  �?          �  �A     '                   �  �A          �   A     -                   �   A          �  �A                           �A            �          4  �7     (                   h  �7          �  P8                        �  P8          �   =                        �   =            _          _  8          w  8      main    g          �                           �  0                        �  �4     H                   !	  �4          J	  �=     (                   �	  �=          �	  06                         
  06           
  �6                         @
  �6          Z
  @7     1                   �
  @7          �
  p8     J                   �
  p8             =     W                   !   =          :  @<     �                   c  @<          �  �>     �                   �  �>            �          _  �          �  J          �  J          �  �          (
  �          T
  �          �
  0          �
  P          L  �A                         t  �A          �  P4     !                    �  P4          �  �8                           �8          .  pC                         �  pC          &  �C     .                   �  �C            �=     	  
                �  �=          �  8     .                    '  8          Z  `9                         ~  `9          �  �7     R                   �  �7             @8                         9  @8          l  �B     :                    �  �B            C     Z                   P  C          �  :     �                  �  :            b          3  n          r  n          �  �          �  �                      J  ,          {  r          �  \             j          q  j          �  �            �A     '                   1  �A          I  �A                         �  �A          �  `5     �   	                �  `5            �;     *                   4  �;          O  <     &                   r  <          �  �4     A                   �  �4          O  �6     K                   �  �6          �  �@                         �  �@          �  �9     <                     �9          M  �          �  �          �  �            .          G  @          �  �          �  �            ,          5  �8     +                   e  �8          �  �6                         �  �6          �  P6     &                     P6          ;  0A     +                    W  0A          m  �4                         �  �4          �  @5                         3  @5          q  0@     c                   �  0@          �  T             �          P   �          �             �   p9     [                   �   p9           !  P5                         Z!  P5          �!   9     Q                   �!   9          �!   @                         "   @          ""  �@     P                   �"  �@          #  T          ?#  `A     +                    [#  `A          q#  �          �#            �#  5          ($  C          L$  b          �$  �          �$  �          �$  _          �$  �          5%  �          h%  �          �%  �      .text   �       �             .data                           .bss    �                            �%  �                          �%  �                         �%  �                          �%  �                         &  �                          4&  �                         \&  �                          }&  �                         �&  �                          �&  �                         �&  �                          �&  �                         '  �                          @'  �                         b'  �                          �'  �                         �'                           �'  �                          (                           0(                          `(                            �(                          �(  ,                         �(                           �(  8                         �(  ,                    .xdata  @     �               .pdata  8     �  {                  )                           5)  $                        j)                           �)  0                        �)                            �)  <                    .rdata  P      |                    �)  ,                         (*  H                        X*  8                         �*  T                        �*  D                         +  `                        >+  L                         _+  l                        �+  T                         �+  x                        �+  `                        ,  �                        8,  t                        X,  �                        x,  �                         �,  �                        �,  �     "                   -  �                        f-  �                         �-  �                        �-  �                         �-  �                        .  �                         ;.  �                        `.  �                         �.  �                        `/  �                        �/  �                        Z0  �     #                   �0  �                        81                           r1  �                        �1                           �1                          �1                            ,2                          b2  ,                         �2                           �2  4                         '3  ,                        x3  <                         �3  8                        4  H                         K4  D                        �4  X                         �4  P                        �4  d                         5  \                        @5  l                         p5  h                        �5  x                         �5  t                        �5  �                         6  �                        ,6  �                         �6  �                        �6  �                         &7  �                        X7  �                         {7  �                        �7  �                         �7  �                        
8  �                         ;8  �                        l8  �                         �8  �                        �8  �                          9  �                        (9  �                         E9  �                        b9  �                         �9  �                        �9  �                         1:  �                        v:  �                         �:                          �:                           ;                          >;                           y;                          �;                           �;  (                        <  $                         5<  4                        X<  ,                         �<  @                        B=  8                         _=  L                        �  �     M                 .text   �      .idata$7�      .idata$5�      .idata$4�      .idata$6�      .text   �      .idata$7�      .idata$5�      .idata$4x      .idata$6�      .text          .idata$7�      .idata$5�      .idata$4p      .idata$6�      .text         .idata$7�      .idata$5�      .idata$4h      .idata$6�      .text         .idata$7�      .idata$5�      .idata$4`      .idata$6�      .text         .idata$7�      .idata$5�      .idata$4X      .idata$6�      .text          .idata$7�      .idata$5�      .idata$4P      .idata$6$      .text   (      .idata$7�      .idata$5�      .idata$4H      .idata$6�
      .text   0      .idata$7�      .idata$5�      .idata$4@      .idata$6�
      .text   8      .idata$7�      .idata$5�      .idata$48      .idata$6X
      .text   @      .idata$7�      .idata$5�      .idata$40      .idata$6
      .text   H      .idata$7�      .idata$5�      .idata$4(      .idata$6
      .text   H      .idata$7�      .idata$5�      .idata$4       .idata$6�	      .text   P      .idata$7�      .idata$5�      .idata$4      .idata$6�	      .text   X      .idata$7�      .idata$5�      .idata$4      .idata$6�	      .text   `      .idata$7�      .idata$5x      .idata$4      .idata$6�	      .text   h      .idata$7�      .idata$5p      .idata$4       .idata$6L	      .text   p      .idata$7�      .idata$5h      .idata$4�       .idata$6	      .text   x      .idata$7�      .idata$5`      .idata$4�       .idata$6�      .text   �      .idata$7|      .idata$5X      .idata$4�       .idata$6�      .text   �      .idata$7x      .idata$5P      .idata$4�       .idata$60      .text   �      .idata$7t      .idata$5H      .idata$4�       .idata$6�      .text   �      .idata$7p      .idata$5@      .idata$4�       .idata$6�      .text   �      .idata$7l      .idata$58      .idata$4�       .idata$6H      .text   �      .idata$7h      .idata$50      .idata$4�       .idata$6       .text   �      .idata$7d      .idata$5(      .idata$4�       .idata$6�      .text   �      .idata$7`      .idata$5       .idata$4�       .idata$6�      .text   �      .idata$7\      .idata$5      .idata$4�       .idata$6�      .text   �      .idata$7X      .idata$5      .idata$4�       .idata$6�      .text   �      .idata$7T      .idata$5      .idata$4�       .idata$6l      .text   �      .idata$7P      .idata$5       .idata$4�       .idata$6(      .text   �      .idata$7L      .idata$5�      .idata$4�       .idata$6�      .text   �      .idata$7H      .idata$5�      .idata$4�       .idata$6�      .text   �      .idata$7D      .idata$5�      .idata$4x       .idata$6\      .file     ��  ggccmain.c             �=                          p.0                �=  ?          �=  @                        �=  �       __main  �      .text         �   
             .data                         .bss    �                       .xdata  @     $                 .pdata  X     $   	                 �  �     M                 .file   !  ��  gnatstart.c        .text   �                       .data                           .bss    �                            �   	     M                 .file   +  ��  gwildcard.c        .text   �                       .data   0                       .bss    �                            �  p	     M                 .file   ;  ��  gdllargv.c         _setargv�                       .text   �                      .data   @                        .bss    �                        .xdata  d                      .pdata  |                         �  �	     M                 .file   E  ��  g_newmode.c        .text   �                       .data   @                        .bss    �                           �  
     M                 .file   m  ��  gtlssup.c          __xd_a  �      __xd_z  �          �=  �                           �=                           >  �          >  �      .text   �                    .data   @                        .bss    �                       .tls        	                    .tls$ZZZ   	                    .CRT$XLA�                      .CRT$XLZ�                      .rdata  �     0                .CRT$XDA�                      .CRT$XDZ�                      .xdata  l                       .pdata  �     $   	             .CRT$XLC�                     .CRT$XLD�                         �  `
     M                 .file   w  ��  gxncommod.c        .text                           .data   @                        .bss    �                           �  �
     M                 .file   �  ��  gcinitexe.c        .text                           .data   @                        .bss    �                        .CRT$XIA�                      .CRT$XIZ�                      .CRT$XCA�                      .CRT$XCZ�                          �        M                 .file   �  ��  gmerr.c            _matherr                        .text                        .data   @                        .bss    �                        .rdata        @               .xdata  �                      .pdata  �                         �  P     M                 .file   �  ��  gxthdloc.c         .text                           .data   @                       .bss    �                            �  �     M                 .file   �  ��  gCRT_fp10.c        _fpreset                        fpreset        .text         	                 .data   P                        .bss    �                        .xdata  �                      .pdata  �                         �  �     M                 .file   �  ��  gmingw_helpers.    .text   0                       .data   P                        .bss    �                           �  @     M                 .file   �  ��  gpseudo-reloc.c         >  0                       the_secs�           />  �           ;>  �          Q>  "          k>  W#          z>  �#          �>  ('          �>  �           �>  `                        �>  p                    .text   0     �  0             .data   P                        .bss    �                       .rdata  `     [                .xdata  �     H                 .pdata  �     H                    �  �     M                 .file   �  ��  gusermatherr.c         
?             ?  �'                           1?  :(      .text   �'     �                .data   P                        .bss                           .xdata  �                      .pdata                           �  �     M                 .file   �  ��  gxtxtmode.c        .text   p(                       .data   P                        .bss                              �  0
     M                 .file     ��  gcrt_handler.c         H?  p(                       .text   p(     #               .data   P                        .bss                           .xdata                        .rdata  �     ,                .pdata  $                         �  �
     M                 .file   "  ��  gtlsthrd.c             _?  @          m?  h          �?  p          �?  �*                           �?  A+          �?  ,          �?  �,      .text   �*     �  $             .data   P                        .bss    @     8                 .xdata       0                 .pdata  0     0                    �  �
     M                 .file   ,  ��  gtlsmcrt.c         .text   �-                       .data   P                       .bss    �                           �        M                 .file   6  ��  g    �?            .text   �-                       .data   `                        .bss    �                          �  p     M                 .file   S  ��  gpesect.c              @  �-                           @  .          .@  �.          C@  {/          `@  �/          x@  0          �@  �0          �@  �0          �@  l1      .text   �-     �  	             .data   `                        .bss    �                       .xdata  L     l                 .pdata  `     l                    �  �     M                 .text   P2      .idata$7,      .idata$5�      .idata$4h       .idata$6H      .file   i  ��  gfake                  �@         (                    �@      
                        �@         \                .text   `2     2                 .data   `                        .bss    �                            A         r                     A         0                    A         ~                     *A         H                .file   }  ��  glibgcc2.c         .text   �2                       .data   `                        .bss    �                           �@  (      :                   �@     
   �                     A  0                           �@  \      H                     A  r                          �       M                 .file   �  ��  gmingw_matherr.    .text   �2                       .data   `                       .bss    �                           �  `     M                 .file   �  ��  g__p__fmode.c          7A  �2                           BA  �                    .text   �2                     .data   p                      .bss    �                       .xdata  �                      .pdata  �                         �  �     M                 .file   �  ��  g__p__commode.c        ^A  �2                           kA  �                    .text   �2                     .data   �                      .bss    �                       .xdata  �                      .pdata  �                         �        M                 .file   �  ��  g    B            handler �          �A  �2                           �A  �2          �A  �2          �A  �2      .text   �2     @                .data   �                      .bss    �                      .xdata  �                      .pdata  �                         �  P     M                 .file   ~  ��  gacrt_iob_func.        +B   3                       .text    3     ,                .data   �                      .bss    �                       .xdata  �                      .pdata  �                         �  �     M                 .text   03      .data   �       .bss    �      .idata$7$      .idata$5x      .idata$4      .idata$6
      .text   83      .data   �       .bss    �      .idata$7(      .idata$5�      .idata$4      .idata$6
      .text   @3      .data   �       .bss    �      .idata$7,      .idata$5�      .idata$4      .idata$6*
      .text   @3      .data   �       .bss    �      .idata$70      .idata$5�      .idata$4       .idata$66
      .text   H3      .data   �       .bss    �      .idata$74      .idata$5�      .idata$4(      .idata$6D
      .text   P3      .data   �       .bss    �      .idata$78      .idata$5�      .idata$40      .idata$6V
      .text   X3      .data   �       .bss    �      .idata$7<      .idata$5�      .idata$48      .idata$6j
      .text   `3      .data   �       .bss    �      .idata$7@      .idata$5�      .idata$4@      .idata$6x
      .text   h3      .data   �       .bss    �      .idata$7D      .idata$5�      .idata$4H      .idata$6�
      .text   h3      .data   �       .bss    �      .idata$7H      .idata$5�      .idata$4P      .idata$6�
      .text   h3      .data   �       .bss    �      .idata$7L      .idata$5�      .idata$4X      .idata$6�
      .text   p3      .data   �       .bss    �      .idata$7P      .idata$5�      .idata$4`      .idata$6�
      .text   x3      .data   �       .bss    �      .idata$7T      .idata$5�      .idata$4h      .idata$6�
      .text   �3      .data   �       .bss    �      .idata$7X      .idata$5�      .idata$4p      .idata$6�
      .text   �3      .data   �       .bss    �      .idata$7\      .idata$5�      .idata$4x      .idata$6�
      .text   �3      .data   �       .bss    �      .idata$7`      .idata$5�      .idata$4�      .idata$6�
      .text   �3      .data   �       .bss    �      .idata$7d      .idata$5�      .idata$4�      .idata$6�
      .text   �3      .data   �       .bss    �      .idata$7h      .idata$5       .idata$4�      .idata$6�
      .text   �3      .data   �       .bss    �      .idata$7l      .idata$5      .idata$4�      .idata$6�
      .text   �3      .data   �       .bss    �      .idata$7p      .idata$5      .idata$4�      .idata$6�
      .text   �3      .data   �       .bss    �      .idata$7t      .idata$5      .idata$4�      .idata$6�
      .text   �3      .data   �       .bss    �      .idata$7x      .idata$5       .idata$4�      .idata$6      .text   �3      .data   �       .bss    �      .idata$7|      .idata$5(      .idata$4�      .idata$6      .text   �3      .data   �       .bss    �      .idata$7�      .idata$50      .idata$4�      .idata$6      .text   �3      .data   �       .bss    �      .idata$7�      .idata$58      .idata$4�      .idata$6       .file   �  ��  gfake              hname         fthunk  x      .text   �3                       .data   �                        .bss    �                       .idata$2<                      .idata$4      .idata$5x      .file   �  ��  gfake              .text   �3                       .data   �                        .bss    �                       .idata$4�                      .idata$5@                      .idata$7�                      .text   �3      .data   �       .bss    �      .idata$7      .idata$5h      .idata$4�      .idata$6�      .text   �3      .data   �       .bss    �      .idata$7      .idata$5`      .idata$4�      .idata$6�      .text   �3      .data   �       .bss    �      .idata$7      .idata$5X      .idata$4�      .idata$6�      .text   �3      .data   �       .bss    �      .idata$7      .idata$5P      .idata$4�      .idata$6�      .text    4      .data   �       .bss    �      .idata$7       .idata$5H      .idata$4�      .idata$6�      .text   4      .data   �       .bss    �      .idata$7�      .idata$5@      .idata$4�      .idata$6�      .text   4      .data   �       .bss    �      .idata$7�      .idata$58      .idata$4�      .idata$6�      .text   4      .data   �       .bss    �      .idata$7�      .idata$50      .idata$4�      .idata$6h      .text    4      .data   �       .bss    �      .idata$7�      .idata$5(      .idata$4�      .idata$6V      .text   (4      .data   �       .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6B      .text   04      .data   �       .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$62      .text   84      .data   �       .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6$      .text   @4      .data   �       .bss    �      .idata$7�      .idata$5      .idata$4�      .idata$6      .text   H4      .data   �       .bss    �      .idata$7�      .idata$5       .idata$4�      .idata$6�      .file   
  ��  gfake              hname   �      fthunk         .text   P4                       .data   �                        .bss    �                       .idata$2(                      .idata$4�      .idata$5       .file     ��  gfake              .text   P4                       .data   �                        .bss    �                       .idata$4                       .idata$5p                      .idata$7     
                 .file   =  ��  gcygming-crtend        ;B              IB  �C                       .text   P4                       .data   �                        .bss    �                           K                              ]B  �C                         kB  �                          zB                           �B  `                         �  �     M                 .rdata  �          �B  @      .idata$2        .idata$5�      .idata$4h       .idata$2       .idata$5�      .idata$4x       .idata$4p       .idata$5�      .idata$70      .idata$4�      .idata$5�      .idata$7�      __xc_z  �          �B  �          �B  @          C  �          _C  X          �C  8          �C  �          �C        _ZdlPvy           D  �          D              #D  p      _Znwy             2D  �          ?D   4          [D  <           wD  �          �D  0          �D  �          �D  p           �D      	        E  �          *E  0      __xl_a  �          IE  `          �E  04          �E            �E  @          �E  �          �E  @      _cexit  `3          .F  `  ��       FF     ��       _F              uF  @3          �F      ��       �F     ��       �F  �          �F         __xl_d  �          �F  �       _tls_end   	        	G  @          G  �3          ,G  �          >G  �          SG  H          �G  �          �G  �          �G  �          �G  �          �G      	        �G  p      memcpy  �3          ,H  �          =H  �          �H  p          �H  �          �H             I  �      malloc  �3      _CRT_MT P           SI  �3          _I  0          lI  �          �I             �I              �I  �          �I  x          �I  �          <J  X          _J     ��       wJ  �          �J            �J  �          �J  �           �J  �          �J  @           K  x          !K  4          .K  `          GK  �          `K  �           yK  �3          �K  �          �K  �          �K  8          �K  �          �K  03          �K  P          	L        abort   x3          *L  `          TL             dL  �          tL  0      __dll__     ��       �L      ��   strtol  �3          �L  @4          �L  �          &M  `           CM            ZM  `          iM             yM  �          �M     ��       �M              �M  �      calloc  �3          �M  �          �M  �          �M  H          �M            N  8          VN  @      fprintf �3          tN  �          �N  �          �N  �          �N                              �N  �      Sleep   �3          WO  �      _commode�           hO  P2          wO  �          �O  �           �O             �O  �          �O  X          �O  �          �O  (           �O  �          P  (4          P  �      __xi_z  �          +P  $           CP  �          SP             tP  �          �P             �P  �          �P            Q  @           $Q  �          .Q  �       signal  �3          9Q  �          PQ  �      strncmp �3          bQ  0          {Q  �          �Q  (          �Q  X          �Q  �           R            
R  H          1R  �          TR  �          uR             �R             �R             �R  P          �R             S            S  h          MS  �          lS     ��       S  (          �S  0          �S  X          T  83          T  4          .T  �          9T             �T  �          �T  8          �T  
          �T     ��       U  �           U              9U  �          IU  p          �U  �          �U  �          �U  �          V  �      __xl_z  �      __end__              V  H          7V  P          �V  �          �V  �          �V   4          �V  p      __xi_a  �          �V  H3          W  P          W  4      __xc_a  �          'W  �          >W     ��       WW  �          iW     ��   _fmode            wW  X3          �W  X          �W  P3          �W  �          �W  �          �W  �          �W  �           �W            �W  0          EX  `          ZX  �      __xl_c  �          �X  �          �X     	        �X  h          Y  �          Y            #Y  0           /Y  �           HY  �          YY            qY  �      _newmode�           �Y  h3      fwrite  �3          �Y  (          �Y  �          �Y  �           �Y      ��       �Y      ��       �Y             �Y  �          =Z  h          �Z  @          �Z  �          �Z  `2          �Z  �          qY  �          �Z        _onexit p3          [  X          )[  (      exit    �3          q[  �           �[     ��       �[      ��       �[  x          \  �          #\  @          2\         strlen  �3          B\  @          U\  p          d\            �\  84          �\  �          �\  H4          �\  X          �\  �          *]                              @]  P          O]  �          _]  �          v]  �      vfprintf�3          �]  �      free    �3          �]  �       �]  .eh_frame .debug_aranges .debug_info .debug_abbrev .debug_line .debug_frame .debug_str .debug_line_str managedapp has_cctor startinfo __mingw_invalidParameterHandler pre_c_init .rdata$.refptr.__mingw_app_type .rdata$.refptr._fmode .rdata$.refptr._commode .rdata$.refptr._MINGW_INSTALL_DEBUG_MATHERR .rdata$.refptr._matherr pre_cpp_init .rdata$.refptr._newmode .rdata$.refptr._dowildcard WinMainCRTStartup .l_startw mainCRTStartup __tmainCRTStartup .rdata$.refptr.__native_startup_lock .rdata$.refptr.__native_startup_state .rdata$.refptr.__xi_z .rdata$.refptr.__xi_a .rdata$.refptr.__xc_z .rdata$.refptr.__xc_a .rdata$.refptr.__dyn_tls_init_callback .rdata$.refptr._gnu_exception_handler .rdata$.refptr.__mingw_oldexcpt_handler .rdata$.refptr.__imp___initenv check_managed_app .rdata$.refptr.__mingw_initltsdrot_force .rdata$.refptr.__mingw_initltsdyn_force .rdata$.refptr.__mingw_initltssuo_force .rdata$.refptr.__ImageBase duplicate_ppstrings .rdata$.refptr.__globallocalestatus .CRT$XIAA .CRT$XCAA .rdata$zzz __EH_FRAME_BEGIN__ deregister_frame_fn __gcc_register_frame __gcc_deregister_frame .eh_frame .text$_ZnwyPv .text$_ZdlPvS_ .text$_ZNSt11char_traitsIcE6lengthEPKc _ZNSt11char_traitsIcE6lengthEPKc .text$_ZStanSt13_Ios_FmtflagsS_ _ZStanSt13_Ios_FmtflagsS_ .text$_ZStorSt13_Ios_FmtflagsS_ _ZStorSt13_Ios_FmtflagsS_ .text$_ZStcoSt13_Ios_Fmtflags _ZStcoSt13_Ios_Fmtflags .text$_ZStoRRSt13_Ios_FmtflagsS_ _ZStoRRSt13_Ios_FmtflagsS_ .text$_ZStaNRSt13_Ios_FmtflagsS_ _ZStaNRSt13_Ios_FmtflagsS_ .text$_ZNSt8ios_base4setfESt13_Ios_Fmtflags _ZNSt8ios_base4setfESt13_Ios_Fmtflags .text$_ZNSt8ios_base4setfESt13_Ios_FmtflagsS0_ _ZNSt8ios_base4setfESt13_Ios_FmtflagsS0_ .text$_ZSt9uppercaseRSt8ios_base _ZSt9uppercaseRSt8ios_base .text$_ZSt3hexRSt8ios_base _ZSt3hexRSt8ios_base .text$_ZSt4setwi _ZSt4setwi _Z14calculateCRC16RKSt6vectorIhSaIhEE .text$_ZNSt12_Vector_baseIhSaIhEE12_Vector_implD1Ev _ZNSt12_Vector_baseIhSaIhEE12_Vector_implD1Ev .text$_ZNSt12_Vector_baseIhSaIhEEC2Ev _ZNSt12_Vector_baseIhSaIhEEC2Ev .text$_ZNSt6vectorIhSaIhEEC1Ev _ZNSt6vectorIhSaIhEEC1Ev _Z16hexStringToBytesRKNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEE _ZZ4mainEN8TestCaseD2Ev _ZZ4mainEN8TestCaseD1Ev .rdata$.refptr._ZSt4cout .rdata$.refptr._ZSt4endlIcSt11char_traitsIcEERSt13basic_ostreamIT_T0_ES6_ .text$_ZN9__gnu_cxx11char_traitsIcE6lengthEPKc _ZN9__gnu_cxx11char_traitsIcE6lengthEPKc .text$_ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_Alloc_hiderD1Ev _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_Alloc_hiderD1Ev .text$_ZNKSt6vectorIhSaIhEE4sizeEv _ZNKSt6vectorIhSaIhEE4sizeEv .text$_ZNKSt6vectorIhSaIhEEixEy _ZNKSt6vectorIhSaIhEEixEy .text$_ZNSt12_Vector_baseIhSaIhEE12_Vector_implC1Ev _ZNSt12_Vector_baseIhSaIhEE12_Vector_implC1Ev .text$_ZNSt12_Vector_baseIhSaIhEED2Ev _ZNSt12_Vector_baseIhSaIhEED2Ev .text$_ZNSt6vectorIhSaIhEED1Ev _ZNSt6vectorIhSaIhEED1Ev .text$_ZNSt6vectorIhSaIhEE9push_backERKh _ZNSt6vectorIhSaIhEE9push_backERKh .text$_ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEC1IS3_EEPKcRKS3_ _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEC1IS3_EEPKcRKS3_ _ZNSt6vectorIZ4mainE8TestCaseSaIS0_EEC2ESt16initializer_listIS0_ERKS1_ _ZNSt6vectorIZ4mainE8TestCaseSaIS0_EEC1ESt16initializer_listIS0_ERKS1_ _ZNSt6vectorIZ4mainE8TestCaseSaIS0_EED2Ev _ZNSt6vectorIZ4mainE8TestCaseSaIS0_EED1Ev _ZNSt6vectorIZ4mainE8TestCaseSaIS0_EE5beginEv _ZNSt6vectorIZ4mainE8TestCaseSaIS0_EE3endEv _ZN9__gnu_cxxneIPZ4mainE8TestCaseSt6vectorIS1_SaIS1_EEEEbRKNS_17__normal_iteratorIT_T0_EESB_ _ZN9__gnu_cxx17__normal_iteratorIPZ4mainE8TestCaseSt6vectorIS1_SaIS1_EEEppEv _ZNK9__gnu_cxx17__normal_iteratorIPZ4mainE8TestCaseSt6vectorIS1_SaIS1_EEEdeEv .text$_ZSt7setfillIcESt8_SetfillIT_ES1_ _ZSt7setfillIcESt8_SetfillIT_ES1_ .text$_ZN9__gnu_cxx11char_traitsIcE2eqERKcS3_ _ZN9__gnu_cxx11char_traitsIcE2eqERKcS3_ .text$_ZNSt15__new_allocatorIcED2Ev _ZNSt15__new_allocatorIcED2Ev .text$_ZZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_M_constructIPKcEEvT_S8_St20forward_iterator_tagEN6_GuardC1EPS4_ _ZZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_M_constructIPKcEEvT_S8_St20forward_iterator_tagEN6_GuardC1EPS4_ .text$_ZZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_M_constructIPKcEEvT_S8_St20forward_iterator_tagEN6_GuardD1Ev _ZZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_M_constructIPKcEEvT_S8_St20forward_iterator_tagEN6_GuardD1Ev .text$_ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_M_constructIPKcEEvT_S8_St20forward_iterator_tag _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_M_constructIPKcEEvT_S8_St20forward_iterator_tag .text$_ZNSt12_Vector_baseIhSaIhEE17_Vector_impl_dataC2Ev _ZNSt12_Vector_baseIhSaIhEE17_Vector_impl_dataC2Ev .text$_ZNSt15__new_allocatorIhED2Ev _ZNSt15__new_allocatorIhED2Ev .text$_ZNSt12_Vector_baseIhSaIhEE13_M_deallocateEPhy _ZNSt12_Vector_baseIhSaIhEE13_M_deallocateEPhy .text$_ZNSt12_Vector_baseIhSaIhEE19_M_get_Tp_allocatorEv _ZNSt12_Vector_baseIhSaIhEE19_M_get_Tp_allocatorEv .text$_ZZNSt6vectorIhSaIhEE17_M_realloc_appendIJRKhEEEvDpOT_EN6_GuardC1EPhyRS0_ _ZZNSt6vectorIhSaIhEE17_M_realloc_appendIJRKhEEEvDpOT_EN6_GuardC1EPhyRS0_ .text$_ZZNSt6vectorIhSaIhEE17_M_realloc_appendIJRKhEEEvDpOT_EN6_GuardD1Ev _ZZNSt6vectorIhSaIhEE17_M_realloc_appendIJRKhEEEvDpOT_EN6_GuardD1Ev .text$_ZNSt6vectorIhSaIhEE17_M_realloc_appendIJRKhEEEvDpOT_ _ZNSt6vectorIhSaIhEE17_M_realloc_appendIJRKhEEEvDpOT_ _ZNSt15__new_allocatorIZ4mainE8TestCaseED2Ev _ZNSt12_Vector_baseIZ4mainE8TestCaseSaIS0_EE12_Vector_implD2Ev _ZNSt12_Vector_baseIZ4mainE8TestCaseSaIS0_EE12_Vector_implD1Ev _ZNSt12_Vector_baseIZ4mainE8TestCaseSaIS0_EEC2ERKS1_ _ZNSt12_Vector_baseIZ4mainE8TestCaseSaIS0_EED2Ev _ZNKSt16initializer_listIZ4mainE8TestCaseE5beginEv _ZNKSt16initializer_listIZ4mainE8TestCaseE3endEv _ZNSt6vectorIZ4mainE8TestCaseSaIS0_EE19_M_range_initializeIPKS0_EEvT_S6_St20forward_iterator_tag _ZNSt12_Vector_baseIZ4mainE8TestCaseSaIS0_EE19_M_get_Tp_allocatorEv _ZN9__gnu_cxx17__normal_iteratorIPZ4mainE8TestCaseSt6vectorIS1_SaIS1_EEEC2ERKS2_ _ZN9__gnu_cxx17__normal_iteratorIPZ4mainE8TestCaseSt6vectorIS1_SaIS1_EEEC1ERKS2_ _ZNK9__gnu_cxx17__normal_iteratorIPZ4mainE8TestCaseSt6vectorIS1_SaIS1_EEE4baseEv .text$_ZSt8_DestroyIPhEvT_S1_ _ZSt8_DestroyIPhEvT_S1_ .text$_ZSt7forwardIRKhEOT_RNSt16remove_referenceIS2_E4typeE _ZSt7forwardIRKhEOT_RNSt16remove_referenceIS2_E4typeE .text$_ZNKSt6vectorIhSaIhEE12_M_check_lenEyPKc _ZNKSt6vectorIhSaIhEE12_M_check_lenEyPKc .text$_ZNSt6vectorIhSaIhEE3endEv _ZNSt6vectorIhSaIhEE3endEv .text$_ZNSt6vectorIhSaIhEE5beginEv _ZNSt6vectorIhSaIhEE5beginEv .text$_ZN9__gnu_cxxmiIPhSt6vectorIhSaIhEEEENS_17__normal_iteratorIT_T0_E15difference_typeERKS8_SB_ _ZN9__gnu_cxxmiIPhSt6vectorIhSaIhEEEENS_17__normal_iteratorIT_T0_E15difference_typeERKS8_SB_ .text$_ZNSt12_Vector_baseIhSaIhEE11_M_allocateEy _ZNSt12_Vector_baseIhSaIhEE11_M_allocateEy .text$_ZSt12__to_addressIhEPT_S1_ _ZSt12__to_addressIhEPT_S1_ .text$_ZNSt6vectorIhSaIhEE11_S_relocateEPhS2_S2_RS0_ _ZNSt6vectorIhSaIhEE11_S_relocateEPhS2_S2_RS0_ _ZNSt12_Vector_baseIZ4mainE8TestCaseSaIS0_EE12_Vector_implC2ERKS1_ _ZNSt12_Vector_baseIZ4mainE8TestCaseSaIS0_EE12_Vector_implC1ERKS1_ _ZNSt12_Vector_baseIZ4mainE8TestCaseSaIS0_EE13_M_deallocateEPS0_y _ZNKSt16initializer_listIZ4mainE8TestCaseE4sizeEv _ZNSt6vectorIZ4mainE8TestCaseSaIS0_EE17_S_check_init_lenEyRKS1_ _ZNSt12_Vector_baseIZ4mainE8TestCaseSaIS0_EE11_M_allocateEy _ZSt22__uninitialized_copy_aIPKZ4mainE8TestCasePS0_S0_ET0_T_S5_S4_RSaIT1_E _ZSt8_DestroyIPZ4mainE8TestCaseEvT_S2_ .text$_ZNSt15__new_allocatorIhE10deallocateEPhy _ZNSt15__new_allocatorIhE10deallocateEPhy .text$_ZNSt12_Destroy_auxILb1EE9__destroyIPhEEvT_S3_ _ZNSt12_Destroy_auxILb1EE9__destroyIPhEEvT_S3_ .text$_ZNKSt6vectorIhSaIhEE8max_sizeEv _ZNKSt6vectorIhSaIhEE8max_sizeEv .text$_ZSt3maxIyERKT_S2_S2_ _ZSt3maxIyERKT_S2_S2_ .text$_ZN9__gnu_cxx17__normal_iteratorIPhSt6vectorIhSaIhEEEC1ERKS1_ _ZN9__gnu_cxx17__normal_iteratorIPhSt6vectorIhSaIhEEEC1ERKS1_ .text$_ZNK9__gnu_cxx17__normal_iteratorIPhSt6vectorIhSaIhEEE4baseEv _ZNK9__gnu_cxx17__normal_iteratorIPhSt6vectorIhSaIhEEE4baseEv .text$_ZSt12__relocate_aIPhS0_SaIhEET0_T_S3_S2_RT1_ _ZSt12__relocate_aIPhS0_SaIhEET0_T_S3_S2_RT1_ _ZNSt12_Vector_baseIZ4mainE8TestCaseSaIS0_EE17_Vector_impl_dataC2Ev _ZNSt6vectorIZ4mainE8TestCaseSaIS0_EE11_S_max_sizeERKS1_ _ZSt18uninitialized_copyIPKZ4mainE8TestCasePS0_ET0_T_S5_S4_ _ZNSt12_Destroy_auxILb0EE9__destroyIPZ4mainE8TestCaseEEvT_S4_ .text$_ZNSt6vectorIhSaIhEE11_S_max_sizeERKS0_ _ZNSt6vectorIhSaIhEE11_S_max_sizeERKS0_ .text$_ZNKSt12_Vector_baseIhSaIhEE19_M_get_Tp_allocatorEv _ZNKSt12_Vector_baseIhSaIhEE19_M_get_Tp_allocatorEv .text$_ZNSt15__new_allocatorIhE8allocateEyPKv _ZNSt15__new_allocatorIhE8allocateEyPKv .text$_ZSt12__niter_baseIPhET_S1_ _ZSt12__niter_baseIPhET_S1_ .text$_ZSt14__relocate_a_1IhhENSt9enable_ifIXsrSt24__is_bitwise_relocatableIT_vE5valueEPS2_E4typeES4_S4_S4_RSaIT0_E _ZSt14__relocate_a_1IhhENSt9enable_ifIXsrSt24__is_bitwise_relocatableIT_vE5valueEPS2_E4typeES4_S4_S4_RSaIT0_E _ZNSt15__new_allocatorIZ4mainE8TestCaseE10deallocateEPS0_y .text$_ZSt3minIyERKT_S2_S2_ _ZSt3minIyERKT_S2_S2_ _ZNSt15__new_allocatorIZ4mainE8TestCaseE8allocateEyPKv _ZNSt20__uninitialized_copyILb0EE13__uninit_copyIPKZ4mainE8TestCasePS2_EET0_T_S7_S6_ _ZSt11__addressofIZ4mainE8TestCaseEPT_RS1_ _ZSt8_DestroyIZ4mainE8TestCaseEvPT_ _ZSt16__do_uninit_copyIPKZ4mainE8TestCasePS0_ET0_T_S5_S4_ _ZZ4mainEN8TestCaseC2ERKS_ _ZZ4mainEN8TestCaseC1ERKS_ _ZSt10_ConstructIZ4mainE8TestCaseJRKS0_EEvPT_DpOT0_ _ZSt7forwardIRKZ4mainE8TestCaseEOT_RNSt16remove_referenceIS3_E4typeE _ZNSt8__detail30__integer_to_chars_is_unsignedIjEE _ZNSt8__detail30__integer_to_chars_is_unsignedImEE _ZNSt8__detail30__integer_to_chars_is_unsignedIyEE .xdata$_ZnwyPv .pdata$_ZnwyPv .xdata$_ZdlPvS_ .pdata$_ZdlPvS_ .xdata$_ZNSt11char_traitsIcE6lengthEPKc .pdata$_ZNSt11char_traitsIcE6lengthEPKc .xdata$_ZStanSt13_Ios_FmtflagsS_ .pdata$_ZStanSt13_Ios_FmtflagsS_ .xdata$_ZStorSt13_Ios_FmtflagsS_ .pdata$_ZStorSt13_Ios_FmtflagsS_ .xdata$_ZStcoSt13_Ios_Fmtflags .pdata$_ZStcoSt13_Ios_Fmtflags .xdata$_ZStoRRSt13_Ios_FmtflagsS_ .pdata$_ZStoRRSt13_Ios_FmtflagsS_ .xdata$_ZStaNRSt13_Ios_FmtflagsS_ .pdata$_ZStaNRSt13_Ios_FmtflagsS_ .xdata$_ZNSt8ios_base4setfESt13_Ios_Fmtflags .pdata$_ZNSt8ios_base4setfESt13_Ios_Fmtflags .xdata$_ZNSt8ios_base4setfESt13_Ios_FmtflagsS0_ .pdata$_ZNSt8ios_base4setfESt13_Ios_FmtflagsS0_ .xdata$_ZSt9uppercaseRSt8ios_base .pdata$_ZSt9uppercaseRSt8ios_base .xdata$_ZSt3hexRSt8ios_base .pdata$_ZSt3hexRSt8ios_base .xdata$_ZSt4setwi .pdata$_ZSt4setwi .xdata$_ZNSt12_Vector_baseIhSaIhEE12_Vector_implD1Ev .pdata$_ZNSt12_Vector_baseIhSaIhEE12_Vector_implD1Ev .xdata$_ZNSt12_Vector_baseIhSaIhEEC2Ev .pdata$_ZNSt12_Vector_baseIhSaIhEEC2Ev .xdata$_ZNSt6vectorIhSaIhEEC1Ev .pdata$_ZNSt6vectorIhSaIhEEC1Ev .xdata$_ZN9__gnu_cxx11char_traitsIcE6lengthEPKc .pdata$_ZN9__gnu_cxx11char_traitsIcE6lengthEPKc .xdata$_ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_Alloc_hiderD1Ev .pdata$_ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_Alloc_hiderD1Ev .xdata$_ZNKSt6vectorIhSaIhEE4sizeEv .pdata$_ZNKSt6vectorIhSaIhEE4sizeEv .xdata$_ZNKSt6vectorIhSaIhEEixEy .pdata$_ZNKSt6vectorIhSaIhEEixEy .xdata$_ZNSt12_Vector_baseIhSaIhEE12_Vector_implC1Ev .pdata$_ZNSt12_Vector_baseIhSaIhEE12_Vector_implC1Ev .xdata$_ZNSt12_Vector_baseIhSaIhEED2Ev .pdata$_ZNSt12_Vector_baseIhSaIhEED2Ev .xdata$_ZNSt6vectorIhSaIhEED1Ev .pdata$_ZNSt6vectorIhSaIhEED1Ev .xdata$_ZNSt6vectorIhSaIhEE9push_backERKh .pdata$_ZNSt6vectorIhSaIhEE9push_backERKh .xdata$_ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEC1IS3_EEPKcRKS3_ .pdata$_ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEC1IS3_EEPKcRKS3_ .xdata$_ZSt7setfillIcESt8_SetfillIT_ES1_ .pdata$_ZSt7setfillIcESt8_SetfillIT_ES1_ .xdata$_ZN9__gnu_cxx11char_traitsIcE2eqERKcS3_ .pdata$_ZN9__gnu_cxx11char_traitsIcE2eqERKcS3_ .xdata$_ZNSt15__new_allocatorIcED2Ev .pdata$_ZNSt15__new_allocatorIcED2Ev .xdata$_ZZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_M_constructIPKcEEvT_S8_St20forward_iterator_tagEN6_GuardC1EPS4_ .pdata$_ZZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_M_constructIPKcEEvT_S8_St20forward_iterator_tagEN6_GuardC1EPS4_ .xdata$_ZZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_M_constructIPKcEEvT_S8_St20forward_iterator_tagEN6_GuardD1Ev .pdata$_ZZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_M_constructIPKcEEvT_S8_St20forward_iterator_tagEN6_GuardD1Ev .xdata$_ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_M_constructIPKcEEvT_S8_St20forward_iterator_tag .pdata$_ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_M_constructIPKcEEvT_S8_St20forward_iterator_tag .xdata$_ZNSt12_Vector_baseIhSaIhEE17_Vector_impl_dataC2Ev .pdata$_ZNSt12_Vector_baseIhSaIhEE17_Vector_impl_dataC2Ev .xdata$_ZNSt15__new_allocatorIhED2Ev .pdata$_ZNSt15__new_allocatorIhED2Ev .xdata$_ZNSt12_Vector_baseIhSaIhEE13_M_deallocateEPhy .pdata$_ZNSt12_Vector_baseIhSaIhEE13_M_deallocateEPhy .xdata$_ZNSt12_Vector_baseIhSaIhEE19_M_get_Tp_allocatorEv .pdata$_ZNSt12_Vector_baseIhSaIhEE19_M_get_Tp_allocatorEv .xdata$_ZZNSt6vectorIhSaIhEE17_M_realloc_appendIJRKhEEEvDpOT_EN6_GuardC1EPhyRS0_ .pdata$_ZZNSt6vectorIhSaIhEE17_M_realloc_appendIJRKhEEEvDpOT_EN6_GuardC1EPhyRS0_ .xdata$_ZZNSt6vectorIhSaIhEE17_M_realloc_appendIJRKhEEEvDpOT_EN6_GuardD1Ev .pdata$_ZZNSt6vectorIhSaIhEE17_M_realloc_appendIJRKhEEEvDpOT_EN6_GuardD1Ev .xdata$_ZNSt6vectorIhSaIhEE17_M_realloc_appendIJRKhEEEvDpOT_ .pdata$_ZNSt6vectorIhSaIhEE17_M_realloc_appendIJRKhEEEvDpOT_ .xdata$_ZSt8_DestroyIPhEvT_S1_ .pdata$_ZSt8_DestroyIPhEvT_S1_ .xdata$_ZSt7forwardIRKhEOT_RNSt16remove_referenceIS2_E4typeE .pdata$_ZSt7forwardIRKhEOT_RNSt16remove_referenceIS2_E4typeE .xdata$_ZNKSt6vectorIhSaIhEE12_M_check_lenEyPKc .pdata$_ZNKSt6vectorIhSaIhEE12_M_check_lenEyPKc .xdata$_ZNSt6vectorIhSaIhEE3endEv .pdata$_ZNSt6vectorIhSaIhEE3endEv .xdata$_ZNSt6vectorIhSaIhEE5beginEv .pdata$_ZNSt6vectorIhSaIhEE5beginEv .xdata$_ZN9__gnu_cxxmiIPhSt6vectorIhSaIhEEEENS_17__normal_iteratorIT_T0_E15difference_typeERKS8_SB_ .pdata$_ZN9__gnu_cxxmiIPhSt6vectorIhSaIhEEEENS_17__normal_iteratorIT_T0_E15difference_typeERKS8_SB_ .xdata$_ZNSt12_Vector_baseIhSaIhEE11_M_allocateEy .pdata$_ZNSt12_Vector_baseIhSaIhEE11_M_allocateEy .xdata$_ZSt12__to_addressIhEPT_S1_ .pdata$_ZSt12__to_addressIhEPT_S1_ .xdata$_ZNSt6vectorIhSaIhEE11_S_relocateEPhS2_S2_RS0_ .pdata$_ZNSt6vectorIhSaIhEE11_S_relocateEPhS2_S2_RS0_ .xdata$_ZNSt15__new_allocatorIhE10deallocateEPhy .pdata$_ZNSt15__new_allocatorIhE10deallocateEPhy .xdata$_ZNSt12_Destroy_auxILb1EE9__destroyIPhEEvT_S3_ .pdata$_ZNSt12_Destroy_auxILb1EE9__destroyIPhEEvT_S3_ .xdata$_ZNKSt6vectorIhSaIhEE8max_sizeEv .pdata$_ZNKSt6vectorIhSaIhEE8max_sizeEv .xdata$_ZSt3maxIyERKT_S2_S2_ .pdata$_ZSt3maxIyERKT_S2_S2_ .xdata$_ZN9__gnu_cxx17__normal_iteratorIPhSt6vectorIhSaIhEEEC1ERKS1_ .pdata$_ZN9__gnu_cxx17__normal_iteratorIPhSt6vectorIhSaIhEEEC1ERKS1_ .xdata$_ZNK9__gnu_cxx17__normal_iteratorIPhSt6vectorIhSaIhEEE4baseEv .pdata$_ZNK9__gnu_cxx17__normal_iteratorIPhSt6vectorIhSaIhEEE4baseEv .xdata$_ZSt12__relocate_aIPhS0_SaIhEET0_T_S3_S2_RT1_ .pdata$_ZSt12__relocate_aIPhS0_SaIhEET0_T_S3_S2_RT1_ .xdata$_ZNSt6vectorIhSaIhEE11_S_max_sizeERKS0_ .pdata$_ZNSt6vectorIhSaIhEE11_S_max_sizeERKS0_ .xdata$_ZNKSt12_Vector_baseIhSaIhEE19_M_get_Tp_allocatorEv .pdata$_ZNKSt12_Vector_baseIhSaIhEE19_M_get_Tp_allocatorEv .xdata$_ZNSt15__new_allocatorIhE8allocateEyPKv .pdata$_ZNSt15__new_allocatorIhE8allocateEyPKv .xdata$_ZSt12__niter_baseIPhET_S1_ .pdata$_ZSt12__niter_baseIPhET_S1_ .xdata$_ZSt14__relocate_a_1IhhENSt9enable_ifIXsrSt24__is_bitwise_relocatableIT_vE5valueEPS2_E4typeES4_S4_S4_RSaIT0_E .pdata$_ZSt14__relocate_a_1IhhENSt9enable_ifIXsrSt24__is_bitwise_relocatableIT_vE5valueEPS2_E4typeES4_S4_S4_RSaIT0_E .xdata$_ZSt3minIyERKT_S2_S2_ .pdata$_ZSt3minIyERKT_S2_S2_ simple_crc16_test.cpp __do_global_dtors __do_global_ctors .rdata$.refptr.__CTOR_LIST__ initialized __dyn_tls_init .rdata$.refptr._CRT_MT __tlregdtor __dyn_tls_dtor __report_error maxSections mark_section_writable restore_modified_sections __write_memory do_pseudo_reloc _pei386_runtime_relocator was_init.0 .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST__ stUserMathErr __mingw_raise_matherr __mingw_setusermatherr _gnu_exception_handler __mingwthr_cs __mingwthr_cs_init key_dtor_list ___w64_mingwthr_add_key_dtor ___w64_mingwthr_remove_key_dtor __mingwthr_run_key_dtors __mingw_TLScallback pseudo-reloc-list.c _ValidateImageBase _FindPESection _FindPESectionByName __mingw_GetSectionForAddress __mingw_GetSectionCount _FindPESectionExec _GetPEImageBase _IsNonwritableInCurrentImage __mingw_enum_import_library_names .debug_info .debug_abbrev .debug_line .debug_line_str .debug_aranges .debug_str .debug_frame __p__fmode .rdata$.refptr.__imp__fmode __p__commode .rdata$.refptr.__imp__commode mingw_set_invalid_parameter_handler _set_invalid_parameter_handler mingw_get_invalid_parameter_handler _get_invalid_parameter_handler invalid_parameter_handler.c __acrt_iob_func __FRAME_END__ register_frame_ctor .text.startup .xdata.startup .pdata.startup .ctors.65535 .rdata_runtime_pseudo_reloc _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE13_M_local_dataEv ___RUNTIME_PSEUDO_RELOC_LIST__ _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE13_M_set_lengthEy __imp__ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE7_M_dataEPc _ZStlsISt11char_traitsIcEERSt13basic_ostreamIcT_ES5_PKc __imp_abort __lib64_libkernel32_a_iname __cxa_rethrow __data_start__ ___DTOR_LIST__ __imp__fmode SetUnhandledExceptionFilter _head_lib64_libmsvcrt_def_a .refptr.__mingw_initltsdrot_force _ZStlsIcSt11char_traitsIcEERSt13basic_ostreamIT_T0_ES6_St5_Setw __imp_calloc __imp___p__fmode ___tls_start__ __imp__ZSt20__throw_length_errorPKc .refptr.__native_startup_state __imp__ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE9_M_createERyy GetLastError __imp__ZNSolsEi __rt_psrelocs_start .refptr.__globallocalestatus __imp__ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE13_M_local_dataEv __dll_characteristics__ __size_of_stack_commit__ __mingw_module_is_dll __iob_func __size_of_stack_reserve__ __major_subsystem_version__ ___crt_xl_start__ __imp_DeleteCriticalSection __imp__set_invalid_parameter_handler .refptr.__CTOR_LIST__ VirtualQuery ___crt_xi_start__ .refptr.__imp__fmode __imp__ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE13_M_set_lengthEy __imp__amsg_exit ___crt_xi_end__ _ZNSolsEt .refptr.__imp___initenv _tls_start _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEC1ERKS4_ .refptr._matherr _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE11_M_capacityEy .refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __imp__ZStlsIcSt11char_traitsIcEERSt13basic_ostreamIT_T0_ES6_St8_SetfillIS3_E __mingw_oldexcpt_handler _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE10_M_disposeEv TlsGetValue __imp_strtol __imp___cxa_rethrow _head_libstdc___6_dll __bss_start__ __imp__ZStlsISt11char_traitsIcEERSt13basic_ostreamIcT_ES5_PKc __imp___C_specific_handler _ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE5c_strEv ___RUNTIME_PSEUDO_RELOC_LIST_END__ __size_of_heap_commit__ __imp__ZSt28__throw_bad_array_new_lengthv __imp_GetLastError .refptr._dowildcard __mingw_initltsdrot_force __imp_free __globallocalestatus __imp__ZSt17__throw_bad_allocv LoadLibraryA _ZSt17__throw_bad_allocv .refptr.__mingw_app_type __mingw_initltssuo_force VirtualProtect ___crt_xd_end__ ___crt_xp_start__ __imp_LeaveCriticalSection _ZNSolsEPFRSoS_E __C_specific_handler _ZSt20__throw_length_errorPKc .refptr.__mingw_oldexcpt_handler .refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ __cxa_end_catch ___crt_xp_end__ .refptr._ZSt4endlIcSt11char_traitsIcEERSt13basic_ostreamIT_T0_ES6_ __minor_os_version__ EnterCriticalSection __imp__ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE5c_strEv _MINGW_INSTALL_DEBUG_MATHERR __imp__ZNSolsEPFRSoS_E .refptr.__xi_a .refptr._CRT_MT __imp__ZdlPvy __section_alignment__ __native_dllmain_reason __imp___cxa_begin_catch _tls_used ___crt_xd_start__ __IAT_end__ __imp_memcpy __imp__ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_Alloc_hiderC1EPcRKS3_ __RUNTIME_PSEUDO_RELOC_LIST__ .weak.__register_frame_info.hmod_libgcc __lib64_libmsvcrt_def_a_iname _ZNSolsEPFRSt8ios_baseS0_E __deregister_frame_info __imp__ZStlsIcSt11char_traitsIcESaIcEERSt13basic_ostreamIT_T0_ES7_RKNSt7__cxx1112basic_stringIS4_S5_T1_EE .refptr._newmode _Unwind_Resume __imp___cxa_end_catch __data_end__ __imp_fwrite __imp__Unwind_Resume __CTOR_LIST__ __imp___getmainargs _head_lib64_libkernel32_a libstdc___6_dll_iname GetModuleHandleA __bss_end__ __native_vcclrit_reason ___crt_xc_end__ .refptr.__mingw_initltssuo_force _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE12_Alloc_hiderC1EPcRKS3_ .refptr.__native_startup_lock __imp___gxx_personality_seh0 __imp_EnterCriticalSection hmod_libgcc _ZNSolsEi _tls_index __native_startup_state ___crt_xc_start__ libgcc_s_seh_1_dll_iname _ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE6substrEyy __imp_GetProcAddress ___CTOR_LIST__ .refptr.__dyn_tls_init_callback __imp_signal _ZSt28__throw_bad_array_new_lengthv __imp__ZSt19__throw_logic_errorPKc .refptr.__mingw_initltsdyn_force __imp_GetModuleHandleA .refptr._ZSt4cout __imp__ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE7_M_dataEv .refptr.__ImageBase __imp_strlen __imp_malloc _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEED1Ev .refptr._gnu_exception_handler __file_alignment__ __imp__ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE10_M_disposeEv __imp_InitializeCriticalSection _ZSt19__throw_logic_errorPKc __getmainargs InitializeCriticalSection __imp_exit _ZStlsIcSt11char_traitsIcESaIcEERSt13basic_ostreamIT_T0_ES7_RKNSt7__cxx1112basic_stringIS4_S5_T1_EE _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE7_M_dataEPc __imp_vfprintf __nm__ZSt4cout __major_os_version__ __mingw_pcinit _head_libgcc_s_seh_1_dll __imp___initenv __imp__ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEED1Ev __IAT_start__ __imp__ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE6substrEyy .weak.__deregister_frame_info.hmod_libgcc __imp__cexit __imp_SetUnhandledExceptionFilter __imp__ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE13_S_copy_charsEPcPKcS7_ __imp__ZSt4endlIcSt11char_traitsIcEERSt13basic_ostreamIT_T0_ES6_ __imp__onexit GetProcAddress __DTOR_LIST__ __set_app_type __imp_Sleep LeaveCriticalSection __imp___setusermatherr __size_of_heap_reserve__ ___crt_xt_start__ __subsystem__ _amsg_exit __imp_TlsGetValue __setusermatherr .refptr._commode __imp_fprintf __mingw_pcppinit __imp___p__commode __imp_FreeLibrary __imp__ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE11_M_capacityEy __imp_VirtualProtect _ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE7_M_dataEv __imp__ZStlsIcSt11char_traitsIcEERSt13basic_ostreamIT_T0_ES6_St5_Setw ___tls_end__ __imp_VirtualQuery __imp__initterm __cxa_begin_catch _dowildcard __mingw_initltsdyn_force __imp___iob_func __dyn_tls_init_callback __imp__ZSt4cout _initterm __imp_strncmp .refptr._fmode __imp___acrt_iob_func __major_image_version__ __loader_flags__ __fu0__ZSt4cout __imp__ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE6lengthEv __imp__ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEEC1ERKS4_ _ZSt4endlIcSt11char_traitsIcEERSt13basic_ostreamIT_T0_ES6_ .refptr.__imp__commode ___chkstk_ms __native_startup_lock __imp__ZNSolsEPFRSt8ios_baseS0_E __rt_psrelocs_end _ZStlsIcSt11char_traitsIcEERSt13basic_ostreamIT_T0_ES6_St8_SetfillIS3_E __imp__get_invalid_parameter_handler __minor_subsystem_version__ __minor_image_version__ _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE9_M_createERyy __imp___set_app_type .refptr.__xc_a __imp__ZNSolsEt __imp_LoadLibraryA .refptr.__xi_z .refptr._MINGW_INSTALL_DEBUG_MATHERR FreeLibrary __imp__commode DeleteCriticalSection __RUNTIME_PSEUDO_RELOC_LIST_END__ _ZNSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE13_S_copy_charsEPcPKcS7_ __register_frame_info .refptr.__xc_z ___crt_xt_end__ __gxx_personality_seh0 _ZNKSt7__cxx1112basic_stringIcSt11char_traitsIcESaIcEE6lengthEv __imp__Znwy __mingw_app_type 