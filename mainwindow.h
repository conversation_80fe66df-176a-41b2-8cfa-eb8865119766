#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QSerialPort>
#include <QSerialPortInfo>
#include <QString>
#include <QTimer>
#include "modbusserial.h"
#include "serialmanager.h"
#include "testcontroller.h" // Include the new TestController

#include "modbustestcase.h"
#include "crcutils.h"

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
class QTableWidgetItem;
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void on_actionExit_triggered();
    void on_actionConnectA_triggered();
    void on_actionConnectB_triggered();
    void on_actionDisconnectA_triggered();
    void on_actionDisconnectB_triggered();
    void on_actionRefresh_triggered();

    void on_actionMapping_triggered();
    void on_actionSetValue_triggered();
    void on_actionImportConfig_triggered();
    void on_actionExportConfig_triggered();
    void on_pushButtonReadA_clicked();
    void on_pushButtonWriteA_clicked();
    void on_pushButtonReadB_clicked();
    void on_pushButtonWriteB_clicked();
    void on_checkBoxAutoA_clicked(bool checked);
    void on_checkBoxAutoB_clicked(bool checked);
    void on_pushButtonSendA_clicked();
    void on_pushButtonSendB_clicked();
    
    // 测试列表相关槽函数
    void on_pushButtonAddTest_clicked();
    void on_pushButtonRemoveTest_clicked();
    void on_pushButtonClearTests_clicked();
    void on_pushButtonImport_clicked();
    void on_pushButtonExport_clicked();
    void importTestCasesFromArray(const QJsonArray &array);
    void importTestStepsFromMappingWindow(const QJsonArray &testStepsArray);
    void on_pushButtonCopyTest_clicked();
    void on_tableWidgetTests_itemSelectionChanged();
    void on_tableWidgetTests_itemChanged(QTableWidgetItem *item);
    void on_lineEditDescription_textChanged(const QString &text);
    void on_lineEditSentA_textChanged(const QString &text);
    void on_lineEditHopeReceiveA_textChanged(const QString &text);
    void on_spinBoxStationA_valueChanged(int value);
    void on_pushButtonTest_clicked();
    void on_checkBoxUseCRC16_toggled(bool checked); // Added for CRC16 checkbox
    
    void handleSerialMessage(const QString &portId, const QString &message);
    void handleSerialError(const QString &portId, const QString &error);
    void handleSerialConnectionChanged(const QString &portId, bool connected);
    void updateTestCaseTable();

    // Slots to handle signals from TestController
    void handleTestCaseFinished(int index, const ModbusTestCase &testCase, bool passed);
    void handleAllTestsFinished();
    void logTestMessage(const QString &message);

private:
    void InitialSetting();
    void SearchSerialPorts();
    QByteArray parseHexString(const QString &hexString);
    
    // 测试列表相关函数
    void initTestListUI();
    void initModbusTestListUI();
    QByteArray hexStringToByteArray(const QString &hexString);
    QString byteArrayToHexString(const QByteArray &data);
    bool validateTestCase(ModbusTestCase &testCase);
    void updateTableRow(int row, const ModbusTestCase &testCase);
    void updateModbusTableRow(int row, const ModbusTestCase &testCase);
    void checkUiComponents();
    
    // Modbus测试列表相关槽函数
    void on_pushButtonAddModbusTest_clicked();
    void on_pushButtonRemoveModbusTest_clicked();
    void on_pushButtonCopyModbusTest_clicked();
    void on_pushButtonClearModbusTests_clicked();
    void on_pushButtonImportModbus_clicked();
    void on_pushButtonExportModbus_clicked();
    void on_pushButtonModbusTest_clicked();
    void on_pushButtonGenerateModbusReport_clicked();
    void on_tableWidgetModbusTests_itemSelectionChanged();
    void on_tableWidgetModbusTests_itemChanged(QTableWidgetItem *item);
    
    // Modbus特有功能
    QByteArray calculateCRC32(const QByteArray &data);
    QByteArray createModbusFrame(int stationId, int functionCode, const QByteArray &data, int crcType);
    
    // Modbus导入辅助函数
    void importModbusTestCasesFromArray(const QJsonArray &array);
    void importModbusTestStepsFromMappingWindow(const QJsonArray &testStepsArray);
    void parseStationIdAndFunctionCode(ModbusTestCase &testCase, const QJsonObject &obj);
    void generateModbusCRC16(ModbusTestCase &testCase);

private:
    Ui::MainWindow *ui;
    SerialManager *m_serialManager;
    TestController *m_testController;
    QTimer *timerAutoReadA;
    QTimer *timerAutoReadB;
    
    // 测试列表相关成员变量
    QString receivedDataA; // These might be removed if TestController handles all data
    QString receivedDataB;
    QList<ModbusTestCase> testCases;
};

#endif // MAINWINDOW_H
