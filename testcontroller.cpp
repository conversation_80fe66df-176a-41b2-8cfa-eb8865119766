#include "testcontroller.h"
#include <QDebug>

TestController::TestController(SerialManager *serialManager, QObject *parent)
    : QObject(parent),
      m_serialManager(serialManager),
      m_currentTestIndex(0),
      m_isRunningTests(false),
      m_delay(100)
{
    connect(&m_testTimer, &QTimer::timeout, this, &TestController::runNextTest);
    connect(&m_testTimeoutTimer, &QTimer::timeout, this, &TestController::onTestTimeout);
    connect(m_serialManager, &SerialManager::dataReceived, this, &TestController::handleSerialData);
}

TestController::~TestController()
{
}

void TestController::setTestCases(const QList<ModbusTestCase> &cases)
{
    m_testCases = cases;
}

void TestController::startTests(int delay)
{
    if (m_isRunningTests || m_testCases.isEmpty()) {
        return;
    }

    m_currentTestIndex = 0;
    m_isRunningTests = true;
    m_delay = delay;
    emit logMessage("--- Test Started ---");
    m_testTimer.start(m_delay);
}

void TestController::stopTests()
{
    m_isRunningTests = false;
    m_testTimer.stop();
    m_testTimeoutTimer.stop();
    emit logMessage("--- Test Stopped by User ---");
    emit allTestsFinished();
}

bool TestController::isRunning() const
{
    return m_isRunningTests;
}

void TestController::runNextTest()
{
    m_testTimer.stop();

    if (!m_isRunningTests || m_currentTestIndex >= m_testCases.size()) {
        m_isRunningTests = false;
        emit logMessage("--- All Tests Finished ---");
        emit allTestsFinished();
        return;
    }

    ModbusTestCase &currentCase = m_testCases[m_currentTestIndex];
    emit logMessage(QString("Running Test %1: %2").arg(m_currentTestIndex + 1).arg(currentCase.description));

    m_receivedDataA.clear();
    m_receivedDataB.clear();

    QString portId = currentCase.targetSerialPort;
    if (portId.isEmpty()) { // Fallback for older formats
        portId = currentCase.sentDataB.isEmpty() ? "A" : "B";
    }

    QString dataHex = portId == "A" ? currentCase.sentDataA : currentCase.sentDataB;
    if (dataHex.isEmpty()) { // Fallback for older formats
        dataHex = currentCase.sendData;
    }

    if (!m_serialManager->isConnected(portId)) {
        emit logMessage(QString("Error: Port %1 is not connected. Skipping test.").arg(portId));
        currentCase.result = "Skipped";
        emit testCaseFinished(m_currentTestIndex, currentCase, false);
        m_currentTestIndex++;
        m_testTimer.start(m_delay);
        return;
    }
    
    // This is a simplified hex-to-byte-array conversion. A more robust one from MainWindow could be used.
    QByteArray dataToSend = QByteArray::fromHex(dataHex.remove(' ').toLatin1());

    m_serialManager->sendData(portId, dataToSend);
    m_testTimeoutTimer.start(currentCase.timeout > 0 ? currentCase.timeout : 1000);
}

void TestController::onTestTimeout()
{
    m_testTimeoutTimer.stop();
    emit logMessage("Test timed out.");

    bool passed = checkResult();
    
    m_currentTestIndex++;
    m_testTimer.start(m_delay);
}

void TestController::handleSerialData(const QString &portId, const QString &message)
{
    if (!m_isRunningTests) return;

    QString processedMessage = message.trimmed();
    if(processedMessage.startsWith("RX:")) {
        processedMessage = processedMessage.mid(3).trimmed();
    }

    if (portId == "A") {
        m_receivedDataA += processedMessage;
    } else if (portId == "B") {
        m_receivedDataB += processedMessage;
    }

    // Optional: check result immediately on data arrival if needed
}

bool TestController::checkResult()
{
    if (m_currentTestIndex >= m_testCases.size()) return false;

    ModbusTestCase &currentCase = m_testCases[m_currentTestIndex];
    QString portId = currentCase.targetSerialPort;
    if (portId.isEmpty()) {
        portId = currentCase.sentDataB.isEmpty() ? "A" : "B";
    }

    QString expectedResponse = portId == "A" ? currentCase.expectReceiveA : currentCase.expectReceiveB;
    if (expectedResponse.isEmpty()) { // Fallback
        expectedResponse = currentCase.expectedResponse;
    }

    QString actualReceived = portId == "A" ? m_receivedDataA : m_receivedDataB;

    currentCase.actualReceiveA = m_receivedDataA;
    currentCase.actualReceiveB = m_receivedDataB;

    bool passed = false;
    if (expectedResponse.isEmpty()) {
        passed = true; // No response expected, so pass.
        emit logMessage("Test passed (no expected response).");
    } else {
        if (actualReceived.toUpper().contains(expectedResponse.toUpper())) {
            passed = true;
            emit logMessage("Test passed.");
        } else {
            passed = false;
            emit logMessage(QString("Test failed. Expected to contain '%1', but got '%2'").arg(expectedResponse).arg(actualReceived));
        }
    }

    currentCase.result = passed ? "Passed" : "Failed";
    emit testCaseFinished(m_currentTestIndex, currentCase, passed);
    return passed;
}
