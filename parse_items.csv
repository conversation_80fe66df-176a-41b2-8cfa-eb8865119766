﻿名称,描述,数据类型,读取方向,采集周期,存储器类型序号,存储器地址,解析方式,位偏移量,BCD解析,地址,处理方式,最小值,最大值,单位,数据处理参数,特性选择,投退选择,保护投退,期望数据,期望数据(定值文件读取),显示数据,比对
CT一次额定电流,额定电流（一次）InP,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40001,原始数据,1,6000,A,1,0,0,---,50,50,50.000,✔
线路额定电流（IL）,基准电流（二次）,UINT16,读写,1000,3,0,2字节 无符号 先高后低,0,0,40002,D1(智能除法),0.1,7200,A,1,0,0,---,32,32,32.000,✔
高定时限过流 ,投退标志,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40003,原始数据,0,2,,1,0,0,---,0.000,,0.000,✔
I>>,倍数,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40003,除以处理参数,0.1,8,IL,10,0,0,---,0.100,,0.100,✔
tI>>,延时,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40004,除以处理参数,0.1,60,S,10,0,0,---,0.100,,0.100,✔
低设定过流特性,特性标志,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40005,原始数据,0,1,,1,1,0,---,1,DEF TIME,1.000,✔
低定时限过流,投退标志,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40005,原始数据,0,2,,1,0,1,TRIP,1,TRIP,1.000,✔
过流定值(A),倍数,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40006,除以处理参数,0.1,8,IL,10,0,0,---,1.0,1.0IL,1.000,✔
过流延时(S),延时,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40007,除以处理参数,0.1,60,S,10,0,0,---,5,5S,5.000,✔
低反时限过流,电流投退,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40008,原始数据,0,2,,1,0,0,---,0.000,,0.000,✔
CHAR,特性曲线,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40008,原始数据,0,7,,1,0,0,---,0.000,,0.000,✔
Is,启动倍数,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40009,除以处理参数,0.1,2,IL,10,0,0,---,0.100,,0.100,✔
Tp,常数,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40010,除以处理参数,0.025,1.5,,1000,0,0,---,0.025,,0.025,✔
Tre,返回常数,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40011,原始数据,0,3.2,,1,0,0,---,0.000,,0.000,✔
高定时限零序过流,投退标志,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40012,原始数据,0,2,,1,0,0,---,0.000,,0.000,✔
IE>>,倍数,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40012,除以处理参数,0.1,8,IL,10,0,0,---,0.100,,0.100,✔
tIE>>,延时,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40013,除以处理参数,0.1,25.5,,10,0,0,---,0.100,,0.100,✔
低设定零序过流特性,特性标志,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40014,原始数据,0,1,,1,0,0,---,1,—,1.000,✔
低定时限零序过流,投退标志,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40014,原始数据,0,2,,1,0,0,---,1,—,1.000,✔
零序过流定值(A),倍数 IE>>,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40015,除以处理参数,0.1,8,,10,0,0,---,1,—,1.000,✔
零序过流延时(S),延时 tIE>>,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40015,除以处理参数,0.1,25.5,,10,0,0,---,0.5,—,0.500,✔
低反时限零序过流,投退标志,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40016,原始数据,0,2,,1,0,0,---,0.000,,0.000,✔
CHARE,特性曲线,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40016,原始数据,0,2,,1,0,0,---,0.000,,0.000,✔
IsE,启动倍数,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40017,除以处理参数,0.1,2,,10,0,0,---,0.100,,0.100,✔
TpE,常数,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40018,除以处理参数,0.025,1.5,,1000,0,0,---,0.025,,0.025,✔
漏电保护,设定标志,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40019,原始数据,0,2,,1,0,0,---,0,OFF,0.000,✔
额定漏电流(IΔn),额定电流,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40019,除以处理参数,0,0,A,10,0,0,---,0.3,—,0.300,✔
漏电保护告警值(IΔa),报警倍数,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40020,除以处理参数,0.1,10,,10,0,0,---,0.2,—,0.200,✔
漏电保护跳闸值(IΔd),跳闸倍数,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40020,除以处理参数,0.1,10,,10,0,0,---,0.3,—,0.300,✔
漏电保护延时(tIΔ),延时,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40021,除以处理参数,0,100,,10,0,0,---,0.300,,0.300,✔
数字温度,设定标志,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40022,原始数据,0,2,,1,0,0,---,0.000,,0.000,✔
TDd,节点类型,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40022,原始数据,1,120,℃,1,0,0,---,1.000,,1.000,✔
tTD,延时,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40023,原始数据,1,250,S,1,0,0,---,1.000,,1.000,✔
外部联动,设定标志,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40024,原始数据,0,2,,1,0,0,---,0.000,,0.000,✔
输入节点,节点类型,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40024,原始数据,0,1,,1,0,0,---,0.000,,0.000,✔
texf,延时,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40025,除以处理参数,0.1,25.5,,10,0,0,---,0.100,,0.100,✔
复位方式,,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40025,原始数据,0,1,,1,0,0,---,0.000,,0.000,✔
模拟量变送,,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40026,原始数据,0,1,,1,0,0,---,0.000,,0.000,✔
保护连跳,,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40026,原始数据,0,1,,1,0,0,---,0.000,,0.000,✔
遥控,,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40027,原始数据,0,1,,1,0,0,---,0.000,,0.000,✔
面板操作,,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40027,原始数据,0,1,,1,0,0,---,0.000,,0.000,✔
开入操作,,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40028,原始数据,0,1,,1,0,0,---,0.000,,0.000,✔
接触器控制,,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40028,原始数据,0,1,,1,0,0,---,0.000,,0.000,✔
远方复位,远方复位,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40029,原始数据,0,1,,1,0,0,---,0.000,,0.000,✔
密码,修改密码,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40030,原始数据,1,4999,,1,0,0,---,1.000,,1.000,✔
