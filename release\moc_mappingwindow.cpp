/****************************************************************************
** Meta object code from reading C++ file 'mappingwindow.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.8.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../mappingwindow.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'mappingwindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.8.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN13MappingWindowE_t {};
} // unnamed namespace


#ifdef QT_MOC_HAS_STRINGDATA
static constexpr auto qt_meta_stringdata_ZN13MappingWindowE = QtMocHelpers::stringData(
    "MappingWindow",
    "addMapping",
    "",
    "saveMapping",
    "loadMapping",
    "undo",
    "redo",
    "resetTestState",
    "generateTestSteps",
    "editTestStep",
    "showTestStepDetails",
    "onSerialPortChanged",
    "index",
    "onGlobalCrc16CheckBoxToggled",
    "checked",
    "onHideInterfaceCheckBoxToggled",
    "onTestButtonClicked",
    "onClearLogButtonClicked",
    "runNextTest",
    "onTestTimeout",
    "handleSerialAMessageReceived",
    "message",
    "handleSerialBMessageReceived",
    "onTestReportButtonClicked",
    "onMappingListContextMenuRequested",
    "pos",
    "onInsertMappingAt",
    "onDeleteMappingAt",
    "showDeviceContextMenu",
    "editDeviceName",
    "updateDeviceNameInLists",
    "oldName",
    "newName",
    "saveDeviceListsToFile",
    "loadDeviceListsFromFile",
    "onTestStepsListDoubleClicked",
    "QListWidgetItem*",
    "item",
    "onGraphicsViewDoubleClicked",
    "position",
    "editTestStepByIndex",
    "onFlowchartContextMenuRequested",
    "onFlowchartAddMapping",
    "onFlowchartDeleteMapping",
    "onInsertMappingAtPosition",
    "showTestReport",
    "generateTestReport"
);
#else  // !QT_MOC_HAS_STRINGDATA
#error "qtmochelpers.h not found or too old."
#endif // !QT_MOC_HAS_STRINGDATA

Q_CONSTINIT static const uint qt_meta_data_ZN13MappingWindowE[] = {

 // content:
      12,       // revision
       0,       // classname
       0,    0, // classinfo
      36,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,  230,    2, 0x08,    1 /* Private */,
       3,    0,  231,    2, 0x08,    2 /* Private */,
       4,    0,  232,    2, 0x08,    3 /* Private */,
       5,    0,  233,    2, 0x08,    4 /* Private */,
       6,    0,  234,    2, 0x08,    5 /* Private */,
       7,    0,  235,    2, 0x08,    6 /* Private */,
       8,    0,  236,    2, 0x08,    7 /* Private */,
       9,    0,  237,    2, 0x08,    8 /* Private */,
      10,    0,  238,    2, 0x08,    9 /* Private */,
      11,    1,  239,    2, 0x08,   10 /* Private */,
      13,    1,  242,    2, 0x08,   12 /* Private */,
      15,    1,  245,    2, 0x08,   14 /* Private */,
      16,    0,  248,    2, 0x08,   16 /* Private */,
      17,    0,  249,    2, 0x08,   17 /* Private */,
      18,    0,  250,    2, 0x08,   18 /* Private */,
      19,    0,  251,    2, 0x08,   19 /* Private */,
      20,    1,  252,    2, 0x08,   20 /* Private */,
      22,    1,  255,    2, 0x08,   22 /* Private */,
      23,    0,  258,    2, 0x08,   24 /* Private */,
      24,    1,  259,    2, 0x08,   25 /* Private */,
      26,    0,  262,    2, 0x08,   27 /* Private */,
      27,    0,  263,    2, 0x08,   28 /* Private */,
      28,    1,  264,    2, 0x08,   29 /* Private */,
      29,    0,  267,    2, 0x08,   31 /* Private */,
      30,    2,  268,    2, 0x08,   32 /* Private */,
      33,    0,  273,    2, 0x08,   35 /* Private */,
      34,    0,  274,    2, 0x08,   36 /* Private */,
      35,    1,  275,    2, 0x08,   37 /* Private */,
      38,    1,  278,    2, 0x08,   39 /* Private */,
      40,    1,  281,    2, 0x08,   41 /* Private */,
      41,    1,  284,    2, 0x08,   43 /* Private */,
      42,    0,  287,    2, 0x08,   45 /* Private */,
      43,    0,  288,    2, 0x08,   46 /* Private */,
      44,    1,  289,    2, 0x08,   47 /* Private */,
      45,    0,  292,    2, 0x08,   49 /* Private */,
      46,    0,  293,    2, 0x08,   50 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   12,
    QMetaType::Void, QMetaType::Bool,   14,
    QMetaType::Void, QMetaType::Bool,   14,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   21,
    QMetaType::Void, QMetaType::QString,   21,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QPoint,   25,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QPoint,   25,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   31,   32,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 36,   37,
    QMetaType::Void, QMetaType::QPointF,   39,
    QMetaType::Void, QMetaType::Int,   12,
    QMetaType::Void, QMetaType::QPoint,   25,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   39,
    QMetaType::Void,
    QMetaType::QString,

       0        // eod
};

Q_CONSTINIT const QMetaObject MappingWindow::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_ZN13MappingWindowE.offsetsAndSizes,
    qt_meta_data_ZN13MappingWindowE,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_tag_ZN13MappingWindowE_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<MappingWindow, std::true_type>,
        // method 'addMapping'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'saveMapping'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'loadMapping'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'undo'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'redo'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'resetTestState'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'generateTestSteps'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'editTestStep'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'showTestStepDetails'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onSerialPortChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'onGlobalCrc16CheckBoxToggled'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'onHideInterfaceCheckBoxToggled'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'onTestButtonClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onClearLogButtonClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'runNextTest'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onTestTimeout'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'handleSerialAMessageReceived'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'handleSerialBMessageReceived'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'onTestReportButtonClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onMappingListContextMenuRequested'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QPoint &, std::false_type>,
        // method 'onInsertMappingAt'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onDeleteMappingAt'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'showDeviceContextMenu'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QPoint &, std::false_type>,
        // method 'editDeviceName'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'updateDeviceNameInLists'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'saveDeviceListsToFile'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'loadDeviceListsFromFile'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onTestStepsListDoubleClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QListWidgetItem *, std::false_type>,
        // method 'onGraphicsViewDoubleClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QPointF &, std::false_type>,
        // method 'editTestStepByIndex'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'onFlowchartContextMenuRequested'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QPoint &, std::false_type>,
        // method 'onFlowchartAddMapping'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onFlowchartDeleteMapping'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onInsertMappingAtPosition'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'showTestReport'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'generateTestReport'
        QtPrivate::TypeAndForceComplete<QString, std::false_type>
    >,
    nullptr
} };

void MappingWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<MappingWindow *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->addMapping(); break;
        case 1: _t->saveMapping(); break;
        case 2: _t->loadMapping(); break;
        case 3: _t->undo(); break;
        case 4: _t->redo(); break;
        case 5: _t->resetTestState(); break;
        case 6: _t->generateTestSteps(); break;
        case 7: _t->editTestStep(); break;
        case 8: _t->showTestStepDetails(); break;
        case 9: _t->onSerialPortChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 10: _t->onGlobalCrc16CheckBoxToggled((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 11: _t->onHideInterfaceCheckBoxToggled((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 12: _t->onTestButtonClicked(); break;
        case 13: _t->onClearLogButtonClicked(); break;
        case 14: _t->runNextTest(); break;
        case 15: _t->onTestTimeout(); break;
        case 16: _t->handleSerialAMessageReceived((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 17: _t->handleSerialBMessageReceived((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 18: _t->onTestReportButtonClicked(); break;
        case 19: _t->onMappingListContextMenuRequested((*reinterpret_cast< std::add_pointer_t<QPoint>>(_a[1]))); break;
        case 20: _t->onInsertMappingAt(); break;
        case 21: _t->onDeleteMappingAt(); break;
        case 22: _t->showDeviceContextMenu((*reinterpret_cast< std::add_pointer_t<QPoint>>(_a[1]))); break;
        case 23: _t->editDeviceName(); break;
        case 24: _t->updateDeviceNameInLists((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 25: _t->saveDeviceListsToFile(); break;
        case 26: _t->loadDeviceListsFromFile(); break;
        case 27: _t->onTestStepsListDoubleClicked((*reinterpret_cast< std::add_pointer_t<QListWidgetItem*>>(_a[1]))); break;
        case 28: _t->onGraphicsViewDoubleClicked((*reinterpret_cast< std::add_pointer_t<QPointF>>(_a[1]))); break;
        case 29: _t->editTestStepByIndex((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 30: _t->onFlowchartContextMenuRequested((*reinterpret_cast< std::add_pointer_t<QPoint>>(_a[1]))); break;
        case 31: _t->onFlowchartAddMapping(); break;
        case 32: _t->onFlowchartDeleteMapping(); break;
        case 33: _t->onInsertMappingAtPosition((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 34: _t->showTestReport(); break;
        case 35: { QString _r = _t->generateTestReport();
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        default: ;
        }
    }
}

const QMetaObject *MappingWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MappingWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ZN13MappingWindowE.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int MappingWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 36)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 36;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 36)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 36;
    }
    return _id;
}
QT_WARNING_POP
