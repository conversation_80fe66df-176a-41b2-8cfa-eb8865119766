#include <iostream>
#include <iomanip>
#include <vector>
#include <string>

// 简化的CRC16计算函数 (复制自crcutils.cpp)
unsigned short calculateCRC16(const std::vector<unsigned char> &data)
{
    unsigned short crc = 0xFFFF;
    for (size_t i = 0; i < data.size(); ++i) {
        crc ^= static_cast<unsigned char>(data[i]);
        for (int j = 0; j < 8; ++j) {
            if (crc & 0x0001) {
                crc = (crc >> 1) ^ 0xA001;
            } else {
                crc = crc >> 1;
            }
        }
    }
    return crc;
}

// 将十六进制字符串转换为字节数组
std::vector<unsigned char> hexStringToBytes(const std::string& hex) {
    std::vector<unsigned char> bytes;
    for (size_t i = 0; i < hex.length(); i += 2) {
        std::string byteString = hex.substr(i, 2);
        unsigned char byte = static_cast<unsigned char>(strtol(byteString.c_str(), nullptr, 16));
        bytes.push_back(byte);
    }
    return bytes;
}

int main() {
    std::cout << "=== Modbus CRC16 校验测试 ===" << std::endl;
    
    // 标准Modbus CRC16测试用例
    struct TestCase {
        std::string description;
        std::string hexData;
        unsigned short expectedCrc;
    };
    
    std::vector<TestCase> testCases = {
        {"读保持寄存器", "010300000001", 0x84C0},
        {"写单个寄存器", "010600010003", 0x9A9B},
        {"读输入寄存器", "010400000001", 0x31C6},
        {"写多个寄存器", "01100001000204000A0102", 0xC6F0},
        {"简单测试", "0102", 0x4140}
    };
    
    bool allTestsPassed = true;
    
    for (const auto& testCase : testCases) {
        std::cout << "\n--- 测试: " << testCase.description << " ---" << std::endl;
        std::cout << "输入数据: " << testCase.hexData << std::endl;
        
        // 转换为字节数组
        std::vector<unsigned char> data = hexStringToBytes(testCase.hexData);
        
        // 计算CRC16
        unsigned short calculatedCrc = calculateCRC16(data);
        
        std::cout << "期望CRC16: " << std::hex << std::uppercase << testCase.expectedCrc << std::endl;
        std::cout << "计算CRC16: " << std::hex << std::uppercase << calculatedCrc << std::endl;
        
        // 显示字节序 (Modbus使用低字节在前)
        unsigned char lowByte = calculatedCrc & 0xFF;
        unsigned char highByte = (calculatedCrc >> 8) & 0xFF;
        std::cout << "CRC字节序 (低字节在前): " 
                  << std::hex << std::setfill('0') << std::setw(2) << (int)lowByte << " "
                  << std::hex << std::setfill('0') << std::setw(2) << (int)highByte << std::endl;
        
        bool passed = (calculatedCrc == testCase.expectedCrc);
        std::cout << "测试结果: " << (passed ? "✓ 通过" : "✗ 失败") << std::endl;
        
        if (!passed) {
            allTestsPassed = false;
        }
    }
    
    std::cout << "\n=== 测试总结 ===" << std::endl;
    if (allTestsPassed) {
        std::cout << "✓ 所有测试通过! CRC16实现符合Modbus标准" << std::endl;
    } else {
        std::cout << "✗ 部分测试失败! CRC16实现可能不符合Modbus标准" << std::endl;
    }
    
    return 0;
}
