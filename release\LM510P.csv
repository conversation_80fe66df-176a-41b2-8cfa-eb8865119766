﻿名称,描述,数据类型,读取方向,采集周期,存储器类型序号,存储器地址,解析方式,位偏移量,BCD解析,地址,处理方式,最小值,最大值,单位,数据处理参数,特性选择,投退选择,保护投退,期望数据,期望数据(定值文件读取),显示数据,比对
一次CT额定电流1,InP1,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40001,原始数据,1,900,A,1,0,0,---,,,,
一次CT额定电流2,InP2,UINT16,读写,1000,3,0,2字节 无符号 先高后低,0,0,40002,原始数据,1,900,A,1,0,0,---,,,,
电动机额定电流1,Im1,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40003,DM1(智能除法),0.2,1080,A,1,0,0,---,,,,
电动机额定电流2,Im2,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40004,DM2(智能除法),0.2,1080,A,1,0,0,---,,,,
热过载保护投退,热过载,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40005,原始数据,0,3,,1,0,1,---,,,,
热过载保护模型选择,热过载模型,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40005,原始数据,0,1,,1,0,0,---,,,,
热过载6倍Im堵转电流延时,Tp,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40006,原始数据,1,40,S,1,0,0,---,,,,
热过载告警值,θa,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40006,原始数据,0.6,1,,1,0,0,---,,,,
热过载极限不动作系数,kB,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40007,原始数据,0.9,1.15,,100,0,0,---,,,,
热过载自动复位剩余热容量,θre,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40007,原始数据,0.1,0.6,,1,0,0,---,,,,
Auto自动复位、Man手动复位,热过载复位,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40008,原始数据,0,1,Man\Auto,1,0,0,---,,,,
热过载冷却系数,Kco,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40008,原始数据,1,10,,1,0,0,---,,,,
起动过流保护投退,起动过流,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40009,原始数据,0,1,OFF\TRIP,1,0,1,---,,,,
起动过流保护定值,Iso,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40009,原始数据,0.2,8640,,10,0,0,---,,,,
起动过流保护延时时间,tIso,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40010,原始数据,0.5,25.5,S,10,0,0,---,,,,
原值写入,备用,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40010,原始数据,0,0,,0,0,0,---,,,,
过流(堵转)保护投退,过流(堵转),UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40011,原始数据,0,3,,1,0,1,---,,,,
过流(堵转)保护告警值,Ioa,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40011,原始数据,0.1,8640,,10,0,0,---,,,,
过流(堵转)保护跳闸值,Iov,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40012,原始数据,0.1,8640,,10,0,0,---,,,,
过流(堵转)保护延时时间,tIov,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40012,原始数据,0.1,25.5,,10,0,0,---,,,,
tE时间保护投退,tE时间,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40013,原始数据,0,3,,1,0,1,---,,,,
电动机起动时闭锁选择,tE起动闭锁,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40013,原始数据,0,1,OFF\ON,1,0,0,---,,,,
7倍额定电流时允许的堵转时间,tEp,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40014,原始数据,1,15,S,10,0,0,---,,,,
tE时间保护告警值,tEa,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40014,原始数据,0.5,1,,1,0,0,---,,,,
起动超时保护投退,起动超时,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40015,原始数据,0,2,,1,0,1,---,,,,
原值写入,备用,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40015,原始数据,0,0,,0,0,0,---,,,,
允许起动时间,tst,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40016,原始数据,0.1,60,,10,0,0,---,,,,
起动次数限制投退,起动次数限制,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40017,原始数据,0,1,,1,0,1,---,,,,
起动次数计数限定时段,tsNo,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40017,原始数据,0,60,,1,0,0,---,,,,
在tsNo 时段内允许的起动次数,SNo,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40018,原始数据,1,10,,1,0,0,---,,,,
禁止再次起动后的闭锁时间,tBst,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40018,原始数据,0,60,,1,0,0,---,,,,
再起动闭锁的复位方式,闭锁复位方式,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40019,原始数据,0,1,,1,0,0,---,,,,
原值写入,备用,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40019,原始数据,0,0,,0,0,0,---,,,,
单相接地保护投退,单相接地,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40020,原始数据,0,3,,1,0,1,---,,,,
单相接地保护告警值,IEa,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40020,原始数据,0.1,8640,,10,0,0,---,,,,
单相接地保护告警值,IEd,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40021,原始数据,0.1,8640,,10,0,0,---,,,,
单相接地保护延时时间,tIE,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40021,原始数据,0.1,5,,10,0,0,---,,,,
接地闭锁时间,tIEbk,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40022,原始数据,0,25.5,,10,0,0,---,,,,
原值写入,备用,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40022,原始数据,0,0,,0,0,0,---,,,,
漏电保护投退,漏电,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40023,原始数据,0,3,,1,0,1,---,,,,
漏电互感器额定一次值,I△n,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40023,原始数据,0.1,10,,10,0,0,---,,,,
漏电保护告警值,I△a,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40024,原始数据,0.1,100,,10,0,0,---,,,,
漏电保护跳闸值,I△d,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40024,原始数据,0.1,100,,10,0,0,---,,,,
漏电保护延时时间,tI△,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40025,原始数据,0.1,25.5,,10,0,0,---,,,,
接地闭锁时间,tI△bk,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40025,原始数据,0,25.5,,10,0,0,---,,,,
断相保护投退,断相,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40026,原始数据,0,2,,1,0,1,---,,,,
断相保护延时时间,top,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40026,原始数据,0.1,20,,10,0,0,---,,,,
电流不平衡保护,电流不平衡,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40027,原始数据,0,3,,1,0,0,---,,,,
电流不平衡保护告警值,△%a,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40027,原始数据,0.1,0.6,,1,0,0,---,,,,
电流不平衡保护跳闸值,△%d,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40028,原始数据,0.1,0.6,,1,0,0,---,,,,
电流不平衡保护延时时间,t△%,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40028,原始数据,1,120,,1,0,0,---,,,,
欠载保护方式选择,欠载保护方式,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40029,原始数据,0,1,,1,0,0,---,,,,
欠载保护复位方式选择,欠载复位,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40029,原始数据,0,1,,1,0,0,---,,,,
欠载保护自动复位延时时间。,turs,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40030,原始数据,1,30,,1,0,0,---,,,,
欠电流(欠功率)保护投退,欠电流(欠功率),UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40030,原始数据,0,3,,1,0,1,---,,,,
欠电流(欠功率)保护告警值,Iua(Pua),UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40031,原始数据,0.2,900,,1,0,0,---,,,,
欠电流(欠功率)保护跳闸值,Iun(Pun),UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40031,原始数据,0.2,900,,1,0,0,---,,,,
欠电流(欠功率)保护延时时间,tIun,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40032,原始数据,1,1200,,1,0,0,---,,,,
欠电压保护投退,欠电压,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40033,原始数据,0,3,,1,0,1,---,,,,
欠电压保护告警值,Uua,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40033,原始数据,30,300,,1,0,0,---,,,,
欠电压保护跳闸值,Uun,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40034,原始数据,30,300,,1,0,0,---,,,,
欠电压保护延时时间,tUun,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40034,原始数据,0.1,25.5,,10,0,0,---,,,,
过电压保护投退,过电压,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40035,原始数据,0,3,,1,0,1,---,,,,
过电压保护告警值,Uoa,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40035,原始数据,200,500,,1,0,0,---,,,,
过电压保护跳闸值,Uov,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40036,原始数据,200,500,,1,0,0,---,,,,
过电压保护延时时间,tUov,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40036,原始数据,0.1,25.5,,10,0,0,---,,,,
接触器分断能力保护投退,接触器分断能力,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40037,原始数据,0,1,,1,0,1,---,,,,
接触器分断电流,Ibr,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40037,原始数据,0.6,1500,,10,0,0,---,,,,
接触器分断能力保护加速时间,tIbr,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40038,原始数据,0.1,1,,10,0,0,---,,,,
原值写入,备用,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40038,原始数据,0,0,,0,0,0,,,,,
外部故障保护,外部故障,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40039,原始数据,0,2,,1,0,0,---,,,,
外部故障保护延时时间,texf,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40039,原始数据,0.1,25.5,,10,0,0,---,,,,
外部故障复位方式,外部复位,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40040,原始数据,0,1,,1,0,0,---,,,,
原值写入,备用,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40040,原始数据,0,0,,0,0,0,,,,,
反相序保护投退,反相序,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40041,原始数据,0,2,,1,0,1,---,,,,
接触器故障保护投退,接触器故障,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40041,原始数据,0,1,,1,0,1,---,,,,
再起动功能中恢复电压定值,Ure,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40042,原始数据,320,400,,1,0,0,---,,,,
再起动功能中跌落电压定值,Udr,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40042,原始数据,240,400,,1,0,0,---,,,,
立即再起动投退,立即再起动,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40043,原始数据,0,1,,1,0,1,---,,,,
立即再起动最大失电时间,trim,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40043,原始数据,0.1,2,,10,0,0,---,,,,
延时再起动投退,延时再起动,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40044,原始数据,0,1,,1,0,1,---,,,,
延时再起动最大失电时间,trm,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40044,原始数据,2,60,,1,0,0,---,,,,
延时再起动起动延时时间,trst,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40045,原始数据,0.1,60,,10,0,0,---,,,,
延时再起动有效时差,△t,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40046,原始数据,0,2,,10,0,0,---,,,,
原值写入,备用,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40046,原始数据,0,0,,0,0,0,,,,,
模拟量变送选择变送参数,模拟量变送,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40047,原始数据,0,1,,1,0,0,---,,,,
原值写入,备用,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40047,原始数据,0,0,,0,0,0,,,,,
原值写入,备用,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40048,原始数据,0,0,,0,0,0,,,,,
原值写入,备用,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40049,原始数据,0,0,,0,0,0,,,,,
遥控投退,遥控,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40050,原始数据,0,1,,1,0,1,---,,,,
1为接入常闭，0为接入常开,开关量S6～S1,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40050,原始数据,0,63,BIT,1,0,0,---,,,,
断路器控制投退,断路器控制,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40051,原始数据,0,1,,1,0,1,---,,,,
面板编程控制方式,控制方式,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40051,原始数据,0,3,,1,0,0,---,,,,
可编程控制功能块,操作切换时间,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40052,原始数据,0,60,,10,0,0,---,,,,
用于正反转起动功能块F12,操作闭锁时间,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40053,原始数据,0,300,,1,0,0,---,,,,
仪表控制功能块起动记忆初值,起动记忆初值A,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40054,原始数据,0,1,,1,0,0,---,,,,
原值写入,备用,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40054,原始数据,0,0,,0,0,0,,,,,
保护参数设定密码,参数密码,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40055,原始数据,1,4999,,1,0,0,---,,,,
原值写入,备用,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40056,原始数据,0,0,,0,0,0,,,,,
原值写入,备用,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40057,原始数据,0,0,,0,0,0,,,,,
原值写入,备用,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40058,原始数据,0,0,,0,0,0,,,,,
速断投退,速断,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40059,原始数据,0,1,,1,0,1,---,,,,
跳闸倍数,IH,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40059,原始数据,0.8,15000,,10,0,0,---,,,,
延时时间,tIH,UINT16,只读,1000,3,0,2字节 无符号 先高后低,0,0,40060,原始数据,0.04,10,,100,0,0,---,,,,
数字温度保护投退,数字温度,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40061,原始数据,0,2,,1,0,1,---,,,,
温度保护报警值,Td,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40061,原始数据,40,120,℃,1,0,0,---,,,,
数字温度差距保护投退,温差保护,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40062,原始数据,0,2,,1,0,1,---,,,,
三相温度差值,TDd,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40062,原始数据,5,10,℃,1,0,0,---,,,,
温度传感器位置1,位置对应关系1,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40063,原始数据,0,8,,1,0,0,---,,,,
温度传感器位置2,位置对应关系2,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40063,原始数据,0,8,,1,0,0,---,,,,
温度传感器位置3,位置对应关系3,UINT16,只读,1000,3,0,2字节 无符号 取高字节,0,0,40064,原始数据,0,8,,1,0,0,---,,,,
温度传感器位置4,位置对应关系4,UINT16,只读,1000,3,0,2字节 无符号 取低字节,0,0,40064,原始数据,0,8,,1,0,0,---,,,,
