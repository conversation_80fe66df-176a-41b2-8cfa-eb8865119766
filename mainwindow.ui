<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1382</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>JcSoft - 双串口通信</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <layout class="QHBoxLayout" name="horizontalLayoutTop">
      <item>
       <widget class="QGroupBox" name="groupBoxSerialA">
        <property name="title">
         <string>串口A配置</string>
        </property>
        <layout class="QGridLayout" name="gridLayoutA">
         <item row="0" column="0">
          <widget class="QLabel" name="labelPortA">
           <property name="text">
            <string>串口号：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QComboBox" name="comboBoxPortA"/>
         </item>
         <item row="0" column="2">
          <widget class="QLabel" name="labelBaudA">
           <property name="text">
            <string>波特率：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="3">
          <widget class="QComboBox" name="comboBoxBaudA"/>
         </item>
         <item row="0" column="4">
          <widget class="QLabel" name="labelParityA">
           <property name="text">
            <string>校验位：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="5">
          <widget class="QComboBox" name="comboBoxParityA"/>
         </item>
         <item row="0" column="6">
          <widget class="QLabel" name="labelDataA">
           <property name="text">
            <string>数据位：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="7">
          <widget class="QComboBox" name="comboBoxDataA"/>
         </item>
         <item row="0" column="8">
          <widget class="QLabel" name="labelStopA">
           <property name="text">
            <string>停止位：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="9">
          <widget class="QComboBox" name="comboBoxStopA"/>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QGroupBox" name="groupBoxSerialB">
        <property name="title">
         <string>串口B配置</string>
        </property>
        <layout class="QGridLayout" name="gridLayoutB">
         <item row="0" column="0">
          <widget class="QLabel" name="labelPortB">
           <property name="text">
            <string>串口号：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QComboBox" name="comboBoxPortB"/>
         </item>
         <item row="0" column="2">
          <widget class="QLabel" name="labelBaudB">
           <property name="text">
            <string>波特率：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="3">
          <widget class="QComboBox" name="comboBoxBaudB"/>
         </item>
         <item row="0" column="4">
          <widget class="QLabel" name="labelParityB">
           <property name="text">
            <string>校验位：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="5">
          <widget class="QComboBox" name="comboBoxParityB"/>
         </item>
         <item row="0" column="6">
          <widget class="QLabel" name="labelDataB">
           <property name="text">
            <string>数据位：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="7">
          <widget class="QComboBox" name="comboBoxDataB"/>
         </item>
         <item row="0" column="8">
          <widget class="QLabel" name="labelStopB">
           <property name="text">
            <string>停止位：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="9">
          <widget class="QComboBox" name="comboBoxStopB"/>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <widget class="QGroupBox" name="testListGroupBox">
      <property name="title">
       <string>UART测试列表</string>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <item>
        <widget class="QTableWidget" name="tableWidgetTests">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="alternatingRowColors">
          <bool>true</bool>
         </property>
         <property name="selectionMode">
          <enum>QAbstractItemView::SelectionMode::SingleSelection</enum>
         </property>
         <property name="selectionBehavior">
          <enum>QAbstractItemView::SelectionBehavior::SelectRows</enum>
         </property>
         <attribute name="horizontalHeaderStretchLastSection">
          <bool>true</bool>
         </attribute>
         <column>
          <property name="text">
           <string>序号</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>测试描述</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>串口A发送</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>串口A期望接收</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>串口A接收</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>串口B发送</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>串口B期望接收</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>串口B接收</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>测试结果</string>
          </property>
         </column>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayoutButtons">
         <item>
          <widget class="QPushButton" name="pushButtonAddTest">
           <property name="text">
            <string>添加测试用例</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonRemoveTest">
           <property name="text">
            <string>移除当前用例</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonCopyTest">
           <property name="text">
            <string>复制所选用例</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonClearTests">
           <property name="text">
            <string>清空用例</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonImport">
           <property name="text">
            <string>导入配置</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonExport">
           <property name="text">
            <string>导出配置</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonGenerateReport">
           <property name="text">
            <string>生成报告</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="labelDelay">
           <property name="text">
            <string>测试延时：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QSpinBox" name="spinBoxDelay">
           <property name="suffix">
            <string> ms</string>
           </property>
           <property name="minimum">
            <number>100</number>
           </property>
           <property name="maximum">
            <number>5000</number>
           </property>
           <property name="value">
            <number>1000</number>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBoxStopOnFailure">
           <property name="text">
            <string>失败时停止</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBoxUseCRC16">
           <property name="text">
            <string>CRC16校验</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonTest">
           <property name="text">
            <string>开始测试</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QGroupBox" name="modbusTestListGroupBox">
      <property name="title">
       <string>Modbus测试列表</string>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_4">
       <item>
        <widget class="QTableWidget" name="tableWidgetModbusTests">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="alternatingRowColors">
          <bool>true</bool>
         </property>
         <property name="selectionMode">
          <enum>QAbstractItemView::SelectionMode::SingleSelection</enum>
         </property>
         <property name="selectionBehavior">
          <enum>QAbstractItemView::SelectionBehavior::SelectRows</enum>
         </property>
         <attribute name="horizontalHeaderStretchLastSection">
          <bool>true</bool>
         </attribute>
         <column>
          <property name="text">
           <string>序号</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>测试描述</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>站号</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>功能码</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>发送数据</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>期望响应</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>实际响应</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>测试结果</string>
          </property>
         </column>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayoutModbusButtons">
         <item>
          <widget class="QPushButton" name="pushButtonAddModbusTest">
           <property name="text">
            <string>添加测试用例</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonRemoveModbusTest">
           <property name="text">
            <string>移除当前用例</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonCopyModbusTest">
           <property name="text">
            <string>复制所选用例</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonClearModbusTests">
           <property name="text">
            <string>清空用例</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonImportModbus">
           <property name="text">
            <string>导入配置</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonExportModbus">
           <property name="text">
            <string>导出配置</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonGenerateModbusReport">
           <property name="text">
            <string>生成报告</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacerModbus">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="labelModbusTimeout">
           <property name="text">
            <string>测试延时：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QSpinBox" name="spinBoxModbusTimeout">
           <property name="suffix">
            <string> ms</string>
           </property>
           <property name="minimum">
            <number>100</number>
           </property>
           <property name="maximum">
            <number>5000</number>
           </property>
           <property name="value">
            <number>1000</number>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonModbusTest">
           <property name="text">
            <string>开始测试</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayoutBottom">
      <item>
       <layout class="QVBoxLayout" name="verticalLayoutA">
        <item>
         <widget class="QGroupBox" name="groupBoxSendA">
          <property name="title">
           <string>串口A发送数据</string>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayoutSendA">
           <item>
            <widget class="QLineEdit" name="lineEditSendDataA">
             <property name="placeholderText">
              <string>请输入十六进制数据，例如：01 03 00 00 00 01</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pushButtonSendA">
             <property name="text">
              <string>发送</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBoxDisplayA">
          <property name="title">
           <string>串口A数据显示</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayoutDisplayA">
           <item>
            <widget class="QTextBrowser" name="textBrowserA"/>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayoutB">
        <item>
         <widget class="QGroupBox" name="groupBoxSendB">
          <property name="title">
           <string>串口B发送数据</string>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayoutSendB">
           <item>
            <widget class="QLineEdit" name="lineEditSendDataB">
             <property name="placeholderText">
              <string>请输入十六进制数据，例如：01 03 00 00 00 01</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pushButtonSendB">
             <property name="text">
              <string>发送</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBoxDisplayB">
          <property name="title">
           <string>串口B数据显示</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayoutDisplayB">
           <item>
            <widget class="QTextBrowser" name="textBrowserB"/>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QToolBar" name="mainToolBar">
   <property name="windowTitle">
    <string>工具栏</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="actionConnectA"/>
   <addaction name="actionConnectB"/>
   <addaction name="actionDisconnectA"/>
   <addaction name="actionDisconnectB"/>
   <addaction name="actionRefresh"/>
   <addaction name="separator"/>
   <addaction name="actionMapping"/>
   <addaction name="actionSetValue"/>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1382</width>
     <height>20</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>文件</string>
    </property>
    <addaction name="actionImportConfig"/>
    <addaction name="actionExportConfig"/>
    <addaction name="separator"/>
    <addaction name="actionExit"/>
   </widget>
   <widget class="QMenu" name="menuTest">
    <property name="title">
     <string>检测</string>
    </property>
    <addaction name="actionStartTest"/>
    <addaction name="actionStopTest"/>
    <addaction name="separator"/>
   </widget>
   <widget class="QMenu" name="menuConfig">
    <property name="title">
     <string>配置</string>
    </property>
    <addaction name="actionMapping"/>
    <addaction name="separator"/>
    <addaction name="actionSetValue"/>
   </widget>
   <widget class="QMenu" name="menuReport">
    <property name="title">
     <string>报表</string>
    </property>
    <addaction name="actionGenerateReport"/>
    <addaction name="actionViewReport"/>
   </widget>
   <widget class="QMenu" name="menuTools">
    <property name="title">
     <string>工具</string>
    </property>
    <addaction name="actionRefresh"/>
    <addaction name="separator"/>
    <addaction name="actionConnectA"/>
    <addaction name="actionConnectB"/>
    <addaction name="actionDisconnectA"/>
    <addaction name="actionDisconnectB"/>
   </widget>
   <addaction name="menuFile"/>
   <addaction name="menuConfig"/>
   <addaction name="menuTest"/>
   <addaction name="menuReport"/>
   <addaction name="menuTools"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <action name="actionExit">
   <property name="text">
    <string>退出</string>
   </property>
  </action>
  <action name="actionStartTest">
   <property name="text">
    <string>开始测试</string>
   </property>
  </action>
  <action name="actionStopTest">
   <property name="text">
    <string>停止测试</string>
   </property>
  </action>

  <action name="actionMapping">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/test.svg</normaloff>
     <normalon>:/icons/test.svg</normalon>:/icons/test.svg</iconset>
   </property>
   <property name="text">
    <string>TEST配置</string>
   </property>
   <property name="toolTip">
    <string>TEST配置</string>
   </property>
  </action>
  <action name="actionConnectA">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/connect.svg</normaloff>
     <normalon>:/icons/connect.svg</normalon>:/icons/connect.svg</iconset>
   </property>
   <property name="text">
    <string>连接串口A</string>
   </property>
   <property name="toolTip">
    <string>连接串口A</string>
   </property>
  </action>
  <action name="actionConnectB">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/connect.svg</normaloff>
     <normalon>:/icons/connect.svg</normalon>:/icons/connect.svg</iconset>
   </property>
   <property name="text">
    <string>连接串口B</string>
   </property>
   <property name="toolTip">
    <string>连接串口B</string>
   </property>
  </action>
  <action name="actionDisconnectA">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/disconnect.svg</normaloff>
     <normalon>:/icons/disconnect.svg</normalon>:/icons/disconnect.svg</iconset>
   </property>
   <property name="text">
    <string>断开串口A</string>
   </property>
   <property name="toolTip">
    <string>断开串口A</string>
   </property>
  </action>
  <action name="actionDisconnectB">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/disconnect.svg</normaloff>
     <normalon>:/icons/disconnect.svg</normalon>:/icons/disconnect.svg</iconset>
   </property>
   <property name="text">
    <string>断开串口B</string>
   </property>
   <property name="toolTip">
    <string>断开串口B</string>
   </property>
  </action>
  <action name="actionRefresh">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/refresh.svg</normaloff>
     <normalon>:/icons/refresh.svg</normalon>:/icons/refresh.svg</iconset>
   </property>
   <property name="text">
    <string>刷新串口</string>
   </property>
   <property name="toolTip">
    <string>刷新串口</string>
   </property>
  </action>
  <action name="actionGenerateReport">
   <property name="text">
    <string>生成报表</string>
   </property>
  </action>
  <action name="actionViewReport">
   <property name="text">
    <string>查看报表</string>
   </property>
  </action>
  <action name="actionImportConfig">
   <property name="text">
    <string>导入配置</string>
   </property>
  </action>
  <action name="actionExportConfig">
   <property name="text">
    <string>导出配置</string>
   </property>
  </action>
  <action name="actionSetValue">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/setvalue.svg</normaloff>
     <normalon>:/icons/setvalue.svg</normalon>:/icons/setvalue.svg</iconset>
   </property>
   <property name="text">
    <string>定值召唤</string>
   </property>
   <property name="toolTip">
    <string>定值召唤</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui>
