/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 6.8.2
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#ifdef _MSC_VER
// disable informational message "function ... selected for automatic inline expansion"
#pragma warning (disable: 4711)
#endif

static const unsigned char qt_resource_data[] = {
  // setvalue.svg
  0x0,0x0,0x1,0x81,
  0x3c,
  0x73,0x76,0x67,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,
  0x2f,0x2f,0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,
  0x30,0x2f,0x73,0x76,0x67,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,0x22,0x20,0x76,0x69,
  0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,0x34,0x20,0x32,0x34,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,0x22,0x20,0x73,0x74,
  0x72,0x6f,0x6b,0x65,0x3d,0x22,0x63,0x75,0x72,0x72,0x65,0x6e,0x74,0x43,0x6f,0x6c,
  0x6f,0x72,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,
  0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x20,0x73,0x74,0x72,0x6f,
  0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x6a,0x6f,0x69,0x6e,0x3d,0x22,0x72,0x6f,0x75,
  0x6e,0x64,0x22,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,0x22,
  0x33,0x22,0x20,0x79,0x3d,0x22,0x33,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,
  0x31,0x38,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x38,0x22,0x20,
  0x72,0x78,0x3d,0x22,0x32,0x22,0x20,0x72,0x79,0x3d,0x22,0x32,0x22,0x3e,0x3c,0x2f,
  0x72,0x65,0x63,0x74,0x3e,0xa,0x20,0x20,0x3c,0x6c,0x69,0x6e,0x65,0x20,0x78,0x31,
  0x3d,0x22,0x33,0x22,0x20,0x79,0x31,0x3d,0x22,0x39,0x22,0x20,0x78,0x32,0x3d,0x22,
  0x32,0x31,0x22,0x20,0x79,0x32,0x3d,0x22,0x39,0x22,0x3e,0x3c,0x2f,0x6c,0x69,0x6e,
  0x65,0x3e,0xa,0x20,0x20,0x3c,0x6c,0x69,0x6e,0x65,0x20,0x78,0x31,0x3d,0x22,0x39,
  0x22,0x20,0x79,0x31,0x3d,0x22,0x32,0x31,0x22,0x20,0x78,0x32,0x3d,0x22,0x39,0x22,
  0x20,0x79,0x32,0x3d,0x22,0x39,0x22,0x3e,0x3c,0x2f,0x6c,0x69,0x6e,0x65,0x3e,0xa,
  0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x35,
  0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x35,0x22,0x20,0x72,0x3d,0x22,0x33,0x22,0x3e,
  0x3c,0x2f,0x63,0x69,0x72,0x63,0x6c,0x65,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,
  
    // test.svg
  0x0,0x0,0x1,0x93,
  0x0,
  0x0,0x5,0xc5,0x78,0xda,0xbd,0x94,0xc1,0x72,0x82,0x30,0x10,0x86,0xef,0x3e,0xc5,
  0x4e,0x7a,0x16,0x92,0x10,0xaa,0x74,0x8c,0xce,0x94,0x99,0x8e,0x7,0x7b,0x6b,0x3d,
  0xf4,0xe6,0x8,0x15,0x5a,0x1a,0x1c,0xa0,0xe2,0xe3,0x77,0x97,0x92,0x80,0xf,0x80,
  0x10,0xcd,0xe6,0x4f,0xd8,0x7f,0x3f,0x76,0x86,0xd5,0xe6,0xfa,0x53,0xc0,0x25,0xad,
  0xea,0xbc,0x34,0x9a,0x9,0x8f,0x33,0x48,0xcd,0xb1,0x4c,0x72,0x73,0xd2,0xec,0xfd,
  0xed,0x65,0xbe,0x64,0x9b,0xf5,0x6c,0x55,0x5f,0x4e,0xd0,0xe6,0x49,0x93,0x69,0x26,
  0x15,0x83,0x2c,0xcd,0x4f,0x59,0xf3,0x1f,0x5f,0xf2,0xb4,0x7d,0x2e,0xaf,0x9a,0x71,
  0xe0,0x20,0x15,0x90,0xf6,0x99,0x17,0x85,0x66,0xa6,0x34,0x29,0x3,0x34,0x30,0xb5,
  0x66,0x59,0xd3,0x9c,0x9f,0x7c,0xbf,0x6d,0x5b,0xaf,0xd,0xbc,0xb2,0x3a,0xf9,0x92,
  0x73,0xee,0x63,0x62,0xb6,0x9e,0x1,0xac,0xce,0x87,0x26,0x83,0x44,0xb3,0xd7,0x8,
  0x82,0xad,0x8a,0x3,0x4f,0xa9,0xc5,0x42,0x42,0x40,0xb7,0x8b,0xd5,0x3e,0x8a,0x3,
  0x88,0xbc,0x30,0x94,0x72,0xe9,0x74,0xc1,0x41,0xe1,0xdf,0x36,0x8a,0xed,0xe,0x2a,
  0x38,0x46,0xab,0x68,0xaf,0x62,0x9c,0xec,0x13,0x2e,0x3,0xa0,0xdb,0x7,0x83,0xba,
  0xa9,0xca,0xef,0x54,0xb3,0x7,0xde,0x5d,0x56,0x98,0x5b,0x64,0x27,0x14,0xb9,0x49,
  0x8f,0x87,0xb3,0x66,0x55,0xf9,0x6b,0x92,0x1b,0xf9,0xab,0xcc,0x8d,0xd5,0xfd,0x5b,
  0x24,0x89,0xce,0x5b,0x11,0xc6,0x42,0x75,0xfe,0x68,0x2b,0xd4,0x50,0xbd,0xea,0xb0,
  0x70,0x72,0xf5,0xf6,0xc7,0x88,0x22,0x24,0x30,0xc9,0x63,0xc9,0x69,0x33,0x20,0x4d,
  0xa,0x77,0x92,0x42,0x24,0xc3,0xc9,0x66,0xb3,0xe7,0x2,0x20,0xd3,0xe9,0xd1,0x22,
  0xac,0x76,0xd4,0x2e,0xe2,0x82,0x11,0x66,0xb8,0xc7,0xda,0x83,0xa1,0x2a,0x5b,0xa5,
  0xc0,0x8e,0x49,0x31,0xea,0x18,0x2a,0x84,0x36,0xa2,0xe4,0x7b,0x7a,0x63,0xdc,0x65,
  0x1b,0xde,0xe,0x90,0xe9,0x5d,0xba,0x86,0x6c,0xa3,0xb6,0xa1,0x73,0x37,0x86,0x55,
  0x87,0x87,0x81,0xab,0xbb,0xdf,0x23,0x9a,0x90,0x0,0x47,0x9d,0x43,0x8d,0xc6,0x68,
  0xd5,0x11,0xd2,0x51,0xfb,0x94,0xcb,0x2,0x9d,0xf7,0xf4,0x88,0x8f,0x5e,0x8,0xf8,
  0xdb,0xf5,0xb3,0x98,0xdc,0x50,0x2c,0x7a,0x47,0x1b,0x88,0xbb,0x30,0x92,0xdb,0xce,
  0x6,0x77,0xa2,0xec,0x3c,0x5d,0x34,0x8d,0xe9,0x8a,0xbe,0x9f,0xeb,0x3f,0x75,0xc7,
  0x92,0xba,
    // refresh.svg
  0x0,0x0,0x1,0x70,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x34,0x20,0x34,0x56,0x39,0x48,0x39,0x4d,0x32,0x30,0x20,0x32,0x30,0x56,
  0x31,0x35,0x48,0x31,0x35,0x4d,0x32,0x30,0x20,0x39,0x43,0x32,0x30,0x20,0x35,0x2e,
  0x31,0x33,0x34,0x30,0x31,0x20,0x31,0x36,0x2e,0x38,0x36,0x36,0x20,0x32,0x20,0x31,
  0x33,0x20,0x32,0x43,0x39,0x2e,0x31,0x33,0x34,0x30,0x31,0x20,0x32,0x20,0x36,0x20,
  0x35,0x2e,0x31,0x33,0x34,0x30,0x31,0x20,0x36,0x20,0x39,0x4d,0x31,0x38,0x20,0x31,
  0x35,0x43,0x31,0x38,0x20,0x31,0x38,0x2e,0x38,0x36,0x36,0x20,0x31,0x34,0x2e,0x38,
  0x36,0x36,0x20,0x32,0x32,0x20,0x31,0x31,0x20,0x32,0x32,0x43,0x37,0x2e,0x31,0x33,
  0x34,0x30,0x31,0x20,0x32,0x32,0x20,0x34,0x20,0x31,0x38,0x2e,0x38,0x36,0x36,0x20,
  0x34,0x20,0x31,0x35,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x30,
  0x30,0x30,0x30,0x30,0x30,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,
  0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,
  0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x20,0x73,
  0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x6a,0x6f,0x69,0x6e,0x3d,0x22,
  0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,
    // settings.svg
  0x0,0x0,0x8,0x97,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x32,0x20,0x31,0x35,0x43,0x31,0x33,0x2e,0x36,0x35,0x36,0x39,0x20,
  0x31,0x35,0x20,0x31,0x35,0x20,0x31,0x33,0x2e,0x36,0x35,0x36,0x39,0x20,0x31,0x35,
  0x20,0x31,0x32,0x43,0x31,0x35,0x20,0x31,0x30,0x2e,0x33,0x34,0x33,0x31,0x20,0x31,
  0x33,0x2e,0x36,0x35,0x36,0x39,0x20,0x39,0x20,0x31,0x32,0x20,0x39,0x43,0x31,0x30,
  0x2e,0x33,0x34,0x33,0x31,0x20,0x39,0x20,0x39,0x20,0x31,0x30,0x2e,0x33,0x34,0x33,
  0x31,0x20,0x39,0x20,0x31,0x32,0x43,0x39,0x20,0x31,0x33,0x2e,0x36,0x35,0x36,0x39,
  0x20,0x31,0x30,0x2e,0x33,0x34,0x33,0x31,0x20,0x31,0x35,0x20,0x31,0x32,0x20,0x31,
  0x35,0x5a,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x30,0x30,0x30,
  0x30,0x30,0x30,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,
  0x68,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,
  0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x20,0x73,0x74,0x72,
  0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x6a,0x6f,0x69,0x6e,0x3d,0x22,0x72,0x6f,
  0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,
  0x3d,0x22,0x4d,0x31,0x39,0x2e,0x34,0x20,0x31,0x35,0x43,0x31,0x39,0x2e,0x31,0x32,
  0x37,0x37,0x20,0x31,0x35,0x2e,0x36,0x31,0x37,0x31,0x20,0x31,0x39,0x2e,0x32,0x35,
  0x38,0x33,0x20,0x31,0x36,0x2e,0x33,0x33,0x37,0x38,0x20,0x31,0x39,0x2e,0x37,0x33,
  0x20,0x31,0x36,0x2e,0x38,0x32,0x4c,0x31,0x39,0x2e,0x37,0x39,0x20,0x31,0x36,0x2e,
  0x38,0x38,0x43,0x32,0x30,0x2e,0x31,0x36,0x35,0x36,0x20,0x31,0x37,0x2e,0x32,0x35,
  0x35,0x31,0x20,0x32,0x30,0x2e,0x33,0x37,0x36,0x36,0x20,0x31,0x37,0x2e,0x37,0x36,
  0x34,0x32,0x20,0x32,0x30,0x2e,0x33,0x37,0x36,0x36,0x20,0x31,0x38,0x2e,0x32,0x39,
  0x35,0x43,0x32,0x30,0x2e,0x33,0x37,0x36,0x36,0x20,0x31,0x38,0x2e,0x38,0x32,0x35,
  0x38,0x20,0x32,0x30,0x2e,0x31,0x36,0x35,0x36,0x20,0x31,0x39,0x2e,0x33,0x33,0x34,
  0x39,0x20,0x31,0x39,0x2e,0x37,0x39,0x20,0x31,0x39,0x2e,0x37,0x31,0x43,0x31,0x39,
  0x2e,0x34,0x31,0x34,0x39,0x20,0x32,0x30,0x2e,0x30,0x38,0x35,0x36,0x20,0x31,0x38,
  0x2e,0x39,0x30,0x35,0x38,0x20,0x32,0x30,0x2e,0x32,0x39,0x36,0x36,0x20,0x31,0x38,
  0x2e,0x33,0x37,0x35,0x20,0x32,0x30,0x2e,0x32,0x39,0x36,0x36,0x43,0x31,0x37,0x2e,
  0x38,0x34,0x34,0x32,0x20,0x32,0x30,0x2e,0x32,0x39,0x36,0x36,0x20,0x31,0x37,0x2e,
  0x33,0x33,0x35,0x31,0x20,0x32,0x30,0x2e,0x30,0x38,0x35,0x36,0x20,0x31,0x36,0x2e,
  0x39,0x36,0x20,0x31,0x39,0x2e,0x37,0x31,0x4c,0x31,0x36,0x2e,0x39,0x20,0x31,0x39,
  0x2e,0x36,0x35,0x43,0x31,0x36,0x2e,0x34,0x31,0x37,0x38,0x20,0x31,0x39,0x2e,0x31,
  0x37,0x38,0x33,0x20,0x31,0x35,0x2e,0x36,0x39,0x37,0x31,0x20,0x31,0x39,0x2e,0x30,
  0x34,0x37,0x37,0x20,0x31,0x35,0x2e,0x30,0x38,0x20,0x31,0x39,0x2e,0x33,0x32,0x43,
  0x31,0x34,0x2e,0x34,0x37,0x35,0x35,0x20,0x31,0x39,0x2e,0x35,0x37,0x39,0x31,0x20,
  0x31,0x34,0x2e,0x30,0x38,0x32,0x36,0x20,0x32,0x30,0x2e,0x31,0x37,0x32,0x34,0x20,
  0x31,0x34,0x2e,0x30,0x38,0x20,0x32,0x30,0x2e,0x38,0x33,0x56,0x32,0x31,0x43,0x31,
  0x34,0x2e,0x30,0x38,0x20,0x32,0x32,0x2e,0x31,0x30,0x34,0x36,0x20,0x31,0x33,0x2e,
  0x31,0x38,0x34,0x36,0x20,0x32,0x33,0x20,0x31,0x32,0x2e,0x30,0x38,0x20,0x32,0x33,
  0x43,0x31,0x30,0x2e,0x39,0x37,0x35,0x34,0x20,0x32,0x33,0x20,0x31,0x30,0x2e,0x30,
  0x38,0x20,0x32,0x32,0x2e,0x31,0x30,0x34,0x36,0x20,0x31,0x30,0x2e,0x30,0x38,0x20,
  0x32,0x31,0x56,0x32,0x30,0x2e,0x39,0x31,0x43,0x31,0x30,0x2e,0x30,0x36,0x34,0x32,
  0x20,0x32,0x30,0x2e,0x32,0x33,0x32,0x37,0x20,0x39,0x2e,0x36,0x33,0x35,0x38,0x37,
  0x20,0x31,0x39,0x2e,0x36,0x33,0x33,0x39,0x20,0x39,0x20,0x31,0x39,0x2e,0x34,0x43,
  0x38,0x2e,0x33,0x38,0x32,0x39,0x31,0x20,0x31,0x39,0x2e,0x31,0x32,0x37,0x37,0x20,
  0x37,0x2e,0x36,0x36,0x32,0x31,0x39,0x20,0x31,0x39,0x2e,0x32,0x35,0x38,0x33,0x20,
  0x37,0x2e,0x31,0x38,0x20,0x31,0x39,0x2e,0x37,0x33,0x4c,0x37,0x2e,0x31,0x32,0x20,
  0x31,0x39,0x2e,0x37,0x39,0x43,0x36,0x2e,0x37,0x34,0x34,0x38,0x36,0x20,0x32,0x30,
  0x2e,0x31,0x36,0x35,0x36,0x20,0x36,0x2e,0x32,0x33,0x35,0x38,0x32,0x20,0x32,0x30,
  0x2e,0x33,0x37,0x36,0x36,0x20,0x35,0x2e,0x37,0x30,0x35,0x20,0x32,0x30,0x2e,0x33,
  0x37,0x36,0x36,0x43,0x35,0x2e,0x31,0x37,0x34,0x31,0x38,0x20,0x32,0x30,0x2e,0x33,
  0x37,0x36,0x36,0x20,0x34,0x2e,0x36,0x36,0x35,0x31,0x34,0x20,0x32,0x30,0x2e,0x31,
  0x36,0x35,0x36,0x20,0x34,0x2e,0x32,0x39,0x20,0x31,0x39,0x2e,0x37,0x39,0x43,0x33,
  0x2e,0x39,0x31,0x34,0x34,0x35,0x20,0x31,0x39,0x2e,0x34,0x31,0x34,0x39,0x20,0x33,
  0x2e,0x37,0x30,0x33,0x34,0x33,0x20,0x31,0x38,0x2e,0x39,0x30,0x35,0x38,0x20,0x33,
  0x2e,0x37,0x30,0x33,0x34,0x33,0x20,0x31,0x38,0x2e,0x33,0x37,0x35,0x43,0x33,0x2e,
  0x37,0x30,0x33,0x34,0x33,0x20,0x31,0x37,0x2e,0x38,0x34,0x34,0x32,0x20,0x33,0x2e,
  0x39,0x31,0x34,0x34,0x35,0x20,0x31,0x37,0x2e,0x33,0x33,0x35,0x31,0x20,0x34,0x2e,
  0x32,0x39,0x20,0x31,0x36,0x2e,0x39,0x36,0x4c,0x34,0x2e,0x33,0x35,0x20,0x31,0x36,
  0x2e,0x39,0x43,0x34,0x2e,0x38,0x32,0x31,0x36,0x37,0x20,0x31,0x36,0x2e,0x34,0x31,
  0x37,0x38,0x20,0x34,0x2e,0x39,0x35,0x32,0x33,0x35,0x20,0x31,0x35,0x2e,0x36,0x39,
  0x37,0x31,0x20,0x34,0x2e,0x36,0x38,0x20,0x31,0x35,0x2e,0x30,0x38,0x43,0x34,0x2e,
  0x34,0x32,0x30,0x39,0x33,0x20,0x31,0x34,0x2e,0x34,0x37,0x35,0x35,0x20,0x33,0x2e,
  0x38,0x32,0x37,0x36,0x34,0x20,0x31,0x34,0x2e,0x30,0x38,0x32,0x36,0x20,0x33,0x2e,
  0x31,0x37,0x20,0x31,0x34,0x2e,0x30,0x38,0x48,0x33,0x43,0x31,0x2e,0x38,0x39,0x35,
  0x34,0x33,0x20,0x31,0x34,0x2e,0x30,0x38,0x20,0x31,0x20,0x31,0x33,0x2e,0x31,0x38,
  0x34,0x36,0x20,0x31,0x20,0x31,0x32,0x2e,0x30,0x38,0x43,0x31,0x20,0x31,0x30,0x2e,
  0x39,0x37,0x35,0x34,0x20,0x31,0x2e,0x38,0x39,0x35,0x34,0x33,0x20,0x31,0x30,0x2e,
  0x30,0x38,0x20,0x33,0x20,0x31,0x30,0x2e,0x30,0x38,0x48,0x33,0x2e,0x30,0x39,0x43,
  0x33,0x2e,0x37,0x36,0x37,0x33,0x33,0x20,0x31,0x30,0x2e,0x30,0x36,0x34,0x32,0x20,
  0x34,0x2e,0x33,0x36,0x36,0x31,0x33,0x20,0x39,0x2e,0x36,0x33,0x35,0x38,0x37,0x20,
  0x34,0x2e,0x36,0x20,0x39,0x43,0x34,0x2e,0x38,0x37,0x32,0x33,0x35,0x20,0x38,0x2e,
  0x33,0x38,0x32,0x39,0x31,0x20,0x34,0x2e,0x37,0x34,0x31,0x36,0x37,0x20,0x37,0x2e,
  0x36,0x36,0x32,0x31,0x39,0x20,0x34,0x2e,0x32,0x37,0x20,0x37,0x2e,0x31,0x38,0x4c,
  0x34,0x2e,0x32,0x31,0x20,0x37,0x2e,0x31,0x32,0x43,0x33,0x2e,0x38,0x33,0x34,0x34,
  0x35,0x20,0x36,0x2e,0x37,0x34,0x34,0x38,0x36,0x20,0x33,0x2e,0x36,0x32,0x33,0x34,
  0x33,0x20,0x36,0x2e,0x32,0x33,0x35,0x38,0x32,0x20,0x33,0x2e,0x36,0x32,0x33,0x34,
  0x33,0x20,0x35,0x2e,0x37,0x30,0x35,0x43,0x33,0x2e,0x36,0x32,0x33,0x34,0x33,0x20,
  0x35,0x2e,0x31,0x37,0x34,0x31,0x38,0x20,0x33,0x2e,0x38,0x33,0x34,0x34,0x35,0x20,
  0x34,0x2e,0x36,0x36,0x35,0x31,0x34,0x20,0x34,0x2e,0x32,0x31,0x20,0x34,0x2e,0x32,
  0x39,0x43,0x34,0x2e,0x35,0x38,0x35,0x31,0x34,0x20,0x33,0x2e,0x39,0x31,0x34,0x34,
  0x35,0x20,0x35,0x2e,0x30,0x39,0x34,0x31,0x38,0x20,0x33,0x2e,0x37,0x30,0x33,0x34,
  0x33,0x20,0x35,0x2e,0x36,0x32,0x35,0x20,0x33,0x2e,0x37,0x30,0x33,0x34,0x33,0x43,
  0x36,0x2e,0x31,0x35,0x35,0x38,0x32,0x20,0x33,0x2e,0x37,0x30,0x33,0x34,0x33,0x20,
  0x36,0x2e,0x36,0x36,0x34,0x38,0x36,0x20,0x33,0x2e,0x39,0x31,0x34,0x34,0x35,0x20,
  0x37,0x2e,0x30,0x34,0x20,0x34,0x2e,0x32,0x39,0x4c,0x37,0x2e,0x31,0x20,0x34,0x2e,
  0x33,0x35,0x43,0x37,0x2e,0x35,0x38,0x32,0x31,0x39,0x20,0x34,0x2e,0x38,0x32,0x31,
  0x36,0x37,0x20,0x38,0x2e,0x33,0x30,0x32,0x39,0x31,0x20,0x34,0x2e,0x39,0x35,0x32,
  0x33,0x35,0x20,0x38,0x2e,0x39,0x32,0x20,0x34,0x2e,0x36,0x38,0x48,0x39,0x43,0x39,
  0x2e,0x36,0x30,0x34,0x34,0x37,0x20,0x34,0x2e,0x34,0x32,0x30,0x39,0x33,0x20,0x39,
  0x2e,0x39,0x39,0x37,0x33,0x38,0x20,0x33,0x2e,0x38,0x32,0x37,0x36,0x34,0x20,0x31,
  0x30,0x20,0x33,0x2e,0x31,0x37,0x56,0x33,0x43,0x31,0x30,0x20,0x31,0x2e,0x38,0x39,
  0x35,0x34,0x33,0x20,0x31,0x30,0x2e,0x38,0x39,0x35,0x34,0x20,0x31,0x20,0x31,0x32,
  0x20,0x31,0x43,0x31,0x33,0x2e,0x31,0x30,0x34,0x36,0x20,0x31,0x20,0x31,0x34,0x20,
  0x31,0x2e,0x38,0x39,0x35,0x34,0x33,0x20,0x31,0x34,0x20,0x33,0x56,0x33,0x2e,0x30,
  0x39,0x43,0x31,0x34,0x2e,0x30,0x30,0x32,0x36,0x20,0x33,0x2e,0x37,0x34,0x37,0x36,
  0x34,0x20,0x31,0x34,0x2e,0x33,0x39,0x35,0x35,0x20,0x34,0x2e,0x33,0x34,0x30,0x39,
  0x33,0x20,0x31,0x35,0x20,0x34,0x2e,0x36,0x43,0x31,0x35,0x2e,0x36,0x31,0x37,0x31,
  0x20,0x34,0x2e,0x38,0x37,0x32,0x33,0x35,0x20,0x31,0x36,0x2e,0x33,0x33,0x37,0x38,
  0x20,0x34,0x2e,0x37,0x34,0x31,0x36,0x37,0x20,0x31,0x36,0x2e,0x38,0x32,0x20,0x34,
  0x2e,0x32,0x37,0x4c,0x31,0x36,0x2e,0x38,0x38,0x20,0x34,0x2e,0x32,0x31,0x43,0x31,
  0x37,0x2e,0x32,0x35,0x35,0x31,0x20,0x33,0x2e,0x38,0x33,0x34,0x34,0x35,0x20,0x31,
  0x37,0x2e,0x37,0x36,0x34,0x32,0x20,0x33,0x2e,0x36,0x32,0x33,0x34,0x33,0x20,0x31,
  0x38,0x2e,0x32,0x39,0x35,0x20,0x33,0x2e,0x36,0x32,0x33,0x34,0x33,0x43,0x31,0x38,
  0x2e,0x38,0x32,0x35,0x38,0x20,0x33,0x2e,0x36,0x32,0x33,0x34,0x33,0x20,0x31,0x39,
  0x2e,0x33,0x33,0x34,0x39,0x20,0x33,0x2e,0x38,0x33,0x34,0x34,0x35,0x20,0x31,0x39,
  0x2e,0x37,0x31,0x20,0x34,0x2e,0x32,0x31,0x43,0x32,0x30,0x2e,0x30,0x38,0x35,0x36,
  0x20,0x34,0x2e,0x35,0x38,0x35,0x31,0x34,0x20,0x32,0x30,0x2e,0x32,0x39,0x36,0x36,
  0x20,0x35,0x2e,0x30,0x39,0x34,0x31,0x38,0x20,0x32,0x30,0x2e,0x32,0x39,0x36,0x36,
  0x20,0x35,0x2e,0x36,0x32,0x35,0x43,0x32,0x30,0x2e,0x32,0x39,0x36,0x36,0x20,0x36,
  0x2e,0x31,0x35,0x35,0x38,0x32,0x20,0x32,0x30,0x2e,0x30,0x38,0x35,0x36,0x20,0x36,
  0x2e,0x36,0x36,0x34,0x38,0x36,0x20,0x31,0x39,0x2e,0x37,0x31,0x20,0x37,0x2e,0x30,
  0x34,0x4c,0x31,0x39,0x2e,0x36,0x35,0x20,0x37,0x2e,0x31,0x43,0x31,0x39,0x2e,0x31,
  0x37,0x38,0x33,0x20,0x37,0x2e,0x35,0x38,0x32,0x31,0x39,0x20,0x31,0x39,0x2e,0x30,
  0x34,0x37,0x37,0x20,0x38,0x2e,0x33,0x30,0x32,0x39,0x31,0x20,0x31,0x39,0x2e,0x33,
  0x32,0x20,0x38,0x2e,0x39,0x32,0x56,0x39,0x43,0x31,0x39,0x2e,0x35,0x37,0x39,0x31,
  0x20,0x39,0x2e,0x36,0x30,0x34,0x34,0x37,0x20,0x32,0x30,0x2e,0x31,0x37,0x32,0x34,
  0x20,0x39,0x2e,0x39,0x39,0x37,0x33,0x38,0x20,0x32,0x30,0x2e,0x38,0x33,0x20,0x31,
  0x30,0x48,0x32,0x31,0x43,0x32,0x32,0x2e,0x31,0x30,0x34,0x36,0x20,0x31,0x30,0x20,
  0x32,0x33,0x20,0x31,0x30,0x2e,0x38,0x39,0x35,0x34,0x20,0x32,0x33,0x20,0x31,0x32,
  0x43,0x32,0x33,0x20,0x31,0x33,0x2e,0x31,0x30,0x34,0x36,0x20,0x32,0x32,0x2e,0x31,
  0x30,0x34,0x36,0x20,0x31,0x34,0x20,0x32,0x31,0x20,0x31,0x34,0x48,0x32,0x30,0x2e,
  0x39,0x31,0x43,0x32,0x30,0x2e,0x32,0x35,0x32,0x34,0x20,0x31,0x34,0x2e,0x30,0x30,
  0x32,0x36,0x20,0x31,0x39,0x2e,0x36,0x35,0x39,0x31,0x20,0x31,0x34,0x2e,0x33,0x39,
  0x35,0x35,0x20,0x31,0x39,0x2e,0x34,0x20,0x31,0x35,0x5a,0x22,0x20,0x73,0x74,0x72,
  0x6f,0x6b,0x65,0x3d,0x22,0x23,0x30,0x30,0x30,0x30,0x30,0x30,0x22,0x20,0x73,0x74,
  0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,
  0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,
  0x6f,0x75,0x6e,0x64,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,
  0x65,0x6a,0x6f,0x69,0x6e,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,
  0x3c,0x2f,0x73,0x76,0x67,0x3e,
    // connect.svg
  0x0,0x0,0x0,0xfd,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x32,0x20,0x34,0x56,0x32,0x30,0x4d,0x34,0x20,0x31,0x32,0x48,0x32,
  0x30,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x30,0x30,0x30,0x30,
  0x30,0x30,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,
  0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x20,0x73,0x74,0x72,0x6f,
  0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x6a,0x6f,0x69,0x6e,0x3d,0x22,0x72,0x6f,0x75,
  0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,
    // disconnect.svg
  0x0,0x0,0x1,0x1,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x38,0x20,0x36,0x4c,0x36,0x20,0x31,0x38,0x4d,0x36,0x20,0x36,0x4c,
  0x31,0x38,0x20,0x31,0x38,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,
  0x30,0x30,0x30,0x30,0x30,0x30,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,
  0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,
  0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x20,
  0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x6a,0x6f,0x69,0x6e,0x3d,
  0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,
  
    // modbus.svg
  0x0,0x0,0x1,0xd5,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x34,0x20,0x36,0x48,0x32,0x30,0x4d,0x34,0x20,0x31,0x30,0x48,0x32,0x30,
  0x4d,0x34,0x20,0x31,0x34,0x48,0x32,0x30,0x4d,0x34,0x20,0x31,0x38,0x48,0x32,0x30,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x30,0x30,0x30,0x30,0x30,
  0x30,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,
  0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,
  0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,
  0x65,0x2d,0x6c,0x69,0x6e,0x65,0x6a,0x6f,0x69,0x6e,0x3d,0x22,0x72,0x6f,0x75,0x6e,
  0x64,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,
  0x78,0x3d,0x22,0x38,0x22,0x20,0x63,0x79,0x3d,0x22,0x36,0x22,0x20,0x72,0x3d,0x22,
  0x31,0x2e,0x35,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x30,0x30,0x30,0x30,
  0x30,0x30,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,
  0x63,0x78,0x3d,0x22,0x31,0x36,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,0x22,0x20,
  0x72,0x3d,0x22,0x31,0x2e,0x35,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x30,
  0x30,0x30,0x30,0x30,0x30,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,
  0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x38,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x34,
  0x22,0x20,0x72,0x3d,0x22,0x31,0x2e,0x35,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,
  0x23,0x30,0x30,0x30,0x30,0x30,0x30,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,
  0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x36,0x22,0x20,0x63,0x79,0x3d,
  0x22,0x31,0x38,0x22,0x20,0x72,0x3d,0x22,0x31,0x2e,0x35,0x22,0x20,0x66,0x69,0x6c,
  0x6c,0x3d,0x22,0x23,0x30,0x30,0x30,0x30,0x30,0x30,0x22,0x2f,0x3e,0xa,0x3c,0x2f,
  0x73,0x76,0x67,0x3e,
  
};

static const unsigned char qt_resource_name[] = {
  // icons
  0x0,0x5,
  0x0,0x6f,0xa6,0x53,
  0x0,0x69,
  0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x73,
    // setvalue.svg
  0x0,0xc,
  0xc,0x91,0x23,0xc7,
  0x0,0x73,
  0x0,0x65,0x0,0x74,0x0,0x76,0x0,0x61,0x0,0x6c,0x0,0x75,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // test.svg
  0x0,0x8,
  0xc,0xa7,0x55,0x87,
  0x0,0x74,
  0x0,0x65,0x0,0x73,0x0,0x74,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // refresh.svg
  0x0,0xb,
  0xc,0x6a,0x21,0xc7,
  0x0,0x72,
  0x0,0x65,0x0,0x66,0x0,0x72,0x0,0x65,0x0,0x73,0x0,0x68,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // settings.svg
  0x0,0xc,
  0xb,0xdf,0x2c,0xc7,
  0x0,0x73,
  0x0,0x65,0x0,0x74,0x0,0x74,0x0,0x69,0x0,0x6e,0x0,0x67,0x0,0x73,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // connect.svg
  0x0,0xb,
  0xb,0x73,0x90,0x47,
  0x0,0x63,
  0x0,0x6f,0x0,0x6e,0x0,0x6e,0x0,0x65,0x0,0x63,0x0,0x74,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // disconnect.svg
  0x0,0xe,
  0xa,0x93,0x8a,0x87,
  0x0,0x64,
  0x0,0x69,0x0,0x73,0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x6e,0x0,0x65,0x0,0x63,0x0,0x74,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // modbus.svg
  0x0,0xa,
  0x9,0xcc,0xd1,0x87,
  0x0,0x6d,
  0x0,0x6f,0x0,0x64,0x0,0x62,0x0,0x75,0x0,0x73,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/icons
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x2,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/icons/icons
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x7,0x0,0x0,0x0,0x3,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/icons/icons/modbus.svg
  0x0,0x0,0x0,0xbc,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xf,0x31,
0x0,0x0,0x1,0x97,0x3d,0xa1,0xdb,0x1b,
  // :/icons/icons/disconnect.svg
  0x0,0x0,0x0,0x9a,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xe,0x2c,
0x0,0x0,0x1,0x97,0x3d,0xa1,0xda,0xe2,
  // :/icons/icons/connect.svg
  0x0,0x0,0x0,0x7e,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xd,0x2b,
0x0,0x0,0x1,0x97,0x3d,0xa1,0xda,0xe2,
  // :/icons/icons/settings.svg
  0x0,0x0,0x0,0x60,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x4,0x90,
0x0,0x0,0x1,0x97,0x3d,0xa1,0xdb,0x1b,
  // :/icons/icons/refresh.svg
  0x0,0x0,0x0,0x44,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x3,0x1c,
0x0,0x0,0x1,0x97,0x3d,0xa1,0xdb,0x1b,
  // :/icons/icons/setvalue.svg
  0x0,0x0,0x0,0x10,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x97,0x62,0x85,0xe1,0x54,
  // :/icons/icons/test.svg
  0x0,0x0,0x0,0x2e,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x1,0x85,
0x0,0x0,0x1,0x97,0x3d,0xa1,0xdb,0x4a,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#if defined(QT_INLINE_NAMESPACE)
inline namespace QT_NAMESPACE {
#elif defined(QT_NAMESPACE)
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#if defined(__ELF__) || defined(__APPLE__)
static inline unsigned char qResourceFeatureZlib()
{
    extern const unsigned char qt_resourceFeatureZlib;
    return qt_resourceFeatureZlib;
}
#else
unsigned char qResourceFeatureZlib();
#endif

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_icons)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_icons)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_icons)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_icons)()
{
    int version = 3;
    version += QT_RCC_PREPEND_NAMESPACE(qResourceFeatureZlib());
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

#ifdef __clang__
#   pragma clang diagnostic push
#   pragma clang diagnostic ignored "-Wexit-time-destructors"
#endif

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_icons)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_icons)(); }
   } dummy;
}

#ifdef __clang__
#   pragma clang diagnostic pop
#endif
