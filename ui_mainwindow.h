/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 6.8.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtGui/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenu>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QTextBrowser>
#include <QtWidgets/QToolBar>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QAction *actionExit;
    QAction *actionStartTest;
    QAction *actionStopTest;
    QAction *actionMapping;
    QAction *actionConnectA;
    QAction *actionConnectB;
    QAction *actionDisconnectA;
    QAction *actionDisconnectB;
    QAction *actionRefresh;
    QAction *actionGenerateReport;
    QAction *actionViewReport;
    QAction *actionImportConfig;
    QAction *actionExportConfig;
    QAction *actionSetValue;
    QWidget *centralwidget;
    QVBoxLayout *verticalLayout;
    QHBoxLayout *horizontalLayoutTop;
    QGroupBox *groupBoxSerialA;
    QGridLayout *gridLayoutA;
    QLabel *labelPortA;
    QComboBox *comboBoxPortA;
    QLabel *labelBaudA;
    QComboBox *comboBoxBaudA;
    QLabel *labelParityA;
    QComboBox *comboBoxParityA;
    QLabel *labelDataA;
    QComboBox *comboBoxDataA;
    QLabel *labelStopA;
    QComboBox *comboBoxStopA;
    QGroupBox *groupBoxSerialB;
    QGridLayout *gridLayoutB;
    QLabel *labelPortB;
    QComboBox *comboBoxPortB;
    QLabel *labelBaudB;
    QComboBox *comboBoxBaudB;
    QLabel *labelParityB;
    QComboBox *comboBoxParityB;
    QLabel *labelDataB;
    QComboBox *comboBoxDataB;
    QLabel *labelStopB;
    QComboBox *comboBoxStopB;
    QGroupBox *testListGroupBox;
    QVBoxLayout *verticalLayout_3;
    QTableWidget *tableWidgetTests;
    QHBoxLayout *horizontalLayoutButtons;
    QPushButton *pushButtonAddTest;
    QPushButton *pushButtonRemoveTest;
    QPushButton *pushButtonCopyTest;
    QPushButton *pushButtonClearTests;
    QPushButton *pushButtonImport;
    QPushButton *pushButtonExport;
    QPushButton *pushButtonGenerateReport;
    QSpacerItem *horizontalSpacer;
    QLabel *labelDelay;
    QSpinBox *spinBoxDelay;
    QCheckBox *checkBoxStopOnFailure;
    QCheckBox *checkBoxUseCRC16;
    QPushButton *pushButtonTest;
    QGroupBox *modbusTestListGroupBox;
    QVBoxLayout *verticalLayout_4;
    QTableWidget *tableWidgetModbusTests;
    QHBoxLayout *horizontalLayoutModbusButtons;
    QPushButton *pushButtonAddModbusTest;
    QPushButton *pushButtonRemoveModbusTest;
    QPushButton *pushButtonCopyModbusTest;
    QPushButton *pushButtonClearModbusTests;
    QPushButton *pushButtonImportModbus;
    QPushButton *pushButtonExportModbus;
    QPushButton *pushButtonGenerateModbusReport;
    QSpacerItem *horizontalSpacerModbus;
    QLabel *labelModbusTimeout;
    QSpinBox *spinBoxModbusTimeout;
    QPushButton *pushButtonModbusTest;
    QHBoxLayout *horizontalLayoutBottom;
    QVBoxLayout *verticalLayoutA;
    QGroupBox *groupBoxSendA;
    QHBoxLayout *horizontalLayoutSendA;
    QLineEdit *lineEditSendDataA;
    QPushButton *pushButtonSendA;
    QGroupBox *groupBoxDisplayA;
    QVBoxLayout *verticalLayoutDisplayA;
    QTextBrowser *textBrowserA;
    QVBoxLayout *verticalLayoutB;
    QGroupBox *groupBoxSendB;
    QHBoxLayout *horizontalLayoutSendB;
    QLineEdit *lineEditSendDataB;
    QPushButton *pushButtonSendB;
    QGroupBox *groupBoxDisplayB;
    QVBoxLayout *verticalLayoutDisplayB;
    QTextBrowser *textBrowserB;
    QToolBar *mainToolBar;
    QMenuBar *menubar;
    QMenu *menuFile;
    QMenu *menuTest;
    QMenu *menuConfig;
    QMenu *menuReport;
    QMenu *menuTools;
    QStatusBar *statusbar;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName("MainWindow");
        MainWindow->resize(1382, 800);
        actionExit = new QAction(MainWindow);
        actionExit->setObjectName("actionExit");
        actionStartTest = new QAction(MainWindow);
        actionStartTest->setObjectName("actionStartTest");
        actionStopTest = new QAction(MainWindow);
        actionStopTest->setObjectName("actionStopTest");
        actionMapping = new QAction(MainWindow);
        actionMapping->setObjectName("actionMapping");
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/icons/test.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        icon.addFile(QString::fromUtf8(":/icons/test.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::On);
        actionMapping->setIcon(icon);
        actionConnectA = new QAction(MainWindow);
        actionConnectA->setObjectName("actionConnectA");
        QIcon icon1;
        icon1.addFile(QString::fromUtf8(":/icons/connect.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        icon1.addFile(QString::fromUtf8(":/icons/connect.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::On);
        actionConnectA->setIcon(icon1);
        actionConnectB = new QAction(MainWindow);
        actionConnectB->setObjectName("actionConnectB");
        actionConnectB->setIcon(icon1);
        actionDisconnectA = new QAction(MainWindow);
        actionDisconnectA->setObjectName("actionDisconnectA");
        QIcon icon2;
        icon2.addFile(QString::fromUtf8(":/icons/disconnect.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        icon2.addFile(QString::fromUtf8(":/icons/disconnect.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::On);
        actionDisconnectA->setIcon(icon2);
        actionDisconnectB = new QAction(MainWindow);
        actionDisconnectB->setObjectName("actionDisconnectB");
        actionDisconnectB->setIcon(icon2);
        actionRefresh = new QAction(MainWindow);
        actionRefresh->setObjectName("actionRefresh");
        QIcon icon3;
        icon3.addFile(QString::fromUtf8(":/icons/refresh.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        icon3.addFile(QString::fromUtf8(":/icons/refresh.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::On);
        actionRefresh->setIcon(icon3);
        actionGenerateReport = new QAction(MainWindow);
        actionGenerateReport->setObjectName("actionGenerateReport");
        actionViewReport = new QAction(MainWindow);
        actionViewReport->setObjectName("actionViewReport");
        actionImportConfig = new QAction(MainWindow);
        actionImportConfig->setObjectName("actionImportConfig");
        actionExportConfig = new QAction(MainWindow);
        actionExportConfig->setObjectName("actionExportConfig");
        actionSetValue = new QAction(MainWindow);
        actionSetValue->setObjectName("actionSetValue");
        QIcon icon4;
        icon4.addFile(QString::fromUtf8(":/icons/setvalue.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        icon4.addFile(QString::fromUtf8(":/icons/setvalue.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::On);
        actionSetValue->setIcon(icon4);
        centralwidget = new QWidget(MainWindow);
        centralwidget->setObjectName("centralwidget");
        verticalLayout = new QVBoxLayout(centralwidget);
        verticalLayout->setObjectName("verticalLayout");
        horizontalLayoutTop = new QHBoxLayout();
        horizontalLayoutTop->setObjectName("horizontalLayoutTop");
        groupBoxSerialA = new QGroupBox(centralwidget);
        groupBoxSerialA->setObjectName("groupBoxSerialA");
        gridLayoutA = new QGridLayout(groupBoxSerialA);
        gridLayoutA->setObjectName("gridLayoutA");
        labelPortA = new QLabel(groupBoxSerialA);
        labelPortA->setObjectName("labelPortA");

        gridLayoutA->addWidget(labelPortA, 0, 0, 1, 1);

        comboBoxPortA = new QComboBox(groupBoxSerialA);
        comboBoxPortA->setObjectName("comboBoxPortA");

        gridLayoutA->addWidget(comboBoxPortA, 0, 1, 1, 1);

        labelBaudA = new QLabel(groupBoxSerialA);
        labelBaudA->setObjectName("labelBaudA");

        gridLayoutA->addWidget(labelBaudA, 0, 2, 1, 1);

        comboBoxBaudA = new QComboBox(groupBoxSerialA);
        comboBoxBaudA->setObjectName("comboBoxBaudA");

        gridLayoutA->addWidget(comboBoxBaudA, 0, 3, 1, 1);

        labelParityA = new QLabel(groupBoxSerialA);
        labelParityA->setObjectName("labelParityA");

        gridLayoutA->addWidget(labelParityA, 0, 4, 1, 1);

        comboBoxParityA = new QComboBox(groupBoxSerialA);
        comboBoxParityA->setObjectName("comboBoxParityA");

        gridLayoutA->addWidget(comboBoxParityA, 0, 5, 1, 1);

        labelDataA = new QLabel(groupBoxSerialA);
        labelDataA->setObjectName("labelDataA");

        gridLayoutA->addWidget(labelDataA, 0, 6, 1, 1);

        comboBoxDataA = new QComboBox(groupBoxSerialA);
        comboBoxDataA->setObjectName("comboBoxDataA");

        gridLayoutA->addWidget(comboBoxDataA, 0, 7, 1, 1);

        labelStopA = new QLabel(groupBoxSerialA);
        labelStopA->setObjectName("labelStopA");

        gridLayoutA->addWidget(labelStopA, 0, 8, 1, 1);

        comboBoxStopA = new QComboBox(groupBoxSerialA);
        comboBoxStopA->setObjectName("comboBoxStopA");

        gridLayoutA->addWidget(comboBoxStopA, 0, 9, 1, 1);


        horizontalLayoutTop->addWidget(groupBoxSerialA);

        groupBoxSerialB = new QGroupBox(centralwidget);
        groupBoxSerialB->setObjectName("groupBoxSerialB");
        gridLayoutB = new QGridLayout(groupBoxSerialB);
        gridLayoutB->setObjectName("gridLayoutB");
        labelPortB = new QLabel(groupBoxSerialB);
        labelPortB->setObjectName("labelPortB");

        gridLayoutB->addWidget(labelPortB, 0, 0, 1, 1);

        comboBoxPortB = new QComboBox(groupBoxSerialB);
        comboBoxPortB->setObjectName("comboBoxPortB");

        gridLayoutB->addWidget(comboBoxPortB, 0, 1, 1, 1);

        labelBaudB = new QLabel(groupBoxSerialB);
        labelBaudB->setObjectName("labelBaudB");

        gridLayoutB->addWidget(labelBaudB, 0, 2, 1, 1);

        comboBoxBaudB = new QComboBox(groupBoxSerialB);
        comboBoxBaudB->setObjectName("comboBoxBaudB");

        gridLayoutB->addWidget(comboBoxBaudB, 0, 3, 1, 1);

        labelParityB = new QLabel(groupBoxSerialB);
        labelParityB->setObjectName("labelParityB");

        gridLayoutB->addWidget(labelParityB, 0, 4, 1, 1);

        comboBoxParityB = new QComboBox(groupBoxSerialB);
        comboBoxParityB->setObjectName("comboBoxParityB");

        gridLayoutB->addWidget(comboBoxParityB, 0, 5, 1, 1);

        labelDataB = new QLabel(groupBoxSerialB);
        labelDataB->setObjectName("labelDataB");

        gridLayoutB->addWidget(labelDataB, 0, 6, 1, 1);

        comboBoxDataB = new QComboBox(groupBoxSerialB);
        comboBoxDataB->setObjectName("comboBoxDataB");

        gridLayoutB->addWidget(comboBoxDataB, 0, 7, 1, 1);

        labelStopB = new QLabel(groupBoxSerialB);
        labelStopB->setObjectName("labelStopB");

        gridLayoutB->addWidget(labelStopB, 0, 8, 1, 1);

        comboBoxStopB = new QComboBox(groupBoxSerialB);
        comboBoxStopB->setObjectName("comboBoxStopB");

        gridLayoutB->addWidget(comboBoxStopB, 0, 9, 1, 1);


        horizontalLayoutTop->addWidget(groupBoxSerialB);


        verticalLayout->addLayout(horizontalLayoutTop);

        testListGroupBox = new QGroupBox(centralwidget);
        testListGroupBox->setObjectName("testListGroupBox");
        verticalLayout_3 = new QVBoxLayout(testListGroupBox);
        verticalLayout_3->setObjectName("verticalLayout_3");
        tableWidgetTests = new QTableWidget(testListGroupBox);
        if (tableWidgetTests->columnCount() < 9)
            tableWidgetTests->setColumnCount(9);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        tableWidgetTests->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        tableWidgetTests->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        tableWidgetTests->setHorizontalHeaderItem(2, __qtablewidgetitem2);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        tableWidgetTests->setHorizontalHeaderItem(3, __qtablewidgetitem3);
        QTableWidgetItem *__qtablewidgetitem4 = new QTableWidgetItem();
        tableWidgetTests->setHorizontalHeaderItem(4, __qtablewidgetitem4);
        QTableWidgetItem *__qtablewidgetitem5 = new QTableWidgetItem();
        tableWidgetTests->setHorizontalHeaderItem(5, __qtablewidgetitem5);
        QTableWidgetItem *__qtablewidgetitem6 = new QTableWidgetItem();
        tableWidgetTests->setHorizontalHeaderItem(6, __qtablewidgetitem6);
        QTableWidgetItem *__qtablewidgetitem7 = new QTableWidgetItem();
        tableWidgetTests->setHorizontalHeaderItem(7, __qtablewidgetitem7);
        QTableWidgetItem *__qtablewidgetitem8 = new QTableWidgetItem();
        tableWidgetTests->setHorizontalHeaderItem(8, __qtablewidgetitem8);
        tableWidgetTests->setObjectName("tableWidgetTests");
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(tableWidgetTests->sizePolicy().hasHeightForWidth());
        tableWidgetTests->setSizePolicy(sizePolicy);
        tableWidgetTests->setAlternatingRowColors(true);
        tableWidgetTests->setSelectionMode(QAbstractItemView::SelectionMode::SingleSelection);
        tableWidgetTests->setSelectionBehavior(QAbstractItemView::SelectionBehavior::SelectRows);
        tableWidgetTests->horizontalHeader()->setStretchLastSection(true);

        verticalLayout_3->addWidget(tableWidgetTests);

        horizontalLayoutButtons = new QHBoxLayout();
        horizontalLayoutButtons->setObjectName("horizontalLayoutButtons");
        pushButtonAddTest = new QPushButton(testListGroupBox);
        pushButtonAddTest->setObjectName("pushButtonAddTest");

        horizontalLayoutButtons->addWidget(pushButtonAddTest);

        pushButtonRemoveTest = new QPushButton(testListGroupBox);
        pushButtonRemoveTest->setObjectName("pushButtonRemoveTest");

        horizontalLayoutButtons->addWidget(pushButtonRemoveTest);

        pushButtonCopyTest = new QPushButton(testListGroupBox);
        pushButtonCopyTest->setObjectName("pushButtonCopyTest");

        horizontalLayoutButtons->addWidget(pushButtonCopyTest);

        pushButtonClearTests = new QPushButton(testListGroupBox);
        pushButtonClearTests->setObjectName("pushButtonClearTests");

        horizontalLayoutButtons->addWidget(pushButtonClearTests);

        pushButtonImport = new QPushButton(testListGroupBox);
        pushButtonImport->setObjectName("pushButtonImport");

        horizontalLayoutButtons->addWidget(pushButtonImport);

        pushButtonExport = new QPushButton(testListGroupBox);
        pushButtonExport->setObjectName("pushButtonExport");

        horizontalLayoutButtons->addWidget(pushButtonExport);

        pushButtonGenerateReport = new QPushButton(testListGroupBox);
        pushButtonGenerateReport->setObjectName("pushButtonGenerateReport");

        horizontalLayoutButtons->addWidget(pushButtonGenerateReport);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayoutButtons->addItem(horizontalSpacer);

        labelDelay = new QLabel(testListGroupBox);
        labelDelay->setObjectName("labelDelay");

        horizontalLayoutButtons->addWidget(labelDelay);

        spinBoxDelay = new QSpinBox(testListGroupBox);
        spinBoxDelay->setObjectName("spinBoxDelay");
        spinBoxDelay->setMinimum(100);
        spinBoxDelay->setMaximum(5000);
        spinBoxDelay->setValue(1000);

        horizontalLayoutButtons->addWidget(spinBoxDelay);

        checkBoxStopOnFailure = new QCheckBox(testListGroupBox);
        checkBoxStopOnFailure->setObjectName("checkBoxStopOnFailure");

        horizontalLayoutButtons->addWidget(checkBoxStopOnFailure);

        checkBoxUseCRC16 = new QCheckBox(testListGroupBox);
        checkBoxUseCRC16->setObjectName("checkBoxUseCRC16");

        horizontalLayoutButtons->addWidget(checkBoxUseCRC16);

        pushButtonTest = new QPushButton(testListGroupBox);
        pushButtonTest->setObjectName("pushButtonTest");

        horizontalLayoutButtons->addWidget(pushButtonTest);


        verticalLayout_3->addLayout(horizontalLayoutButtons);


        verticalLayout->addWidget(testListGroupBox);

        modbusTestListGroupBox = new QGroupBox(centralwidget);
        modbusTestListGroupBox->setObjectName("modbusTestListGroupBox");
        verticalLayout_4 = new QVBoxLayout(modbusTestListGroupBox);
        verticalLayout_4->setObjectName("verticalLayout_4");
        tableWidgetModbusTests = new QTableWidget(modbusTestListGroupBox);
        if (tableWidgetModbusTests->columnCount() < 8)
            tableWidgetModbusTests->setColumnCount(8);
        QTableWidgetItem *__qtablewidgetitem9 = new QTableWidgetItem();
        tableWidgetModbusTests->setHorizontalHeaderItem(0, __qtablewidgetitem9);
        QTableWidgetItem *__qtablewidgetitem10 = new QTableWidgetItem();
        tableWidgetModbusTests->setHorizontalHeaderItem(1, __qtablewidgetitem10);
        QTableWidgetItem *__qtablewidgetitem11 = new QTableWidgetItem();
        tableWidgetModbusTests->setHorizontalHeaderItem(2, __qtablewidgetitem11);
        QTableWidgetItem *__qtablewidgetitem12 = new QTableWidgetItem();
        tableWidgetModbusTests->setHorizontalHeaderItem(3, __qtablewidgetitem12);
        QTableWidgetItem *__qtablewidgetitem13 = new QTableWidgetItem();
        tableWidgetModbusTests->setHorizontalHeaderItem(4, __qtablewidgetitem13);
        QTableWidgetItem *__qtablewidgetitem14 = new QTableWidgetItem();
        tableWidgetModbusTests->setHorizontalHeaderItem(5, __qtablewidgetitem14);
        QTableWidgetItem *__qtablewidgetitem15 = new QTableWidgetItem();
        tableWidgetModbusTests->setHorizontalHeaderItem(6, __qtablewidgetitem15);
        QTableWidgetItem *__qtablewidgetitem16 = new QTableWidgetItem();
        tableWidgetModbusTests->setHorizontalHeaderItem(7, __qtablewidgetitem16);
        tableWidgetModbusTests->setObjectName("tableWidgetModbusTests");
        sizePolicy.setHeightForWidth(tableWidgetModbusTests->sizePolicy().hasHeightForWidth());
        tableWidgetModbusTests->setSizePolicy(sizePolicy);
        tableWidgetModbusTests->setAlternatingRowColors(true);
        tableWidgetModbusTests->setSelectionMode(QAbstractItemView::SelectionMode::SingleSelection);
        tableWidgetModbusTests->setSelectionBehavior(QAbstractItemView::SelectionBehavior::SelectRows);
        tableWidgetModbusTests->horizontalHeader()->setStretchLastSection(true);

        verticalLayout_4->addWidget(tableWidgetModbusTests);

        horizontalLayoutModbusButtons = new QHBoxLayout();
        horizontalLayoutModbusButtons->setObjectName("horizontalLayoutModbusButtons");
        pushButtonAddModbusTest = new QPushButton(modbusTestListGroupBox);
        pushButtonAddModbusTest->setObjectName("pushButtonAddModbusTest");

        horizontalLayoutModbusButtons->addWidget(pushButtonAddModbusTest);

        pushButtonRemoveModbusTest = new QPushButton(modbusTestListGroupBox);
        pushButtonRemoveModbusTest->setObjectName("pushButtonRemoveModbusTest");

        horizontalLayoutModbusButtons->addWidget(pushButtonRemoveModbusTest);

        pushButtonCopyModbusTest = new QPushButton(modbusTestListGroupBox);
        pushButtonCopyModbusTest->setObjectName("pushButtonCopyModbusTest");

        horizontalLayoutModbusButtons->addWidget(pushButtonCopyModbusTest);

        pushButtonClearModbusTests = new QPushButton(modbusTestListGroupBox);
        pushButtonClearModbusTests->setObjectName("pushButtonClearModbusTests");

        horizontalLayoutModbusButtons->addWidget(pushButtonClearModbusTests);

        pushButtonImportModbus = new QPushButton(modbusTestListGroupBox);
        pushButtonImportModbus->setObjectName("pushButtonImportModbus");

        horizontalLayoutModbusButtons->addWidget(pushButtonImportModbus);

        pushButtonExportModbus = new QPushButton(modbusTestListGroupBox);
        pushButtonExportModbus->setObjectName("pushButtonExportModbus");

        horizontalLayoutModbusButtons->addWidget(pushButtonExportModbus);

        pushButtonGenerateModbusReport = new QPushButton(modbusTestListGroupBox);
        pushButtonGenerateModbusReport->setObjectName("pushButtonGenerateModbusReport");

        horizontalLayoutModbusButtons->addWidget(pushButtonGenerateModbusReport);

        horizontalSpacerModbus = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayoutModbusButtons->addItem(horizontalSpacerModbus);

        labelModbusTimeout = new QLabel(modbusTestListGroupBox);
        labelModbusTimeout->setObjectName("labelModbusTimeout");

        horizontalLayoutModbusButtons->addWidget(labelModbusTimeout);

        spinBoxModbusTimeout = new QSpinBox(modbusTestListGroupBox);
        spinBoxModbusTimeout->setObjectName("spinBoxModbusTimeout");
        spinBoxModbusTimeout->setMinimum(100);
        spinBoxModbusTimeout->setMaximum(5000);
        spinBoxModbusTimeout->setValue(1000);

        horizontalLayoutModbusButtons->addWidget(spinBoxModbusTimeout);

        pushButtonModbusTest = new QPushButton(modbusTestListGroupBox);
        pushButtonModbusTest->setObjectName("pushButtonModbusTest");

        horizontalLayoutModbusButtons->addWidget(pushButtonModbusTest);


        verticalLayout_4->addLayout(horizontalLayoutModbusButtons);


        verticalLayout->addWidget(modbusTestListGroupBox);

        horizontalLayoutBottom = new QHBoxLayout();
        horizontalLayoutBottom->setObjectName("horizontalLayoutBottom");
        verticalLayoutA = new QVBoxLayout();
        verticalLayoutA->setObjectName("verticalLayoutA");
        groupBoxSendA = new QGroupBox(centralwidget);
        groupBoxSendA->setObjectName("groupBoxSendA");
        horizontalLayoutSendA = new QHBoxLayout(groupBoxSendA);
        horizontalLayoutSendA->setObjectName("horizontalLayoutSendA");
        lineEditSendDataA = new QLineEdit(groupBoxSendA);
        lineEditSendDataA->setObjectName("lineEditSendDataA");

        horizontalLayoutSendA->addWidget(lineEditSendDataA);

        pushButtonSendA = new QPushButton(groupBoxSendA);
        pushButtonSendA->setObjectName("pushButtonSendA");

        horizontalLayoutSendA->addWidget(pushButtonSendA);


        verticalLayoutA->addWidget(groupBoxSendA);

        groupBoxDisplayA = new QGroupBox(centralwidget);
        groupBoxDisplayA->setObjectName("groupBoxDisplayA");
        verticalLayoutDisplayA = new QVBoxLayout(groupBoxDisplayA);
        verticalLayoutDisplayA->setObjectName("verticalLayoutDisplayA");
        textBrowserA = new QTextBrowser(groupBoxDisplayA);
        textBrowserA->setObjectName("textBrowserA");

        verticalLayoutDisplayA->addWidget(textBrowserA);


        verticalLayoutA->addWidget(groupBoxDisplayA);


        horizontalLayoutBottom->addLayout(verticalLayoutA);

        verticalLayoutB = new QVBoxLayout();
        verticalLayoutB->setObjectName("verticalLayoutB");
        groupBoxSendB = new QGroupBox(centralwidget);
        groupBoxSendB->setObjectName("groupBoxSendB");
        horizontalLayoutSendB = new QHBoxLayout(groupBoxSendB);
        horizontalLayoutSendB->setObjectName("horizontalLayoutSendB");
        lineEditSendDataB = new QLineEdit(groupBoxSendB);
        lineEditSendDataB->setObjectName("lineEditSendDataB");

        horizontalLayoutSendB->addWidget(lineEditSendDataB);

        pushButtonSendB = new QPushButton(groupBoxSendB);
        pushButtonSendB->setObjectName("pushButtonSendB");

        horizontalLayoutSendB->addWidget(pushButtonSendB);


        verticalLayoutB->addWidget(groupBoxSendB);

        groupBoxDisplayB = new QGroupBox(centralwidget);
        groupBoxDisplayB->setObjectName("groupBoxDisplayB");
        verticalLayoutDisplayB = new QVBoxLayout(groupBoxDisplayB);
        verticalLayoutDisplayB->setObjectName("verticalLayoutDisplayB");
        textBrowserB = new QTextBrowser(groupBoxDisplayB);
        textBrowserB->setObjectName("textBrowserB");

        verticalLayoutDisplayB->addWidget(textBrowserB);


        verticalLayoutB->addWidget(groupBoxDisplayB);


        horizontalLayoutBottom->addLayout(verticalLayoutB);


        verticalLayout->addLayout(horizontalLayoutBottom);

        MainWindow->setCentralWidget(centralwidget);
        mainToolBar = new QToolBar(MainWindow);
        mainToolBar->setObjectName("mainToolBar");
        MainWindow->addToolBar(Qt::ToolBarArea::TopToolBarArea, mainToolBar);
        menubar = new QMenuBar(MainWindow);
        menubar->setObjectName("menubar");
        menubar->setGeometry(QRect(0, 0, 1382, 20));
        menuFile = new QMenu(menubar);
        menuFile->setObjectName("menuFile");
        menuTest = new QMenu(menubar);
        menuTest->setObjectName("menuTest");
        menuConfig = new QMenu(menubar);
        menuConfig->setObjectName("menuConfig");
        menuReport = new QMenu(menubar);
        menuReport->setObjectName("menuReport");
        menuTools = new QMenu(menubar);
        menuTools->setObjectName("menuTools");
        MainWindow->setMenuBar(menubar);
        statusbar = new QStatusBar(MainWindow);
        statusbar->setObjectName("statusbar");
        MainWindow->setStatusBar(statusbar);

        mainToolBar->addAction(actionConnectA);
        mainToolBar->addAction(actionConnectB);
        mainToolBar->addAction(actionDisconnectA);
        mainToolBar->addAction(actionDisconnectB);
        mainToolBar->addAction(actionRefresh);
        mainToolBar->addSeparator();
        mainToolBar->addAction(actionMapping);
        mainToolBar->addAction(actionSetValue);
        menubar->addAction(menuFile->menuAction());
        menubar->addAction(menuConfig->menuAction());
        menubar->addAction(menuTest->menuAction());
        menubar->addAction(menuReport->menuAction());
        menubar->addAction(menuTools->menuAction());
        menuFile->addAction(actionImportConfig);
        menuFile->addAction(actionExportConfig);
        menuFile->addSeparator();
        menuFile->addAction(actionExit);
        menuTest->addAction(actionStartTest);
        menuTest->addAction(actionStopTest);
        menuTest->addSeparator();
        menuConfig->addAction(actionMapping);
        menuConfig->addSeparator();
        menuConfig->addAction(actionSetValue);
        menuReport->addAction(actionGenerateReport);
        menuReport->addAction(actionViewReport);
        menuTools->addAction(actionRefresh);
        menuTools->addSeparator();
        menuTools->addAction(actionConnectA);
        menuTools->addAction(actionConnectB);
        menuTools->addAction(actionDisconnectA);
        menuTools->addAction(actionDisconnectB);

        retranslateUi(MainWindow);

        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "JcSoft - \345\217\214\344\270\262\345\217\243\351\200\232\344\277\241", nullptr));
        actionExit->setText(QCoreApplication::translate("MainWindow", "\351\200\200\345\207\272", nullptr));
        actionStartTest->setText(QCoreApplication::translate("MainWindow", "\345\274\200\345\247\213\346\265\213\350\257\225", nullptr));
        actionStopTest->setText(QCoreApplication::translate("MainWindow", "\345\201\234\346\255\242\346\265\213\350\257\225", nullptr));
        actionMapping->setText(QCoreApplication::translate("MainWindow", "TEST\351\205\215\347\275\256", nullptr));
#if QT_CONFIG(tooltip)
        actionMapping->setToolTip(QCoreApplication::translate("MainWindow", "TEST\351\205\215\347\275\256", nullptr));
#endif // QT_CONFIG(tooltip)
        actionConnectA->setText(QCoreApplication::translate("MainWindow", "\350\277\236\346\216\245\344\270\262\345\217\243A", nullptr));
#if QT_CONFIG(tooltip)
        actionConnectA->setToolTip(QCoreApplication::translate("MainWindow", "\350\277\236\346\216\245\344\270\262\345\217\243A", nullptr));
#endif // QT_CONFIG(tooltip)
        actionConnectB->setText(QCoreApplication::translate("MainWindow", "\350\277\236\346\216\245\344\270\262\345\217\243B", nullptr));
#if QT_CONFIG(tooltip)
        actionConnectB->setToolTip(QCoreApplication::translate("MainWindow", "\350\277\236\346\216\245\344\270\262\345\217\243B", nullptr));
#endif // QT_CONFIG(tooltip)
        actionDisconnectA->setText(QCoreApplication::translate("MainWindow", "\346\226\255\345\274\200\344\270\262\345\217\243A", nullptr));
#if QT_CONFIG(tooltip)
        actionDisconnectA->setToolTip(QCoreApplication::translate("MainWindow", "\346\226\255\345\274\200\344\270\262\345\217\243A", nullptr));
#endif // QT_CONFIG(tooltip)
        actionDisconnectB->setText(QCoreApplication::translate("MainWindow", "\346\226\255\345\274\200\344\270\262\345\217\243B", nullptr));
#if QT_CONFIG(tooltip)
        actionDisconnectB->setToolTip(QCoreApplication::translate("MainWindow", "\346\226\255\345\274\200\344\270\262\345\217\243B", nullptr));
#endif // QT_CONFIG(tooltip)
        actionRefresh->setText(QCoreApplication::translate("MainWindow", "\345\210\267\346\226\260\344\270\262\345\217\243", nullptr));
#if QT_CONFIG(tooltip)
        actionRefresh->setToolTip(QCoreApplication::translate("MainWindow", "\345\210\267\346\226\260\344\270\262\345\217\243", nullptr));
#endif // QT_CONFIG(tooltip)
        actionGenerateReport->setText(QCoreApplication::translate("MainWindow", "\347\224\237\346\210\220\346\212\245\350\241\250", nullptr));
        actionViewReport->setText(QCoreApplication::translate("MainWindow", "\346\237\245\347\234\213\346\212\245\350\241\250", nullptr));
        actionImportConfig->setText(QCoreApplication::translate("MainWindow", "\345\257\274\345\205\245\351\205\215\347\275\256", nullptr));
        actionExportConfig->setText(QCoreApplication::translate("MainWindow", "\345\257\274\345\207\272\351\205\215\347\275\256", nullptr));
        actionSetValue->setText(QCoreApplication::translate("MainWindow", "\345\256\232\345\200\274\345\217\254\345\224\244", nullptr));
#if QT_CONFIG(tooltip)
        actionSetValue->setToolTip(QCoreApplication::translate("MainWindow", "\345\256\232\345\200\274\345\217\254\345\224\244", nullptr));
#endif // QT_CONFIG(tooltip)
        groupBoxSerialA->setTitle(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243A\351\205\215\347\275\256", nullptr));
        labelPortA->setText(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243\345\217\267\357\274\232", nullptr));
        labelBaudA->setText(QCoreApplication::translate("MainWindow", "\346\263\242\347\211\271\347\216\207\357\274\232", nullptr));
        labelParityA->setText(QCoreApplication::translate("MainWindow", "\346\240\241\351\252\214\344\275\215\357\274\232", nullptr));
        labelDataA->setText(QCoreApplication::translate("MainWindow", "\346\225\260\346\215\256\344\275\215\357\274\232", nullptr));
        labelStopA->setText(QCoreApplication::translate("MainWindow", "\345\201\234\346\255\242\344\275\215\357\274\232", nullptr));
        groupBoxSerialB->setTitle(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243B\351\205\215\347\275\256", nullptr));
        labelPortB->setText(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243\345\217\267\357\274\232", nullptr));
        labelBaudB->setText(QCoreApplication::translate("MainWindow", "\346\263\242\347\211\271\347\216\207\357\274\232", nullptr));
        labelParityB->setText(QCoreApplication::translate("MainWindow", "\346\240\241\351\252\214\344\275\215\357\274\232", nullptr));
        labelDataB->setText(QCoreApplication::translate("MainWindow", "\346\225\260\346\215\256\344\275\215\357\274\232", nullptr));
        labelStopB->setText(QCoreApplication::translate("MainWindow", "\345\201\234\346\255\242\344\275\215\357\274\232", nullptr));
        testListGroupBox->setTitle(QCoreApplication::translate("MainWindow", "UART\346\265\213\350\257\225\345\210\227\350\241\250", nullptr));
        QTableWidgetItem *___qtablewidgetitem = tableWidgetTests->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QCoreApplication::translate("MainWindow", "\345\272\217\345\217\267", nullptr));
        QTableWidgetItem *___qtablewidgetitem1 = tableWidgetTests->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QCoreApplication::translate("MainWindow", "\346\265\213\350\257\225\346\217\217\350\277\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem2 = tableWidgetTests->horizontalHeaderItem(2);
        ___qtablewidgetitem2->setText(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243A\345\217\221\351\200\201", nullptr));
        QTableWidgetItem *___qtablewidgetitem3 = tableWidgetTests->horizontalHeaderItem(3);
        ___qtablewidgetitem3->setText(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243A\346\234\237\346\234\233\346\216\245\346\224\266", nullptr));
        QTableWidgetItem *___qtablewidgetitem4 = tableWidgetTests->horizontalHeaderItem(4);
        ___qtablewidgetitem4->setText(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243A\346\216\245\346\224\266", nullptr));
        QTableWidgetItem *___qtablewidgetitem5 = tableWidgetTests->horizontalHeaderItem(5);
        ___qtablewidgetitem5->setText(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243B\345\217\221\351\200\201", nullptr));
        QTableWidgetItem *___qtablewidgetitem6 = tableWidgetTests->horizontalHeaderItem(6);
        ___qtablewidgetitem6->setText(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243B\346\234\237\346\234\233\346\216\245\346\224\266", nullptr));
        QTableWidgetItem *___qtablewidgetitem7 = tableWidgetTests->horizontalHeaderItem(7);
        ___qtablewidgetitem7->setText(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243B\346\216\245\346\224\266", nullptr));
        QTableWidgetItem *___qtablewidgetitem8 = tableWidgetTests->horizontalHeaderItem(8);
        ___qtablewidgetitem8->setText(QCoreApplication::translate("MainWindow", "\346\265\213\350\257\225\347\273\223\346\236\234", nullptr));
        pushButtonAddTest->setText(QCoreApplication::translate("MainWindow", "\346\267\273\345\212\240\346\265\213\350\257\225\347\224\250\344\276\213", nullptr));
        pushButtonRemoveTest->setText(QCoreApplication::translate("MainWindow", "\347\247\273\351\231\244\345\275\223\345\211\215\347\224\250\344\276\213", nullptr));
        pushButtonCopyTest->setText(QCoreApplication::translate("MainWindow", "\345\244\215\345\210\266\346\211\200\351\200\211\347\224\250\344\276\213", nullptr));
        pushButtonClearTests->setText(QCoreApplication::translate("MainWindow", "\346\270\205\347\251\272\347\224\250\344\276\213", nullptr));
        pushButtonImport->setText(QCoreApplication::translate("MainWindow", "\345\257\274\345\205\245\351\205\215\347\275\256", nullptr));
        pushButtonExport->setText(QCoreApplication::translate("MainWindow", "\345\257\274\345\207\272\351\205\215\347\275\256", nullptr));
        pushButtonGenerateReport->setText(QCoreApplication::translate("MainWindow", "\347\224\237\346\210\220\346\212\245\345\221\212", nullptr));
        labelDelay->setText(QCoreApplication::translate("MainWindow", "\346\265\213\350\257\225\345\273\266\346\227\266\357\274\232", nullptr));
        spinBoxDelay->setSuffix(QCoreApplication::translate("MainWindow", " ms", nullptr));
        checkBoxStopOnFailure->setText(QCoreApplication::translate("MainWindow", "\345\244\261\350\264\245\346\227\266\345\201\234\346\255\242", nullptr));
        checkBoxUseCRC16->setText(QCoreApplication::translate("MainWindow", "CRC16\346\240\241\351\252\214", nullptr));
        pushButtonTest->setText(QCoreApplication::translate("MainWindow", "\345\274\200\345\247\213\346\265\213\350\257\225", nullptr));
        modbusTestListGroupBox->setTitle(QCoreApplication::translate("MainWindow", "Modbus\346\265\213\350\257\225\345\210\227\350\241\250", nullptr));
        QTableWidgetItem *___qtablewidgetitem9 = tableWidgetModbusTests->horizontalHeaderItem(0);
        ___qtablewidgetitem9->setText(QCoreApplication::translate("MainWindow", "\345\272\217\345\217\267", nullptr));
        QTableWidgetItem *___qtablewidgetitem10 = tableWidgetModbusTests->horizontalHeaderItem(1);
        ___qtablewidgetitem10->setText(QCoreApplication::translate("MainWindow", "\346\265\213\350\257\225\346\217\217\350\277\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem11 = tableWidgetModbusTests->horizontalHeaderItem(2);
        ___qtablewidgetitem11->setText(QCoreApplication::translate("MainWindow", "\347\253\231\345\217\267", nullptr));
        QTableWidgetItem *___qtablewidgetitem12 = tableWidgetModbusTests->horizontalHeaderItem(3);
        ___qtablewidgetitem12->setText(QCoreApplication::translate("MainWindow", "\345\212\237\350\203\275\347\240\201", nullptr));
        QTableWidgetItem *___qtablewidgetitem13 = tableWidgetModbusTests->horizontalHeaderItem(4);
        ___qtablewidgetitem13->setText(QCoreApplication::translate("MainWindow", "\345\217\221\351\200\201\346\225\260\346\215\256", nullptr));
        QTableWidgetItem *___qtablewidgetitem14 = tableWidgetModbusTests->horizontalHeaderItem(5);
        ___qtablewidgetitem14->setText(QCoreApplication::translate("MainWindow", "\346\234\237\346\234\233\345\223\215\345\272\224", nullptr));
        QTableWidgetItem *___qtablewidgetitem15 = tableWidgetModbusTests->horizontalHeaderItem(6);
        ___qtablewidgetitem15->setText(QCoreApplication::translate("MainWindow", "\345\256\236\351\231\205\345\223\215\345\272\224", nullptr));
        QTableWidgetItem *___qtablewidgetitem16 = tableWidgetModbusTests->horizontalHeaderItem(7);
        ___qtablewidgetitem16->setText(QCoreApplication::translate("MainWindow", "\346\265\213\350\257\225\347\273\223\346\236\234", nullptr));
        pushButtonAddModbusTest->setText(QCoreApplication::translate("MainWindow", "\346\267\273\345\212\240\346\265\213\350\257\225\347\224\250\344\276\213", nullptr));
        pushButtonRemoveModbusTest->setText(QCoreApplication::translate("MainWindow", "\347\247\273\351\231\244\345\275\223\345\211\215\347\224\250\344\276\213", nullptr));
        pushButtonCopyModbusTest->setText(QCoreApplication::translate("MainWindow", "\345\244\215\345\210\266\346\211\200\351\200\211\347\224\250\344\276\213", nullptr));
        pushButtonClearModbusTests->setText(QCoreApplication::translate("MainWindow", "\346\270\205\347\251\272\347\224\250\344\276\213", nullptr));
        pushButtonImportModbus->setText(QCoreApplication::translate("MainWindow", "\345\257\274\345\205\245\351\205\215\347\275\256", nullptr));
        pushButtonExportModbus->setText(QCoreApplication::translate("MainWindow", "\345\257\274\345\207\272\351\205\215\347\275\256", nullptr));
        pushButtonGenerateModbusReport->setText(QCoreApplication::translate("MainWindow", "\347\224\237\346\210\220\346\212\245\345\221\212", nullptr));
        labelModbusTimeout->setText(QCoreApplication::translate("MainWindow", "\346\265\213\350\257\225\345\273\266\346\227\266\357\274\232", nullptr));
        spinBoxModbusTimeout->setSuffix(QCoreApplication::translate("MainWindow", " ms", nullptr));
        pushButtonModbusTest->setText(QCoreApplication::translate("MainWindow", "\345\274\200\345\247\213\346\265\213\350\257\225", nullptr));
        groupBoxSendA->setTitle(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243A\345\217\221\351\200\201\346\225\260\346\215\256", nullptr));
        lineEditSendDataA->setPlaceholderText(QCoreApplication::translate("MainWindow", "\350\257\267\350\276\223\345\205\245\345\215\201\345\205\255\350\277\233\345\210\266\346\225\260\346\215\256\357\274\214\344\276\213\345\246\202\357\274\23201 03 00 00 00 01", nullptr));
        pushButtonSendA->setText(QCoreApplication::translate("MainWindow", "\345\217\221\351\200\201", nullptr));
        groupBoxDisplayA->setTitle(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243A\346\225\260\346\215\256\346\230\276\347\244\272", nullptr));
        groupBoxSendB->setTitle(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243B\345\217\221\351\200\201\346\225\260\346\215\256", nullptr));
        lineEditSendDataB->setPlaceholderText(QCoreApplication::translate("MainWindow", "\350\257\267\350\276\223\345\205\245\345\215\201\345\205\255\350\277\233\345\210\266\346\225\260\346\215\256\357\274\214\344\276\213\345\246\202\357\274\23201 03 00 00 00 01", nullptr));
        pushButtonSendB->setText(QCoreApplication::translate("MainWindow", "\345\217\221\351\200\201", nullptr));
        groupBoxDisplayB->setTitle(QCoreApplication::translate("MainWindow", "\344\270\262\345\217\243B\346\225\260\346\215\256\346\230\276\347\244\272", nullptr));
        mainToolBar->setWindowTitle(QCoreApplication::translate("MainWindow", "\345\267\245\345\205\267\346\240\217", nullptr));
        menuFile->setTitle(QCoreApplication::translate("MainWindow", "\346\226\207\344\273\266", nullptr));
        menuTest->setTitle(QCoreApplication::translate("MainWindow", "\346\243\200\346\265\213", nullptr));
        menuConfig->setTitle(QCoreApplication::translate("MainWindow", "\351\205\215\347\275\256", nullptr));
        menuReport->setTitle(QCoreApplication::translate("MainWindow", "\346\212\245\350\241\250", nullptr));
        menuTools->setTitle(QCoreApplication::translate("MainWindow", "\345\267\245\345\205\267", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
