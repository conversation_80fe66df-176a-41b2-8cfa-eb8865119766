#ifndef SETVALUEDIALOG_H
#define SETVALUEDIALOG_H

#include <QDialog>
#include <QMainWindow>
#include <QTimer>
#include <QTableWidgetItem>
#include <QMenu>
#include <QAction>
#include <QContextMenuEvent>
#include <QDesktopServices>
#include <QUrl>
#include <QTemporaryFile>
#include <QPrinter>
#include <QTextDocument>
#include "serialmanager.h"
#include "crcutils.h"

namespace Ui {
class SetValueDialog;
}

// 定值解析项结构体
struct ParseItem {
    QString name;        // 名称(英文)
    QString description; // 描述(中文)
    QString dataType;    // 数据类型
    QString readDirection; // 读取方向
    int samplePeriod;    // 采集周期(毫秒)
    int deviceTypeId;    // 存储器类型序号
    int deviceAddress;   // 存储器地址
    QString parseMethod; // 解析方式
    int bitOffset;       // 位偏移量
    int bcdParse;        // 按BCD解析
    
    // 新增字段
    QString address;     // 地址
    QString processMethod; // 处理方式
    double minValue;     // 最小值
    double maxValue;     // 最大值
    QString unit;        // 单位
    double processParam; // 数据处理参数（除数，如10、100等）
    bool featureSelection; // 特性选择（新增复选框）
    bool enableProtection; // 投退选择（是否启用保护功能）
    QString protectionStatus; // 保护投退（根据报文自动填入）
    QString expectedData; // 期望数据（手动填入）
    QString expectedDataFromFile; // 期望数据(定值文件读取)（新增列）
    QString actualData;   // 实际数据（根据报文自动填入）
    QString compareResult; // 比对列（如果期望数据和实际数据一致，则✔，否则×）
};

class SetValueDialog : public QMainWindow
{
    Q_OBJECT

public:
    explicit SetValueDialog(SerialManager *serialManager, QWidget *parent = nullptr);
    ~SetValueDialog();

private:
    QSize originalSize;  // 保存原始窗口大小

private slots:
    // 串口相关槽函数
    void on_comboBoxPort_currentIndexChanged(int index);
    void on_comboBoxDeviceModel_currentIndexChanged(int index);
    void on_pushButtonSend_clicked();
    void on_pushButtonSelectValueFile_clicked();

    void on_checkBoxCRC16_stateChanged(int state);
    
    // 解析项相关槽函数
    void on_tableWidgetParse_itemDoubleClicked(QTableWidgetItem *item);
    void on_pushButtonGenerateReport_clicked();
    void on_pushButtonWriteValue_clicked();
    void on_pushButtonClearLog_clicked();
    
    // 右键菜单相关槽函数
    void showContextMenu(const QPoint &pos);
    void addParseItem();
    void editParseItem();
    void deleteParseItem();
    void deleteSelectedParseItems();
    void saveParseItems();
    void loadParseItems();
    
    // 串口消息处理
    void handleSerialMessageReceived(const QString &message);
    
    // 表格项变更处理
    void onTableItemChanged(QTableWidgetItem *item);
    
    // 延迟保存槽函数
    void performDelayedSave();

protected:
    // 事件过滤器
    bool eventFilter(QObject *obj, QEvent *event) override;

private:
    Ui::SetValueDialog *ui;
    SerialManager *m_serialManager; // 使用SerialManager
    QList<ParseItem> parseItems;
    QByteArray lastRegisterData;  // 存储最新读取的寄存器数据
    
    // 日志控制
    bool enableDebugLog = false;  // 控制调试日志输出
    bool enableUILog = false;     // 控制UI日志输出（保留关键信息）
    
    // 延迟保存机制
    QTimer *saveTimer;
    bool pendingSave = false;
    
    // 初始化UI
    void initUI();
    void updateParseTable();
    void updateSingleRow(int row);
    
    // 创建Modbus帧
    QByteArray createModbusFrame(int stationId, int functionCode, const QByteArray &data);
    
    // 解析响应数据
    void parseResponse(const QByteArray &response);
    void parseReadRegistersResponse(const QByteArray &response);
    
    // 写入定值相关函数
    void writeValueToDevice();
    void performWriteValueOperation();
    void writeInconsistentValuesToDevice(const QList<ParseItem> &inconsistentItems);
    QByteArray createWriteValueFrame(int stationId, int functionCode, const QString &address, const QList<quint16> &values);
    void parseReadCoilsResponse(const QByteArray &response);
    void parseWriteSingleResponse(const QByteArray &response);
    void parseWriteMultipleResponse(const QByteArray &response);
    void parseDataValues(const QByteArray &data);
    
    // 保存和加载解析项
    bool saveParseItemsToFile(const QString &filePath);
    bool loadParseItemsFromFile(const QString &filePath);
    
    // 设备型号配置相关函数
    void configureDeviceParameters(const QString &deviceModel);
    bool loadDeviceConfigFile(const QString &deviceModel);
    
    // 生成报告相关函数
    QString generateHtmlReport();
    bool saveReportToHtml(const QString &htmlContent, const QString &filePath);
    bool saveReportToPdf(const QString &htmlContent, const QString &filePath);
};

#endif // SETVALUEDIALOG_H
