<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SetValueDialog</class>
 <widget class="QMainWindow" name="SetValueDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>定值召唤</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QGroupBox" name="groupBoxConfig">
     <property name="title">
      <string>通信配置</string>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>60</height>
      </size>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_config">
      <property name="spacing">
       <number>15</number>
      </property>
      <property name="leftMargin">
       <number>10</number>
      </property>
      <property name="topMargin">
       <number>10</number>
      </property>
      <property name="rightMargin">
       <number>10</number>
      </property>
      <property name="bottomMargin">
       <number>10</number>
      </property>
      <item>
       <widget class="QLabel" name="labelPort">
        <property name="text">
         <string>通信串口：</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QComboBox" name="comboBoxPort">
        <property name="minimumSize">
         <size>
          <width>80</width>
          <height>0</height>
         </size>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="labelDeviceModel">
        <property name="text">
         <string>设备型号：</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QComboBox" name="comboBoxDeviceModel">
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>0</height>
         </size>
        </property>
        <item>
         <property name="text">
          <string>无设备</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>LL510P</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>LM510P</string>
         </property>
        </item>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButtonSelectValueFile">
        <property name="text">
         <string>定值文件选择</string>
        </property>
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>0</height>
         </size>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="labelDeviceAddress">
        <property name="text">
         <string>设备地址：</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QSpinBox" name="spinBoxDeviceAddress">
        <property name="minimum">
         <number>1</number>
        </property>
        <property name="maximum">
         <number>255</number>
        </property>
        <property name="minimumSize">
         <size>
          <width>60</width>
          <height>0</height>
         </size>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="labelFunctionCode">
        <property name="text">
         <string>功能码：</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QComboBox" name="comboBoxFunctionCode">
        <property name="minimumSize">
         <size>
          <width>120</width>
          <height>0</height>
         </size>
        </property>
        <item>
         <property name="text">
          <string>01 - 读线圈</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>02 - 读离散输入</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>03 - 读保持寄存器</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>04 - 读输入寄存器</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>05 - 写单个线圈</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>06 - 写单个寄存器</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>15 - 写多个线圈</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>16 - 写多个寄存器</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="labelDataAddress">
        <property name="text">
         <string>数据地址：</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QLineEdit" name="lineEditDataAddress">
        <property name="text">
         <string>00</string>
        </property>
        <property name="placeholderText">
         <string>十六进制地址</string>
        </property>
       </widget>
      </item>
      <item row="1" column="2">
       <widget class="QLabel" name="labelDataCount">
        <property name="text">
         <string>数据个数：</string>
        </property>
       </widget>
      </item>
      <item row="1" column="3">
       <widget class="QSpinBox" name="spinBoxDataCount">
        <property name="minimum">
         <number>1</number>
        </property>
        <property name="maximum">
         <number>125</number>
        </property>
        <property name="value">
         <number>30</number>
        </property>
       </widget>
      </item>
      <item row="1" column="4">
       <widget class="QLabel" name="labelDelay">
        <property name="text">
         <string>测试延时(ms)：</string>
        </property>
       </widget>
      </item>
      <item row="1" column="5">
       <widget class="QSpinBox" name="spinBoxDelay">
        <property name="minimum">
         <number>100</number>
        </property>
        <property name="maximum">
         <number>10000</number>
        </property>
        <property name="singleStep">
         <number>100</number>
        </property>
        <property name="value">
         <number>1000</number>
        </property>
       </widget>
      </item>
      <item row="3" column="0" colspan="2">
       <widget class="QCheckBox" name="checkBoxCRC16">
        <property name="text">
         <string>CRC16校验</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="3" column="2" colspan="4">
       <layout class="QHBoxLayout" name="horizontalLayout">
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="pushButtonSend">
          <property name="text">
           <string>发送</string>
          </property>
          <property name="icon">
           <iconset resource="resources.qrc">
            <normaloff>:/images/images/send.png</normaloff>:/images/images/send.png</iconset>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="pushButtonGenerateReport">
          <property name="text">
           <string>生成报告</string>
          </property>
          <property name="icon">
           <iconset resource="resources.qrc">
            <normaloff>:/images/images/view.svg</normaloff>:/images/images/view.svg</iconset>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="pushButtonWriteValue">
          <property name="text">
           <string>写入定值</string>
          </property>
          <property name="icon">
           <iconset resource="resources.qrc">
            <normaloff>:/icons/setvalue.svg</normaloff>:/icons/setvalue.svg</iconset>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="checkBoxHideParseConfig">
          <property name="text">
           <string>隐藏</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QSplitter" name="splitter">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <widget class="QGroupBox" name="groupBoxParse">
      <property name="title">
       <string>报文解析配置</string>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QTableWidget" name="tableWidgetParse"/>
       </item>
       <item>
        <widget class="QLabel" name="labelParseHint">
         <property name="text">
          <string>提示：右键点击表格可添加/编辑/删除解析项，双击表格项可编辑</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QGroupBox" name="groupBoxLog">
      <property name="title">
       <string>通信日志</string>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <item>
        <widget class="QTextBrowser" name="textBrowserLog"/>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayoutLog">
         <item>
          <spacer name="horizontalSpacerLog">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonClearLog">
           <property name="text">
            <string>清空日志</string>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/images/images/close.png</normaloff>:/images/images/close.png</iconset>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 </widget>
 <resources>
  <include location="resources.qrc"/>
 </resources>
 <connections/>
</ui>