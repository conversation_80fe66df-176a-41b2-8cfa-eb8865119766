#include "serialmanager.h"
#include <QSerialPortInfo>

SerialManager::SerialManager(QObject *parent) : QObject(parent)
{
    // Create and setup both serial ports
    auto* serialA = new ModbusSerialA(this);
    auto* serialB = new ModbusSerialB(this);

    m_serialPorts.insert("A", serialA);
    m_serialPorts.insert("B", serialB);

    setupSerialPort("A", serialA);
    setupSerialPort("B", serialB);
}

SerialManager::~SerialManager()
{
    // Qt's parent-child mechanism will handle deletion of m_serialPorts values
}

void SerialManager::setupSerialPort(const QString &portId, ModbusSerialBase *serial)
{
    connect(serial, &ModbusSerialBase::connectionStateChanged, this, [this, portId](bool connected) {
        emit connectionStateChanged(portId, connected);
    });

    connect(serial, &ModbusSerialBase::errorOccurred, this, [this, portId](const QString &error) {
        emit errorOccurred(portId, error);
    });

    connect(serial, &ModbusSerialBase::communicationMessageReceived, this, [this, portId](const QString &message) {
        emit dataReceived(portId, message);
    });

    connect(serial, &ModbusSerialBase::communicationMessageSent, this, [this, portId](const QString &message) {
        emit dataSent(portId, message);
    });
}

bool SerialManager::connectPort(const QString &portId, const QString &portName, int baudRate,
                                const QString &parityText, int dataBitsValue, const QString &stopBitsText)
{
    if (!m_serialPorts.contains(portId)) {
        emit errorOccurred(portId, "Invalid port identifier.");
        return false;
    }

    ModbusSerialBase* serial = m_serialPorts.value(portId);

    if (portName.isEmpty()) {
        emit errorOccurred(portId, "Serial port name cannot be empty.");
        return false;
    }

    QSerialPort::DataBits dataBits = static_cast<QSerialPort::DataBits>(dataBitsValue);
    QSerialPort::StopBits stopBits;
    if (stopBitsText == "1") stopBits = QSerialPort::OneStop;
    else if (stopBitsText == "1.5") stopBits = QSerialPort::OneAndHalfStop;
    else stopBits = QSerialPort::TwoStop;

    QSerialPort::Parity parity;
    if (parityText == "None") parity = QSerialPort::NoParity;
    else if (parityText == "Odd") parity = QSerialPort::OddParity;
    else if (parityText == "Even") parity = QSerialPort::EvenParity;
    else if (parityText == "Mark") parity = QSerialPort::MarkParity;
    else parity = QSerialPort::SpaceParity;

    return serial->connectDevice(portName, baudRate, parity, dataBits, stopBits);
}

void SerialManager::disconnectPort(const QString &portId)
{
    if (m_serialPorts.contains(portId)) {
        m_serialPorts.value(portId)->disconnectDevice();
    }
}

bool SerialManager::sendData(const QString &portId, const QByteArray &data)
{
    if (m_serialPorts.contains(portId) && m_serialPorts.value(portId)->isConnected()) {
        return m_serialPorts.value(portId)->sendRawData(data);
    }
    emit errorOccurred(portId, "Port is not connected or invalid.");
    return false;
}

bool SerialManager::isConnected(const QString &portId) const
{
    return m_serialPorts.contains(portId) && m_serialPorts.value(portId)->isConnected();
}

QStringList SerialManager::getAvailablePorts()
{
    QStringList portNames;
    const auto ports = QSerialPortInfo::availablePorts();
    for (const QSerialPortInfo &info : ports) {
        portNames.append(info.portName());
    }
    return portNames;
}
