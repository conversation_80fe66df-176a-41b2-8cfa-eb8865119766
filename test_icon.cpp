#include <QApplication>
#include <QMainWindow>
#include <QIcon>
#include <QDebug>

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 测试图标是否能正确加载
    QIcon icon(":/images/images/app_icon.svg");
    qDebug() << "Icon null:" << icon.isNull();
    qDebug() << "Icon available sizes:" << icon.availableSizes();
    
    QMainWindow window;
    window.setWindowIcon(icon);
    window.setWindowTitle("Icon Test");
    window.show();
    
    return app.exec();
}
