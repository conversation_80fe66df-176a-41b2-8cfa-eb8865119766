/****************************************************************************
** Meta object code from reading C++ file 'mainwindow.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.8.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../mainwindow.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'mainwindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.8.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN10MainWindowE_t {};
} // unnamed namespace


#ifdef QT_MOC_HAS_STRINGDATA
static constexpr auto qt_meta_stringdata_ZN10MainWindowE = QtMocHelpers::stringData(
    "MainWindow",
    "on_actionExit_triggered",
    "",
    "on_actionConnectA_triggered",
    "on_actionConnectB_triggered",
    "on_actionDisconnectA_triggered",
    "on_actionDisconnectB_triggered",
    "on_actionRefresh_triggered",
    "on_actionMapping_triggered",
    "on_actionSetValue_triggered",
    "on_actionImportConfig_triggered",
    "on_actionExportConfig_triggered",
    "on_pushButtonReadA_clicked",
    "on_pushButtonWriteA_clicked",
    "on_pushButtonReadB_clicked",
    "on_pushButtonWriteB_clicked",
    "on_checkBoxAutoA_clicked",
    "checked",
    "on_checkBoxAutoB_clicked",
    "on_pushButtonSendA_clicked",
    "on_pushButtonSendB_clicked",
    "on_pushButtonAddTest_clicked",
    "on_pushButtonRemoveTest_clicked",
    "on_pushButtonClearTests_clicked",
    "on_pushButtonImport_clicked",
    "on_pushButtonExport_clicked",
    "importTestCasesFromArray",
    "array",
    "importTestStepsFromMappingWindow",
    "testStepsArray",
    "on_pushButtonCopyTest_clicked",
    "on_tableWidgetTests_itemSelectionChanged",
    "on_tableWidgetTests_itemChanged",
    "QTableWidgetItem*",
    "item",
    "on_lineEditDescription_textChanged",
    "text",
    "on_lineEditSentA_textChanged",
    "on_lineEditHopeReceiveA_textChanged",
    "on_spinBoxStationA_valueChanged",
    "value",
    "on_pushButtonTest_clicked",
    "on_checkBoxUseCRC16_toggled",
    "handleSerialMessage",
    "portId",
    "message",
    "handleSerialError",
    "error",
    "handleSerialConnectionChanged",
    "connected",
    "updateTestCaseTable"
);
#else  // !QT_MOC_HAS_STRINGDATA
#error "qtmochelpers.h not found or too old."
#endif // !QT_MOC_HAS_STRINGDATA

Q_CONSTINIT static const uint qt_meta_data_ZN10MainWindowE[] = {

 // content:
      12,       // revision
       0,       // classname
       0,    0, // classinfo
      38,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,  242,    2, 0x08,    1 /* Private */,
       3,    0,  243,    2, 0x08,    2 /* Private */,
       4,    0,  244,    2, 0x08,    3 /* Private */,
       5,    0,  245,    2, 0x08,    4 /* Private */,
       6,    0,  246,    2, 0x08,    5 /* Private */,
       7,    0,  247,    2, 0x08,    6 /* Private */,
       8,    0,  248,    2, 0x08,    7 /* Private */,
       9,    0,  249,    2, 0x08,    8 /* Private */,
      10,    0,  250,    2, 0x08,    9 /* Private */,
      11,    0,  251,    2, 0x08,   10 /* Private */,
      12,    0,  252,    2, 0x08,   11 /* Private */,
      13,    0,  253,    2, 0x08,   12 /* Private */,
      14,    0,  254,    2, 0x08,   13 /* Private */,
      15,    0,  255,    2, 0x08,   14 /* Private */,
      16,    1,  256,    2, 0x08,   15 /* Private */,
      18,    1,  259,    2, 0x08,   17 /* Private */,
      19,    0,  262,    2, 0x08,   19 /* Private */,
      20,    0,  263,    2, 0x08,   20 /* Private */,
      21,    0,  264,    2, 0x08,   21 /* Private */,
      22,    0,  265,    2, 0x08,   22 /* Private */,
      23,    0,  266,    2, 0x08,   23 /* Private */,
      24,    0,  267,    2, 0x08,   24 /* Private */,
      25,    0,  268,    2, 0x08,   25 /* Private */,
      26,    1,  269,    2, 0x08,   26 /* Private */,
      28,    1,  272,    2, 0x08,   28 /* Private */,
      30,    0,  275,    2, 0x08,   30 /* Private */,
      31,    0,  276,    2, 0x08,   31 /* Private */,
      32,    1,  277,    2, 0x08,   32 /* Private */,
      35,    1,  280,    2, 0x08,   34 /* Private */,
      37,    1,  283,    2, 0x08,   36 /* Private */,
      38,    1,  286,    2, 0x08,   38 /* Private */,
      39,    1,  289,    2, 0x08,   40 /* Private */,
      41,    0,  292,    2, 0x08,   42 /* Private */,
      42,    1,  293,    2, 0x08,   43 /* Private */,
      43,    2,  296,    2, 0x08,   45 /* Private */,
      46,    2,  301,    2, 0x08,   48 /* Private */,
      48,    2,  306,    2, 0x08,   51 /* Private */,
      50,    0,  311,    2, 0x08,   54 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,   17,
    QMetaType::Void, QMetaType::Bool,   17,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QJsonArray,   27,
    QMetaType::Void, QMetaType::QJsonArray,   29,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 33,   34,
    QMetaType::Void, QMetaType::QString,   36,
    QMetaType::Void, QMetaType::QString,   36,
    QMetaType::Void, QMetaType::QString,   36,
    QMetaType::Void, QMetaType::Int,   40,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,   17,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   44,   45,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   44,   47,
    QMetaType::Void, QMetaType::QString, QMetaType::Bool,   44,   49,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject MainWindow::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_ZN10MainWindowE.offsetsAndSizes,
    qt_meta_data_ZN10MainWindowE,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_tag_ZN10MainWindowE_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<MainWindow, std::true_type>,
        // method 'on_actionExit_triggered'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_actionConnectA_triggered'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_actionConnectB_triggered'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_actionDisconnectA_triggered'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_actionDisconnectB_triggered'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_actionRefresh_triggered'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_actionMapping_triggered'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_actionSetValue_triggered'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_actionImportConfig_triggered'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_actionExportConfig_triggered'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_pushButtonReadA_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_pushButtonWriteA_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_pushButtonReadB_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_pushButtonWriteB_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_checkBoxAutoA_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'on_checkBoxAutoB_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'on_pushButtonSendA_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_pushButtonSendB_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_pushButtonAddTest_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_pushButtonRemoveTest_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_pushButtonClearTests_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_pushButtonImport_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_pushButtonExport_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'importTestCasesFromArray'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QJsonArray &, std::false_type>,
        // method 'importTestStepsFromMappingWindow'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QJsonArray &, std::false_type>,
        // method 'on_pushButtonCopyTest_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_tableWidgetTests_itemSelectionChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_tableWidgetTests_itemChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QTableWidgetItem *, std::false_type>,
        // method 'on_lineEditDescription_textChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'on_lineEditSentA_textChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'on_lineEditHopeReceiveA_textChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'on_spinBoxStationA_valueChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'on_pushButtonTest_clicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'on_checkBoxUseCRC16_toggled'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'handleSerialMessage'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'handleSerialError'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'handleSerialConnectionChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'updateTestCaseTable'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void MainWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<MainWindow *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->on_actionExit_triggered(); break;
        case 1: _t->on_actionConnectA_triggered(); break;
        case 2: _t->on_actionConnectB_triggered(); break;
        case 3: _t->on_actionDisconnectA_triggered(); break;
        case 4: _t->on_actionDisconnectB_triggered(); break;
        case 5: _t->on_actionRefresh_triggered(); break;
        case 6: _t->on_actionMapping_triggered(); break;
        case 7: _t->on_actionSetValue_triggered(); break;
        case 8: _t->on_actionImportConfig_triggered(); break;
        case 9: _t->on_actionExportConfig_triggered(); break;
        case 10: _t->on_pushButtonReadA_clicked(); break;
        case 11: _t->on_pushButtonWriteA_clicked(); break;
        case 12: _t->on_pushButtonReadB_clicked(); break;
        case 13: _t->on_pushButtonWriteB_clicked(); break;
        case 14: _t->on_checkBoxAutoA_clicked((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 15: _t->on_checkBoxAutoB_clicked((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 16: _t->on_pushButtonSendA_clicked(); break;
        case 17: _t->on_pushButtonSendB_clicked(); break;
        case 18: _t->on_pushButtonAddTest_clicked(); break;
        case 19: _t->on_pushButtonRemoveTest_clicked(); break;
        case 20: _t->on_pushButtonClearTests_clicked(); break;
        case 21: _t->on_pushButtonImport_clicked(); break;
        case 22: _t->on_pushButtonExport_clicked(); break;
        case 23: _t->importTestCasesFromArray((*reinterpret_cast< std::add_pointer_t<QJsonArray>>(_a[1]))); break;
        case 24: _t->importTestStepsFromMappingWindow((*reinterpret_cast< std::add_pointer_t<QJsonArray>>(_a[1]))); break;
        case 25: _t->on_pushButtonCopyTest_clicked(); break;
        case 26: _t->on_tableWidgetTests_itemSelectionChanged(); break;
        case 27: _t->on_tableWidgetTests_itemChanged((*reinterpret_cast< std::add_pointer_t<QTableWidgetItem*>>(_a[1]))); break;
        case 28: _t->on_lineEditDescription_textChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 29: _t->on_lineEditSentA_textChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 30: _t->on_lineEditHopeReceiveA_textChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 31: _t->on_spinBoxStationA_valueChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 32: _t->on_pushButtonTest_clicked(); break;
        case 33: _t->on_checkBoxUseCRC16_toggled((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 34: _t->handleSerialMessage((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 35: _t->handleSerialError((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 36: _t->handleSerialConnectionChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 37: _t->updateTestCaseTable(); break;
        default: ;
        }
    }
}

const QMetaObject *MainWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MainWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ZN10MainWindowE.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int MainWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 38)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 38;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 38)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 38;
    }
    return _id;
}
QT_WARNING_POP
