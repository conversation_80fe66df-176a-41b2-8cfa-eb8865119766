<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MappingWindow</class>
 <widget class="QMainWindow" name="MappingWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>900</width>
    <height>700</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>主控软件与控制器/保护装置映射配置工具</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="mainWindowLayout">
   <item>
    <layout class="QHBoxLayout" name="deviceSelectionLayout">
     <item>
      <widget class="QLabel" name="serialPortLabel">
       <property name="text">
        <string>主控软件:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="serialPortComboBox">
       <item>
        <property name="text">
         <string>主控软件(COMA)</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>主控软件(COMB)</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer1">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="deviceLabel">
       <property name="text">
        <string>设备:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="deviceComboBox"/>
     </item>
     <item>
      <spacer name="horizontalSpacer2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="addButton">
       <property name="text">
        <string>添加映射</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QSplitter" name="mainSplitter">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <widget class="QWidget" name="mappingWidget" native="true">
      <layout class="QVBoxLayout" name="mappingLayout">
       <item>
        <layout class="QHBoxLayout" name="titleLayout">
         <item>
          <widget class="QLabel" name="mappingTitleLabel">
           <property name="font">
            <font>
             <pointsize>10</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>映射配置</string>
           </property>
           <property name="maximumHeight">
            <number>25</number>
           </property>
           <property name="sizePolicy">
            <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="titleSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="testStepsTitleLabel">
           <property name="font">
            <font>
             <pointsize>10</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>测试流程</string>
           </property>
           <property name="maximumHeight">
            <number>25</number>
           </property>
           <property name="sizePolicy">
            <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QSplitter" name="displaySplitter">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <widget class="QListWidget" name="mappingList">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
         </widget>
         <widget class="QListWidget" name="testStepsList">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="flowchartWidget" native="true">
      <layout class="QVBoxLayout" name="flowchartLayout">
       <item>
        <widget class="QLabel" name="flowchartTitleLabel">
         <!-- 删除流程图标题label -->
        </widget>
       </item>
       <item>
        <widget class="QGraphicsView" name="view">
         <property name="renderHints">
          <set>QPainter::Antialiasing</set>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="logWidget" native="true">
      <layout class="QVBoxLayout" name="logLayout">
       <item>
        <layout class="QHBoxLayout" name="logTitleLayout">
         <item>
          <widget class="QLabel" name="logTitleLabel">
           <property name="font">
            <font>
             <pointsize>10</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>通信日志</string>
           </property>
           <property name="maximumHeight">
            <number>25</number>
           </property>
           <property name="sizePolicy">
            <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="logTitleSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="clearLogButton">
           <property name="text">
            <string>清空日志</string>
           </property>
           <property name="maximumWidth">
            <number>80</number>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QTextEdit" name="logTextEdit">
         <property name="maximumHeight">
          <number>150</number>
         </property>
         <property name="readOnly">
          <bool>true</bool>
         </property>
         <property name="font">
          <font>
           <family>Consolas</family>
           <pointsize>9</pointsize>
          </font>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="buttonLayout">
     <item>
      <widget class="QPushButton" name="saveButton">
       <property name="text">
        <string>保存配置</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="loadButton">
       <property name="text">
        <string>加载配置</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="undoButton">
       <property name="text">
        <string>撤销</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="redoButton">
       <property name="text">
        <string>重做</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="editButton">
       <property name="text">
        <string>编辑步骤</string>
       </property>
      </widget>
     </item>

     <item>
      <widget class="QPushButton" name="detailsButton">
       <property name="text">
        <string>查看详情</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="testButtonSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="testButton">
       <property name="text">
        <string>开始测试</string>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #45a049;
}
QPushButton:pressed {
    background-color: #3d8b40;
}</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="testReportButton">
       <property name="text">
        <string>测试报告</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>