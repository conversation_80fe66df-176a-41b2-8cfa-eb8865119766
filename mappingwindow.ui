<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MappingWindow</class>
 <widget class="QMainWindow" name="MappingWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>900</width>
    <height>700</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>主控软件与控制器/保护装置映射配置工具</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="mainWindowLayout">
    <property name="spacing">
     <number>2</number>
    </property>
    <property name="leftMargin">
     <number>3</number>
    </property>
    <property name="topMargin">
     <number>3</number>
    </property>
    <property name="rightMargin">
     <number>3</number>
    </property>
    <property name="bottomMargin">
     <number>3</number>
    </property>
   <item>
    <widget class="QGroupBox" name="deviceConfigGroupBox">
     <property name="title">
      <string>设备配置</string>
     </property>
     <property name="styleSheet">
      <string>QGroupBox {
    font-weight: bold;
    border: 1px solid #cccccc;
    border-radius: 3px;
    margin-top: 0.5ex;
    padding-top: 2px;
}
QGroupBox::title {
    subcontrol-origin: margin;
    left: 8px;
    padding: 0 3px 0 3px;
}</string>
     </property>
     <layout class="QVBoxLayout" name="deviceConfigLayout">
      <property name="spacing">
       <number>3</number>
      </property>
      <property name="leftMargin">
       <number>6</number>
      </property>
      <property name="topMargin">
       <number>2</number>
      </property>
      <property name="rightMargin">
       <number>6</number>
      </property>
      <property name="bottomMargin">
       <number>3</number>
      </property>
      <item>
       <layout class="QHBoxLayout" name="deviceSelectionLayout">
        <property name="spacing">
         <number>8</number>
        </property>
        <item>
         <widget class="QLabel" name="serialPortLabel">
          <property name="text">
           <string>主控软件:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="serialPortComboBox">
          <item>
           <property name="text">
            <string>主控软件(COMA)</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>主控软件(COMB)</string>
           </property>
          </item>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer1">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="deviceLabel">
          <property name="text">
           <string>设备:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="deviceComboBox"/>
        </item>
        <item>
         <spacer name="horizontalSpacer2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="testDelayLabel">
          <property name="text">
           <string>测试延时(ms):</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QSpinBox" name="testDelaySpinBox">
          <property name="minimum">
           <number>100</number>
          </property>
          <property name="maximum">
           <number>10000</number>
          </property>
          <property name="singleStep">
           <number>100</number>
          </property>
          <property name="value">
           <number>500</number>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QCheckBox" name="globalCrc16CheckBox">
          <property name="text">
           <string>CRC16校验</string>
          </property>
          <property name="toolTip">
           <string>选中后，映射配置流程中的每一个流程都会在发送数据后追加CRC16校验</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer4">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QCheckBox" name="hideInterfaceCheckBox">
          <property name="text">
           <string>隐藏</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
          <property name="toolTip">
           <string>隐藏映射配置、测试流程和通信日志界面，使流程图显示最大化</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer5">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="addButton">
          <property name="text">
           <string>添加映射</string>
          </property>
          <property name="styleSheet">
           <string>QPushButton {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #45a049;
}
QPushButton:pressed {
    background-color: #3d8b40;
}</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QSplitter" name="mainSplitter">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <widget class="QWidget" name="mappingWidget" native="true">
      <layout class="QVBoxLayout" name="mappingLayout">
       <property name="spacing">
        <number>5</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QSplitter" name="displaySplitter">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <widget class="QGroupBox" name="mappingGroupBox">
          <property name="title">
           <string>映射配置</string>
          </property>
          <property name="styleSheet">
           <string>QGroupBox {
    font-weight: bold;
    border: 2px solid #cccccc;
    border-radius: 5px;
    margin-top: 1ex;
    padding-top: 10px;
}
QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
}</string>
          </property>
          <layout class="QVBoxLayout" name="mappingGroupLayout">
           <item>
            <widget class="QListWidget" name="mappingList">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="styleSheet">
              <string>QListWidget {
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fafafa;
}
QListWidget::item {
    padding: 5px;
    border-bottom: 1px solid #eee;
}
QListWidget::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QGroupBox" name="testStepsGroupBox">
          <property name="title">
           <string>测试流程</string>
          </property>
          <property name="styleSheet">
           <string>QGroupBox {
    font-weight: bold;
    border: 2px solid #cccccc;
    border-radius: 5px;
    margin-top: 1ex;
    padding-top: 10px;
}
QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
}</string>
          </property>
          <layout class="QVBoxLayout" name="testStepsGroupLayout">
           <item>
            <widget class="QListWidget" name="testStepsList">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="styleSheet">
              <string>QListWidget {
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fafafa;
}
QListWidget::item {
    padding: 5px;
    border-bottom: 1px solid #eee;
}
QListWidget::item:selected {
    background-color: #fff3e0;
    color: #f57c00;
}</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QGroupBox" name="flowchartGroupBox">
      <property name="title">
       <string>流程图显示</string>
      </property>
      <property name="styleSheet">
       <string>QGroupBox {
    font-weight: bold;
    border: 1px solid #cccccc;
    border-radius: 3px;
    margin-top: 0.5ex;
    padding-top: 2px;
}
QGroupBox::title {
    subcontrol-origin: margin;
    left: 8px;
    padding: 0 3px 0 3px;
}</string>
      </property>
      <layout class="QVBoxLayout" name="flowchartLayout">
       <property name="spacing">
        <number>2</number>
       </property>
       <property name="leftMargin">
        <number>3</number>
       </property>
       <property name="topMargin">
        <number>2</number>
       </property>
       <property name="rightMargin">
        <number>3</number>
       </property>
       <property name="bottomMargin">
        <number>2</number>
       </property>
       <item>
        <widget class="QGraphicsView" name="view">
         <property name="renderHints">
          <set>QPainter::Antialiasing</set>
         </property>
         <property name="styleSheet">
          <string>QGraphicsView {
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
}</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QGroupBox" name="logGroupBox">
      <property name="title">
       <string>通信日志</string>
      </property>
      <property name="styleSheet">
       <string>QGroupBox {
    font-weight: bold;
    border: 2px solid #cccccc;
    border-radius: 5px;
    margin-top: 1ex;
    padding-top: 10px;
}
QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
}</string>
      </property>
      <layout class="QVBoxLayout" name="logLayout">
       <item>
        <layout class="QHBoxLayout" name="logButtonLayout">
         <property name="sizeConstraint">
          <enum>QLayout::SetFixedSize</enum>
         </property>
         <item>
          <spacer name="logButtonSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>10</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="clearLogButton">
           <property name="text">
            <string>清空日志</string>
           </property>
           <property name="maximumWidth">
            <number>80</number>
           </property>
           <property name="maximumHeight">
            <number>25</number>
           </property>
           <property name="styleSheet">
            <string>QPushButton {
    background-color: #ff9800;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 11px;
}
QPushButton:hover {
    background-color: #f57c00;
}
QPushButton:pressed {
    background-color: #ef6c00;
}</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QTextEdit" name="logTextEdit">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="readOnly">
          <bool>true</bool>
         </property>
         <property name="font">
          <font>
           <family>Consolas</family>
           <pointsize>9</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string>QTextEdit {
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f8f9fa;
    padding: 5px;
}</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="buttonFrame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <property name="styleSheet">
      <string>QFrame {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 5px;
}</string>
     </property>
     <layout class="QHBoxLayout" name="buttonLayout">
      <property name="spacing">
       <number>6</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>3</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QPushButton" name="saveButton">
        <property name="text">
         <string>保存配置</string>
        </property>
        <property name="styleSheet">
         <string>QPushButton {
    background-color: #2196F3;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #1976D2;
}
QPushButton:pressed {
    background-color: #1565C0;
}</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="loadButton">
        <property name="text">
         <string>加载配置</string>
        </property>
        <property name="styleSheet">
         <string>QPushButton {
    background-color: #2196F3;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #1976D2;
}
QPushButton:pressed {
    background-color: #1565C0;
}</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QFrame" name="separatorFrame1">
        <property name="frameShape">
         <enum>QFrame::VLine</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Sunken</enum>
        </property>
        <property name="styleSheet">
         <string>QFrame {
    color: #ccc;
}</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="undoButton">
        <property name="text">
         <string>撤销</string>
        </property>
        <property name="styleSheet">
         <string>QPushButton {
    background-color: #9E9E9E;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #757575;
}
QPushButton:pressed {
    background-color: #616161;
}</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="redoButton">
        <property name="text">
         <string>重做</string>
        </property>
        <property name="styleSheet">
         <string>QPushButton {
    background-color: #9E9E9E;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #757575;
}
QPushButton:pressed {
    background-color: #616161;
}</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="resetButton">
        <property name="text">
         <string>复位</string>
        </property>
        <property name="styleSheet">
         <string>QPushButton {
    background-color: #607D8B;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #455A64;
}
QPushButton:pressed {
    background-color: #37474F;
}</string>
        </property>
        <property name="toolTip">
         <string>复位到映射配置刚添加时的状态，清除所有测试结果</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QFrame" name="separatorFrame2">
        <property name="frameShape">
         <enum>QFrame::VLine</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Sunken</enum>
        </property>
        <property name="styleSheet">
         <string>QFrame {
    color: #ccc;
}</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="editButton">
        <property name="text">
         <string>编辑步骤</string>
        </property>
        <property name="styleSheet">
         <string>QPushButton {
    background-color: #FF9800;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #F57C00;
}
QPushButton:pressed {
    background-color: #EF6C00;
}</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="detailsButton">
        <property name="text">
         <string>查看详情</string>
        </property>
        <property name="styleSheet">
         <string>QPushButton {
    background-color: #FF9800;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #F57C00;
}
QPushButton:pressed {
    background-color: #EF6C00;
}</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="testButtonSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="testButton">
        <property name="text">
         <string>开始测试</string>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 12px;
}
QPushButton:hover {
    background-color: #45a049;
}
QPushButton:pressed {
    background-color: #3d8b40;
}</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="testReportButton">
        <property name="text">
         <string>测试报告</string>
        </property>
        <property name="styleSheet">
         <string>QPushButton {
    background-color: #673AB7;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #5E35B1;
}
QPushButton:pressed {
    background-color: #512DA8;
}</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="saveReportButton">
        <property name="text">
         <string>保存报告</string>
        </property>
        <property name="styleSheet">
         <string>QPushButton {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #45a049;
}
QPushButton:pressed {
    background-color: #3d8b40;
}</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="exportReportButton">
        <property name="text">
         <string>导出报告</string>
        </property>
        <property name="styleSheet">
         <string>QPushButton {
    background-color: #FF9800;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #F57C00;
}
QPushButton:pressed {
    background-color: #E65100;
}</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>