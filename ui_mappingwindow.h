/********************************************************************************
** Form generated from reading UI file 'mappingwindow.ui'
**
** Created by: Qt User Interface Compiler version 6.8.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAPPINGWINDOW_H
#define UI_MAPPINGWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGraphicsView>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QListWidget>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QSplitter>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MappingWindow
{
public:
    QWidget *centralwidget;
    QVBoxLayout *mainWindowLayout;
    QGroupBox *deviceConfigGroupBox;
    QVBoxLayout *deviceConfigLayout;
    QHBoxLayout *deviceSelectionLayout;
    QLabel *serialPortLabel;
    QComboBox *serialPortComboBox;
    QSpacerItem *horizontalSpacer1;
    QLabel *deviceLabel;
    QComboBox *deviceComboBox;
    QSpacerItem *horizontalSpacer2;
    QLabel *testDelayLabel;
    QSpinBox *testDelaySpinBox;
    QSpacerItem *horizontalSpacer3;
    QCheckBox *globalCrc16CheckBox;
    QSpacerItem *horizontalSpacer4;
    QCheckBox *hideInterfaceCheckBox;
    QSpacerItem *horizontalSpacer5;
    QPushButton *addButton;
    QSplitter *mainSplitter;
    QWidget *mappingWidget;
    QVBoxLayout *mappingLayout;
    QSplitter *displaySplitter;
    QGroupBox *mappingGroupBox;
    QVBoxLayout *mappingGroupLayout;
    QListWidget *mappingList;
    QGroupBox *testStepsGroupBox;
    QVBoxLayout *testStepsGroupLayout;
    QListWidget *testStepsList;
    QGroupBox *flowchartGroupBox;
    QVBoxLayout *flowchartLayout;
    QGraphicsView *view;
    QGroupBox *logGroupBox;
    QVBoxLayout *logLayout;
    QHBoxLayout *logButtonLayout;
    QSpacerItem *logButtonSpacer;
    QPushButton *clearLogButton;
    QTextEdit *logTextEdit;
    QFrame *buttonFrame;
    QHBoxLayout *buttonLayout;
    QPushButton *saveButton;
    QPushButton *loadButton;
    QFrame *separatorFrame1;
    QPushButton *undoButton;
    QPushButton *redoButton;
    QPushButton *resetButton;
    QFrame *separatorFrame2;
    QPushButton *editButton;
    QPushButton *detailsButton;
    QSpacerItem *testButtonSpacer;
    QPushButton *testButton;
    QPushButton *testReportButton;
    QPushButton *saveReportButton;
    QPushButton *exportReportButton;

    void setupUi(QMainWindow *MappingWindow)
    {
        if (MappingWindow->objectName().isEmpty())
            MappingWindow->setObjectName("MappingWindow");
        MappingWindow->resize(900, 700);
        centralwidget = new QWidget(MappingWindow);
        centralwidget->setObjectName("centralwidget");
        mainWindowLayout = new QVBoxLayout(centralwidget);
        mainWindowLayout->setSpacing(2);
        mainWindowLayout->setObjectName("mainWindowLayout");
        mainWindowLayout->setContentsMargins(3, 3, 3, 3);
        deviceConfigGroupBox = new QGroupBox(centralwidget);
        deviceConfigGroupBox->setObjectName("deviceConfigGroupBox");
        deviceConfigLayout = new QVBoxLayout(deviceConfigGroupBox);
        deviceConfigLayout->setSpacing(3);
        deviceConfigLayout->setObjectName("deviceConfigLayout");
        deviceConfigLayout->setContentsMargins(6, 2, 6, 3);
        deviceSelectionLayout = new QHBoxLayout();
        deviceSelectionLayout->setSpacing(8);
        deviceSelectionLayout->setObjectName("deviceSelectionLayout");
        serialPortLabel = new QLabel(deviceConfigGroupBox);
        serialPortLabel->setObjectName("serialPortLabel");

        deviceSelectionLayout->addWidget(serialPortLabel);

        serialPortComboBox = new QComboBox(deviceConfigGroupBox);
        serialPortComboBox->addItem(QString());
        serialPortComboBox->addItem(QString());
        serialPortComboBox->setObjectName("serialPortComboBox");

        deviceSelectionLayout->addWidget(serialPortComboBox);

        horizontalSpacer1 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        deviceSelectionLayout->addItem(horizontalSpacer1);

        deviceLabel = new QLabel(deviceConfigGroupBox);
        deviceLabel->setObjectName("deviceLabel");

        deviceSelectionLayout->addWidget(deviceLabel);

        deviceComboBox = new QComboBox(deviceConfigGroupBox);
        deviceComboBox->setObjectName("deviceComboBox");

        deviceSelectionLayout->addWidget(deviceComboBox);

        horizontalSpacer2 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        deviceSelectionLayout->addItem(horizontalSpacer2);

        testDelayLabel = new QLabel(deviceConfigGroupBox);
        testDelayLabel->setObjectName("testDelayLabel");

        deviceSelectionLayout->addWidget(testDelayLabel);

        testDelaySpinBox = new QSpinBox(deviceConfigGroupBox);
        testDelaySpinBox->setObjectName("testDelaySpinBox");
        testDelaySpinBox->setMinimum(100);
        testDelaySpinBox->setMaximum(10000);
        testDelaySpinBox->setSingleStep(100);
        testDelaySpinBox->setValue(500);

        deviceSelectionLayout->addWidget(testDelaySpinBox);

        horizontalSpacer3 = new QSpacerItem(20, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        deviceSelectionLayout->addItem(horizontalSpacer3);

        globalCrc16CheckBox = new QCheckBox(deviceConfigGroupBox);
        globalCrc16CheckBox->setObjectName("globalCrc16CheckBox");

        deviceSelectionLayout->addWidget(globalCrc16CheckBox);

        horizontalSpacer4 = new QSpacerItem(20, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        deviceSelectionLayout->addItem(horizontalSpacer4);

        hideInterfaceCheckBox = new QCheckBox(deviceConfigGroupBox);
        hideInterfaceCheckBox->setObjectName("hideInterfaceCheckBox");
        hideInterfaceCheckBox->setChecked(true);

        deviceSelectionLayout->addWidget(hideInterfaceCheckBox);

        horizontalSpacer5 = new QSpacerItem(20, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        deviceSelectionLayout->addItem(horizontalSpacer5);

        addButton = new QPushButton(deviceConfigGroupBox);
        addButton->setObjectName("addButton");

        deviceSelectionLayout->addWidget(addButton);


        deviceConfigLayout->addLayout(deviceSelectionLayout);


        mainWindowLayout->addWidget(deviceConfigGroupBox);

        mainSplitter = new QSplitter(centralwidget);
        mainSplitter->setObjectName("mainSplitter");
        mainSplitter->setOrientation(Qt::Vertical);
        mainSplitter->setChildrenCollapsible(true);
        mappingWidget = new QWidget(mainSplitter);
        mappingWidget->setObjectName("mappingWidget");
        mappingLayout = new QVBoxLayout(mappingWidget);
        mappingLayout->setSpacing(1);
        mappingLayout->setObjectName("mappingLayout");
        mappingLayout->setContentsMargins(0, 0, 0, 0);
        displaySplitter = new QSplitter(mappingWidget);
        displaySplitter->setObjectName("displaySplitter");
        displaySplitter->setOrientation(Qt::Horizontal);
        mappingGroupBox = new QGroupBox(displaySplitter);
        mappingGroupBox->setObjectName("mappingGroupBox");
        mappingGroupLayout = new QVBoxLayout(mappingGroupBox);
        mappingGroupLayout->setObjectName("mappingGroupLayout");
        mappingList = new QListWidget(mappingGroupBox);
        mappingList->setObjectName("mappingList");
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(mappingList->sizePolicy().hasHeightForWidth());
        mappingList->setSizePolicy(sizePolicy);

        mappingGroupLayout->addWidget(mappingList);

        displaySplitter->addWidget(mappingGroupBox);
        testStepsGroupBox = new QGroupBox(displaySplitter);
        testStepsGroupBox->setObjectName("testStepsGroupBox");
        testStepsGroupLayout = new QVBoxLayout(testStepsGroupBox);
        testStepsGroupLayout->setObjectName("testStepsGroupLayout");
        testStepsList = new QListWidget(testStepsGroupBox);
        testStepsList->setObjectName("testStepsList");
        sizePolicy.setHeightForWidth(testStepsList->sizePolicy().hasHeightForWidth());
        testStepsList->setSizePolicy(sizePolicy);

        testStepsGroupLayout->addWidget(testStepsList);

        displaySplitter->addWidget(testStepsGroupBox);

        mappingLayout->addWidget(displaySplitter);

        mainSplitter->addWidget(mappingWidget);
        flowchartGroupBox = new QGroupBox(mainSplitter);
        flowchartGroupBox->setObjectName("flowchartGroupBox");
        flowchartLayout = new QVBoxLayout(flowchartGroupBox);
        flowchartLayout->setSpacing(2);
        flowchartLayout->setObjectName("flowchartLayout");
        flowchartLayout->setContentsMargins(3, 2, 3, 2);
        view = new QGraphicsView(flowchartGroupBox);
        view->setObjectName("view");
        view->setRenderHints(QPainter::Antialiasing);

        flowchartLayout->addWidget(view);

        mainSplitter->addWidget(flowchartGroupBox);
        logGroupBox = new QGroupBox(mainSplitter);
        logGroupBox->setObjectName("logGroupBox");
        logLayout = new QVBoxLayout(logGroupBox);
        logLayout->setObjectName("logLayout");
        logButtonLayout = new QHBoxLayout();
        logButtonLayout->setObjectName("logButtonLayout");
        logButtonLayout->setSizeConstraint(QLayout::SetFixedSize);
        logButtonSpacer = new QSpacerItem(40, 10, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        logButtonLayout->addItem(logButtonSpacer);

        clearLogButton = new QPushButton(logGroupBox);
        clearLogButton->setObjectName("clearLogButton");
        clearLogButton->setMaximumWidth(80);
        clearLogButton->setMaximumHeight(25);

        logButtonLayout->addWidget(clearLogButton);


        logLayout->addLayout(logButtonLayout);

        logTextEdit = new QTextEdit(logGroupBox);
        logTextEdit->setObjectName("logTextEdit");
        sizePolicy.setHeightForWidth(logTextEdit->sizePolicy().hasHeightForWidth());
        logTextEdit->setSizePolicy(sizePolicy);
        logTextEdit->setReadOnly(true);
        QFont font;
        font.setFamilies({QString::fromUtf8("Consolas")});
        font.setPointSize(9);
        logTextEdit->setFont(font);

        logLayout->addWidget(logTextEdit);

        mainSplitter->addWidget(logGroupBox);

        mainWindowLayout->addWidget(mainSplitter);

        buttonFrame = new QFrame(centralwidget);
        buttonFrame->setObjectName("buttonFrame");
        buttonFrame->setFrameShape(QFrame::StyledPanel);
        buttonFrame->setFrameShadow(QFrame::Raised);
        buttonLayout = new QHBoxLayout(buttonFrame);
        buttonLayout->setSpacing(6);
        buttonLayout->setObjectName("buttonLayout");
        buttonLayout->setContentsMargins(0, 3, 0, 0);
        saveButton = new QPushButton(buttonFrame);
        saveButton->setObjectName("saveButton");

        buttonLayout->addWidget(saveButton);

        loadButton = new QPushButton(buttonFrame);
        loadButton->setObjectName("loadButton");

        buttonLayout->addWidget(loadButton);

        separatorFrame1 = new QFrame(buttonFrame);
        separatorFrame1->setObjectName("separatorFrame1");
        separatorFrame1->setFrameShape(QFrame::VLine);
        separatorFrame1->setFrameShadow(QFrame::Sunken);

        buttonLayout->addWidget(separatorFrame1);

        undoButton = new QPushButton(buttonFrame);
        undoButton->setObjectName("undoButton");

        buttonLayout->addWidget(undoButton);

        redoButton = new QPushButton(buttonFrame);
        redoButton->setObjectName("redoButton");

        buttonLayout->addWidget(redoButton);

        resetButton = new QPushButton(buttonFrame);
        resetButton->setObjectName("resetButton");

        buttonLayout->addWidget(resetButton);

        separatorFrame2 = new QFrame(buttonFrame);
        separatorFrame2->setObjectName("separatorFrame2");
        separatorFrame2->setFrameShape(QFrame::VLine);
        separatorFrame2->setFrameShadow(QFrame::Sunken);

        buttonLayout->addWidget(separatorFrame2);

        editButton = new QPushButton(buttonFrame);
        editButton->setObjectName("editButton");

        buttonLayout->addWidget(editButton);

        detailsButton = new QPushButton(buttonFrame);
        detailsButton->setObjectName("detailsButton");

        buttonLayout->addWidget(detailsButton);

        testButtonSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        buttonLayout->addItem(testButtonSpacer);

        testButton = new QPushButton(buttonFrame);
        testButton->setObjectName("testButton");
        testButton->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    background-color: #4CAF50;\n"
"    color: white;\n"
"    border: none;\n"
"    padding: 8px 16px;\n"
"    border-radius: 4px;\n"
"    font-weight: bold;\n"
"    font-size: 12px;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #45a049;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #3d8b40;\n"
"}"));

        buttonLayout->addWidget(testButton);

        testReportButton = new QPushButton(buttonFrame);
        testReportButton->setObjectName("testReportButton");

        buttonLayout->addWidget(testReportButton);

        saveReportButton = new QPushButton(buttonFrame);
        saveReportButton->setObjectName("saveReportButton");

        buttonLayout->addWidget(saveReportButton);

        exportReportButton = new QPushButton(buttonFrame);
        exportReportButton->setObjectName("exportReportButton");

        buttonLayout->addWidget(exportReportButton);


        mainWindowLayout->addWidget(buttonFrame);

        MappingWindow->setCentralWidget(centralwidget);

        retranslateUi(MappingWindow);

        QMetaObject::connectSlotsByName(MappingWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MappingWindow)
    {
        MappingWindow->setWindowTitle(QCoreApplication::translate("MappingWindow", "\344\270\273\346\216\247\350\275\257\344\273\266\344\270\216\346\216\247\345\210\266\345\231\250/\344\277\235\346\212\244\350\243\205\347\275\256\346\230\240\345\260\204\351\205\215\347\275\256\345\267\245\345\205\267", nullptr));
        deviceConfigGroupBox->setTitle(QCoreApplication::translate("MappingWindow", "\350\256\276\345\244\207\351\205\215\347\275\256", nullptr));
        deviceConfigGroupBox->setStyleSheet(QCoreApplication::translate("MappingWindow", "QGroupBox {\n"
"    font-weight: bold;\n"
"    border: 1px solid #cccccc;\n"
"    border-radius: 3px;\n"
"    margin-top: 0.5ex;\n"
"    padding-top: 2px;\n"
"}\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 8px;\n"
"    padding: 0 3px 0 3px;\n"
"}", nullptr));
        serialPortLabel->setText(QCoreApplication::translate("MappingWindow", "\344\270\273\346\216\247\350\275\257\344\273\266:", nullptr));
        serialPortComboBox->setItemText(0, QCoreApplication::translate("MappingWindow", "\344\270\273\346\216\247\350\275\257\344\273\266(COMA)", nullptr));
        serialPortComboBox->setItemText(1, QCoreApplication::translate("MappingWindow", "\344\270\273\346\216\247\350\275\257\344\273\266(COMB)", nullptr));

        deviceLabel->setText(QCoreApplication::translate("MappingWindow", "\350\256\276\345\244\207:", nullptr));
        testDelayLabel->setText(QCoreApplication::translate("MappingWindow", "\346\265\213\350\257\225\345\273\266\346\227\266(ms):", nullptr));
        globalCrc16CheckBox->setText(QCoreApplication::translate("MappingWindow", "CRC16\346\240\241\351\252\214", nullptr));
#if QT_CONFIG(tooltip)
        globalCrc16CheckBox->setToolTip(QCoreApplication::translate("MappingWindow", "\351\200\211\344\270\255\345\220\216\357\274\214\346\230\240\345\260\204\351\205\215\347\275\256\346\265\201\347\250\213\344\270\255\347\232\204\346\257\217\344\270\200\344\270\252\346\265\201\347\250\213\351\203\275\344\274\232\345\234\250\345\217\221\351\200\201\346\225\260\346\215\256\345\220\216\350\277\275\345\212\240CRC16\346\240\241\351\252\214", nullptr));
#endif // QT_CONFIG(tooltip)
        hideInterfaceCheckBox->setText(QCoreApplication::translate("MappingWindow", "\351\232\220\350\227\217", nullptr));
#if QT_CONFIG(tooltip)
        hideInterfaceCheckBox->setToolTip(QCoreApplication::translate("MappingWindow", "\351\232\220\350\227\217\346\230\240\345\260\204\351\205\215\347\275\256\343\200\201\346\265\213\350\257\225\346\265\201\347\250\213\345\222\214\351\200\232\344\277\241\346\227\245\345\277\227\347\225\214\351\235\242\357\274\214\344\275\277\346\265\201\347\250\213\345\233\276\346\230\276\347\244\272\346\234\200\345\244\247\345\214\226", nullptr));
#endif // QT_CONFIG(tooltip)
        addButton->setText(QCoreApplication::translate("MappingWindow", "\346\267\273\345\212\240\346\230\240\345\260\204", nullptr));
        addButton->setStyleSheet(QCoreApplication::translate("MappingWindow", "QPushButton {\n"
"    background-color: #4CAF50;\n"
"    color: white;\n"
"    border: none;\n"
"    padding: 8px 16px;\n"
"    border-radius: 4px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #45a049;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #3d8b40;\n"
"}", nullptr));
        mappingGroupBox->setTitle(QCoreApplication::translate("MappingWindow", "\346\230\240\345\260\204\351\205\215\347\275\256", nullptr));
        mappingGroupBox->setStyleSheet(QCoreApplication::translate("MappingWindow", "QGroupBox {\n"
"    font-weight: bold;\n"
"    border: 2px solid #cccccc;\n"
"    border-radius: 5px;\n"
"    margin-top: 1ex;\n"
"    padding-top: 10px;\n"
"}\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 5px 0 5px;\n"
"}", nullptr));
        mappingList->setStyleSheet(QCoreApplication::translate("MappingWindow", "QListWidget {\n"
"    border: 1px solid #ddd;\n"
"    border-radius: 4px;\n"
"    background-color: #fafafa;\n"
"}\n"
"QListWidget::item {\n"
"    padding: 5px;\n"
"    border-bottom: 1px solid #eee;\n"
"}\n"
"QListWidget::item:selected {\n"
"    background-color: #e3f2fd;\n"
"    color: #1976d2;\n"
"}", nullptr));
        testStepsGroupBox->setTitle(QCoreApplication::translate("MappingWindow", "\346\265\213\350\257\225\346\265\201\347\250\213", nullptr));
        testStepsGroupBox->setStyleSheet(QCoreApplication::translate("MappingWindow", "QGroupBox {\n"
"    font-weight: bold;\n"
"    border: 2px solid #cccccc;\n"
"    border-radius: 5px;\n"
"    margin-top: 1ex;\n"
"    padding-top: 10px;\n"
"}\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 5px 0 5px;\n"
"}", nullptr));
        testStepsList->setStyleSheet(QCoreApplication::translate("MappingWindow", "QListWidget {\n"
"    border: 1px solid #ddd;\n"
"    border-radius: 4px;\n"
"    background-color: #fafafa;\n"
"}\n"
"QListWidget::item {\n"
"    padding: 5px;\n"
"    border-bottom: 1px solid #eee;\n"
"}\n"
"QListWidget::item:selected {\n"
"    background-color: #fff3e0;\n"
"    color: #f57c00;\n"
"}", nullptr));
        flowchartGroupBox->setTitle(QCoreApplication::translate("MappingWindow", "\346\265\201\347\250\213\345\233\276\346\230\276\347\244\272", nullptr));
        flowchartGroupBox->setStyleSheet(QCoreApplication::translate("MappingWindow", "QGroupBox {\n"
"    font-weight: bold;\n"
"    border: 1px solid #cccccc;\n"
"    border-radius: 3px;\n"
"    margin-top: 0.5ex;\n"
"    padding-top: 2px;\n"
"}\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 8px;\n"
"    padding: 0 3px 0 3px;\n"
"}", nullptr));
        view->setStyleSheet(QCoreApplication::translate("MappingWindow", "QGraphicsView {\n"
"    border: 1px solid #ddd;\n"
"    border-radius: 4px;\n"
"    background-color: white;\n"
"}", nullptr));
        logGroupBox->setTitle(QCoreApplication::translate("MappingWindow", "\351\200\232\344\277\241\346\227\245\345\277\227", nullptr));
        logGroupBox->setStyleSheet(QCoreApplication::translate("MappingWindow", "QGroupBox {\n"
"    font-weight: bold;\n"
"    border: 2px solid #cccccc;\n"
"    border-radius: 5px;\n"
"    margin-top: 1ex;\n"
"    padding-top: 10px;\n"
"}\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 5px 0 5px;\n"
"}", nullptr));
        clearLogButton->setText(QCoreApplication::translate("MappingWindow", "\346\270\205\347\251\272\346\227\245\345\277\227", nullptr));
        clearLogButton->setStyleSheet(QCoreApplication::translate("MappingWindow", "QPushButton {\n"
"    background-color: #ff9800;\n"
"    color: white;\n"
"    border: none;\n"
"    padding: 4px 8px;\n"
"    border-radius: 4px;\n"
"    font-weight: bold;\n"
"    font-size: 11px;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #f57c00;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #ef6c00;\n"
"}", nullptr));
        logTextEdit->setStyleSheet(QCoreApplication::translate("MappingWindow", "QTextEdit {\n"
"    border: 1px solid #ddd;\n"
"    border-radius: 4px;\n"
"    background-color: #f8f9fa;\n"
"    padding: 5px;\n"
"}", nullptr));
        buttonFrame->setStyleSheet(QCoreApplication::translate("MappingWindow", "QFrame {\n"
"    background-color: #f5f5f5;\n"
"    border: 1px solid #ddd;\n"
"    border-radius: 5px;\n"
"    padding: 5px;\n"
"}", nullptr));
        saveButton->setText(QCoreApplication::translate("MappingWindow", "\344\277\235\345\255\230\351\205\215\347\275\256", nullptr));
        saveButton->setStyleSheet(QCoreApplication::translate("MappingWindow", "QPushButton {\n"
"    background-color: #2196F3;\n"
"    color: white;\n"
"    border: none;\n"
"    padding: 8px 16px;\n"
"    border-radius: 4px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #1976D2;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #1565C0;\n"
"}", nullptr));
        loadButton->setText(QCoreApplication::translate("MappingWindow", "\345\212\240\350\275\275\351\205\215\347\275\256", nullptr));
        loadButton->setStyleSheet(QCoreApplication::translate("MappingWindow", "QPushButton {\n"
"    background-color: #2196F3;\n"
"    color: white;\n"
"    border: none;\n"
"    padding: 8px 16px;\n"
"    border-radius: 4px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #1976D2;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #1565C0;\n"
"}", nullptr));
        separatorFrame1->setStyleSheet(QCoreApplication::translate("MappingWindow", "QFrame {\n"
"    color: #ccc;\n"
"}", nullptr));
        undoButton->setText(QCoreApplication::translate("MappingWindow", "\346\222\244\351\224\200", nullptr));
        undoButton->setStyleSheet(QCoreApplication::translate("MappingWindow", "QPushButton {\n"
"    background-color: #9E9E9E;\n"
"    color: white;\n"
"    border: none;\n"
"    padding: 8px 16px;\n"
"    border-radius: 4px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #757575;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #616161;\n"
"}", nullptr));
        redoButton->setText(QCoreApplication::translate("MappingWindow", "\351\207\215\345\201\232", nullptr));
        redoButton->setStyleSheet(QCoreApplication::translate("MappingWindow", "QPushButton {\n"
"    background-color: #9E9E9E;\n"
"    color: white;\n"
"    border: none;\n"
"    padding: 8px 16px;\n"
"    border-radius: 4px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #757575;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #616161;\n"
"}", nullptr));
        resetButton->setText(QCoreApplication::translate("MappingWindow", "\345\244\215\344\275\215", nullptr));
        resetButton->setStyleSheet(QCoreApplication::translate("MappingWindow", "QPushButton {\n"
"    background-color: #607D8B;\n"
"    color: white;\n"
"    border: none;\n"
"    padding: 8px 16px;\n"
"    border-radius: 4px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #455A64;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #37474F;\n"
"}", nullptr));
#if QT_CONFIG(tooltip)
        resetButton->setToolTip(QCoreApplication::translate("MappingWindow", "\345\244\215\344\275\215\345\210\260\346\230\240\345\260\204\351\205\215\347\275\256\345\210\232\346\267\273\345\212\240\346\227\266\347\232\204\347\212\266\346\200\201\357\274\214\346\270\205\351\231\244\346\211\200\346\234\211\346\265\213\350\257\225\347\273\223\346\236\234", nullptr));
#endif // QT_CONFIG(tooltip)
        separatorFrame2->setStyleSheet(QCoreApplication::translate("MappingWindow", "QFrame {\n"
"    color: #ccc;\n"
"}", nullptr));
        editButton->setText(QCoreApplication::translate("MappingWindow", "\347\274\226\350\276\221\346\255\245\351\252\244", nullptr));
        editButton->setStyleSheet(QCoreApplication::translate("MappingWindow", "QPushButton {\n"
"    background-color: #FF9800;\n"
"    color: white;\n"
"    border: none;\n"
"    padding: 8px 16px;\n"
"    border-radius: 4px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #F57C00;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #EF6C00;\n"
"}", nullptr));
        detailsButton->setText(QCoreApplication::translate("MappingWindow", "\346\237\245\347\234\213\350\257\246\346\203\205", nullptr));
        detailsButton->setStyleSheet(QCoreApplication::translate("MappingWindow", "QPushButton {\n"
"    background-color: #FF9800;\n"
"    color: white;\n"
"    border: none;\n"
"    padding: 8px 16px;\n"
"    border-radius: 4px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #F57C00;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #EF6C00;\n"
"}", nullptr));
        testButton->setText(QCoreApplication::translate("MappingWindow", "\345\274\200\345\247\213\346\265\213\350\257\225", nullptr));
        testReportButton->setText(QCoreApplication::translate("MappingWindow", "\346\265\213\350\257\225\346\212\245\345\221\212", nullptr));
        testReportButton->setStyleSheet(QCoreApplication::translate("MappingWindow", "QPushButton {\n"
"    background-color: #673AB7;\n"
"    color: white;\n"
"    border: none;\n"
"    padding: 8px 16px;\n"
"    border-radius: 4px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #5E35B1;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #512DA8;\n"
"}", nullptr));
        saveReportButton->setText(QCoreApplication::translate("MappingWindow", "\344\277\235\345\255\230\346\212\245\345\221\212", nullptr));
        saveReportButton->setStyleSheet(QCoreApplication::translate("MappingWindow", "QPushButton {\n"
"    background-color: #4CAF50;\n"
"    color: white;\n"
"    border: none;\n"
"    padding: 8px 16px;\n"
"    border-radius: 4px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #45a049;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #3d8b40;\n"
"}", nullptr));
        exportReportButton->setText(QCoreApplication::translate("MappingWindow", "\345\257\274\345\207\272\346\212\245\345\221\212", nullptr));
        exportReportButton->setStyleSheet(QCoreApplication::translate("MappingWindow", "QPushButton {\n"
"    background-color: #FF9800;\n"
"    color: white;\n"
"    border: none;\n"
"    padding: 8px 16px;\n"
"    border-radius: 4px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #F57C00;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #E65100;\n"
"}", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MappingWindow: public Ui_MappingWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAPPINGWINDOW_H
