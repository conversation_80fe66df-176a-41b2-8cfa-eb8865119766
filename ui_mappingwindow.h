/********************************************************************************
** Form generated from reading UI file 'mappingwindow.ui'
**
** Created by: Qt User Interface Compiler version 6.8.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAPPINGWINDOW_H
#define UI_MAPPINGWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGraphicsView>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QListWidget>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSplitter>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MappingWindow
{
public:
    QWidget *centralwidget;
    QVBoxLayout *mainWindowLayout;
    QHBoxLayout *deviceSelectionLayout;
    QLabel *serialPortLabel;
    QComboBox *serialPortComboBox;
    QSpacerItem *horizontalSpacer1;
    QLabel *deviceLabel;
    QComboBox *deviceComboBox;
    QSpacerItem *horizontalSpacer2;
    QPushButton *addButton;
    QSplitter *mainSplitter;
    QWidget *mappingWidget;
    QVBoxLayout *mappingLayout;
    QHBoxLayout *titleLayout;
    QLabel *mappingTitleLabel;
    QSpacerItem *titleSpacer;
    QLabel *testStepsTitleLabel;
    QSplitter *displaySplitter;
    QListWidget *mappingList;
    QListWidget *testStepsList;
    QWidget *flowchartWidget;
    QVBoxLayout *flowchartLayout;
    QLabel *flowchartTitleLabel;
    QGraphicsView *view;
    QWidget *logWidget;
    QVBoxLayout *logLayout;
    QHBoxLayout *logTitleLayout;
    QLabel *logTitleLabel;
    QSpacerItem *logTitleSpacer;
    QPushButton *clearLogButton;
    QTextEdit *logTextEdit;
    QHBoxLayout *buttonLayout;
    QPushButton *saveButton;
    QPushButton *loadButton;
    QPushButton *undoButton;
    QPushButton *redoButton;
    QPushButton *editButton;
    QPushButton *detailsButton;
    QSpacerItem *testButtonSpacer;
    QPushButton *testButton;
    QPushButton *testReportButton;

    void setupUi(QMainWindow *MappingWindow)
    {
        if (MappingWindow->objectName().isEmpty())
            MappingWindow->setObjectName("MappingWindow");
        MappingWindow->resize(900, 700);
        centralwidget = new QWidget(MappingWindow);
        centralwidget->setObjectName("centralwidget");
        mainWindowLayout = new QVBoxLayout(centralwidget);
        mainWindowLayout->setObjectName("mainWindowLayout");
        deviceSelectionLayout = new QHBoxLayout();
        deviceSelectionLayout->setObjectName("deviceSelectionLayout");
        serialPortLabel = new QLabel(centralwidget);
        serialPortLabel->setObjectName("serialPortLabel");

        deviceSelectionLayout->addWidget(serialPortLabel);

        serialPortComboBox = new QComboBox(centralwidget);
        serialPortComboBox->addItem(QString());
        serialPortComboBox->addItem(QString());
        serialPortComboBox->setObjectName("serialPortComboBox");

        deviceSelectionLayout->addWidget(serialPortComboBox);

        horizontalSpacer1 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        deviceSelectionLayout->addItem(horizontalSpacer1);

        deviceLabel = new QLabel(centralwidget);
        deviceLabel->setObjectName("deviceLabel");

        deviceSelectionLayout->addWidget(deviceLabel);

        deviceComboBox = new QComboBox(centralwidget);
        deviceComboBox->setObjectName("deviceComboBox");

        deviceSelectionLayout->addWidget(deviceComboBox);

        horizontalSpacer2 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        deviceSelectionLayout->addItem(horizontalSpacer2);

        addButton = new QPushButton(centralwidget);
        addButton->setObjectName("addButton");

        deviceSelectionLayout->addWidget(addButton);


        mainWindowLayout->addLayout(deviceSelectionLayout);

        mainSplitter = new QSplitter(centralwidget);
        mainSplitter->setObjectName("mainSplitter");
        mainSplitter->setOrientation(Qt::Vertical);
        mappingWidget = new QWidget(mainSplitter);
        mappingWidget->setObjectName("mappingWidget");
        mappingLayout = new QVBoxLayout(mappingWidget);
        mappingLayout->setObjectName("mappingLayout");
        titleLayout = new QHBoxLayout();
        titleLayout->setObjectName("titleLayout");
        mappingTitleLabel = new QLabel(mappingWidget);
        mappingTitleLabel->setObjectName("mappingTitleLabel");
        QFont font;
        font.setPointSize(10);
        font.setBold(true);
        mappingTitleLabel->setFont(font);
        mappingTitleLabel->setMaximumHeight(25);
        QSizePolicy sizePolicy(QSizePolicy::Policy::Fixed, QSizePolicy::Policy::Fixed);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(mappingTitleLabel->sizePolicy().hasHeightForWidth());
        mappingTitleLabel->setSizePolicy(sizePolicy);

        titleLayout->addWidget(mappingTitleLabel);

        titleSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        titleLayout->addItem(titleSpacer);

        testStepsTitleLabel = new QLabel(mappingWidget);
        testStepsTitleLabel->setObjectName("testStepsTitleLabel");
        testStepsTitleLabel->setFont(font);
        testStepsTitleLabel->setMaximumHeight(25);
        sizePolicy.setHeightForWidth(testStepsTitleLabel->sizePolicy().hasHeightForWidth());
        testStepsTitleLabel->setSizePolicy(sizePolicy);
        testStepsTitleLabel->setAlignment(Qt::AlignRight|Qt::AlignVCenter);

        titleLayout->addWidget(testStepsTitleLabel);


        mappingLayout->addLayout(titleLayout);

        displaySplitter = new QSplitter(mappingWidget);
        displaySplitter->setObjectName("displaySplitter");
        displaySplitter->setOrientation(Qt::Horizontal);
        mappingList = new QListWidget(displaySplitter);
        mappingList->setObjectName("mappingList");
        QSizePolicy sizePolicy1(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy1.setHorizontalStretch(0);
        sizePolicy1.setVerticalStretch(0);
        sizePolicy1.setHeightForWidth(mappingList->sizePolicy().hasHeightForWidth());
        mappingList->setSizePolicy(sizePolicy1);
        displaySplitter->addWidget(mappingList);
        testStepsList = new QListWidget(displaySplitter);
        testStepsList->setObjectName("testStepsList");
        sizePolicy1.setHeightForWidth(testStepsList->sizePolicy().hasHeightForWidth());
        testStepsList->setSizePolicy(sizePolicy1);
        displaySplitter->addWidget(testStepsList);

        mappingLayout->addWidget(displaySplitter);

        mainSplitter->addWidget(mappingWidget);
        flowchartWidget = new QWidget(mainSplitter);
        flowchartWidget->setObjectName("flowchartWidget");
        flowchartLayout = new QVBoxLayout(flowchartWidget);
        flowchartLayout->setObjectName("flowchartLayout");
        flowchartTitleLabel = new QLabel(flowchartWidget);
        flowchartTitleLabel->setObjectName("flowchartTitleLabel");

        flowchartLayout->addWidget(flowchartTitleLabel);

        view = new QGraphicsView(flowchartWidget);
        view->setObjectName("view");
        view->setRenderHints(QPainter::Antialiasing);

        flowchartLayout->addWidget(view);

        mainSplitter->addWidget(flowchartWidget);
        logWidget = new QWidget(mainSplitter);
        logWidget->setObjectName("logWidget");
        logLayout = new QVBoxLayout(logWidget);
        logLayout->setObjectName("logLayout");
        logTitleLayout = new QHBoxLayout();
        logTitleLayout->setObjectName("logTitleLayout");
        logTitleLabel = new QLabel(logWidget);
        logTitleLabel->setObjectName("logTitleLabel");
        logTitleLabel->setFont(font);
        logTitleLabel->setMaximumHeight(25);
        sizePolicy.setHeightForWidth(logTitleLabel->sizePolicy().hasHeightForWidth());
        logTitleLabel->setSizePolicy(sizePolicy);

        logTitleLayout->addWidget(logTitleLabel);

        logTitleSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        logTitleLayout->addItem(logTitleSpacer);

        clearLogButton = new QPushButton(logWidget);
        clearLogButton->setObjectName("clearLogButton");
        clearLogButton->setMaximumWidth(80);

        logTitleLayout->addWidget(clearLogButton);


        logLayout->addLayout(logTitleLayout);

        logTextEdit = new QTextEdit(logWidget);
        logTextEdit->setObjectName("logTextEdit");
        logTextEdit->setMaximumHeight(150);
        logTextEdit->setReadOnly(true);
        QFont font1;
        font1.setFamilies({QString::fromUtf8("Consolas")});
        font1.setPointSize(9);
        logTextEdit->setFont(font1);

        logLayout->addWidget(logTextEdit);

        mainSplitter->addWidget(logWidget);

        mainWindowLayout->addWidget(mainSplitter);

        buttonLayout = new QHBoxLayout();
        buttonLayout->setObjectName("buttonLayout");
        saveButton = new QPushButton(centralwidget);
        saveButton->setObjectName("saveButton");

        buttonLayout->addWidget(saveButton);

        loadButton = new QPushButton(centralwidget);
        loadButton->setObjectName("loadButton");

        buttonLayout->addWidget(loadButton);

        undoButton = new QPushButton(centralwidget);
        undoButton->setObjectName("undoButton");

        buttonLayout->addWidget(undoButton);

        redoButton = new QPushButton(centralwidget);
        redoButton->setObjectName("redoButton");

        buttonLayout->addWidget(redoButton);

        editButton = new QPushButton(centralwidget);
        editButton->setObjectName("editButton");

        buttonLayout->addWidget(editButton);

        detailsButton = new QPushButton(centralwidget);
        detailsButton->setObjectName("detailsButton");

        buttonLayout->addWidget(detailsButton);

        testButtonSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        buttonLayout->addItem(testButtonSpacer);

        testButton = new QPushButton(centralwidget);
        testButton->setObjectName("testButton");
        testButton->setStyleSheet(QString::fromUtf8("QPushButton {\n"
"    background-color: #4CAF50;\n"
"    color: white;\n"
"    border: none;\n"
"    padding: 8px 16px;\n"
"    border-radius: 4px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #45a049;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #3d8b40;\n"
"}"));

        buttonLayout->addWidget(testButton);

        testReportButton = new QPushButton(centralwidget);
        testReportButton->setObjectName("testReportButton");

        buttonLayout->addWidget(testReportButton);


        mainWindowLayout->addLayout(buttonLayout);

        MappingWindow->setCentralWidget(centralwidget);

        retranslateUi(MappingWindow);

        QMetaObject::connectSlotsByName(MappingWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MappingWindow)
    {
        MappingWindow->setWindowTitle(QCoreApplication::translate("MappingWindow", "\344\270\273\346\216\247\350\275\257\344\273\266\344\270\216\346\216\247\345\210\266\345\231\250/\344\277\235\346\212\244\350\243\205\347\275\256\346\230\240\345\260\204\351\205\215\347\275\256\345\267\245\345\205\267", nullptr));
        serialPortLabel->setText(QCoreApplication::translate("MappingWindow", "\344\270\273\346\216\247\350\275\257\344\273\266:", nullptr));
        serialPortComboBox->setItemText(0, QCoreApplication::translate("MappingWindow", "\344\270\273\346\216\247\350\275\257\344\273\266(COMA)", nullptr));
        serialPortComboBox->setItemText(1, QCoreApplication::translate("MappingWindow", "\344\270\273\346\216\247\350\275\257\344\273\266(COMB)", nullptr));

        deviceLabel->setText(QCoreApplication::translate("MappingWindow", "\350\256\276\345\244\207:", nullptr));
        addButton->setText(QCoreApplication::translate("MappingWindow", "\346\267\273\345\212\240\346\230\240\345\260\204", nullptr));
        mappingTitleLabel->setText(QCoreApplication::translate("MappingWindow", "\346\230\240\345\260\204\351\205\215\347\275\256", nullptr));
        testStepsTitleLabel->setText(QCoreApplication::translate("MappingWindow", "\346\265\213\350\257\225\346\265\201\347\250\213", nullptr));
        logTitleLabel->setText(QCoreApplication::translate("MappingWindow", "\351\200\232\344\277\241\346\227\245\345\277\227", nullptr));
        clearLogButton->setText(QCoreApplication::translate("MappingWindow", "\346\270\205\347\251\272\346\227\245\345\277\227", nullptr));
        saveButton->setText(QCoreApplication::translate("MappingWindow", "\344\277\235\345\255\230\351\205\215\347\275\256", nullptr));
        loadButton->setText(QCoreApplication::translate("MappingWindow", "\345\212\240\350\275\275\351\205\215\347\275\256", nullptr));
        undoButton->setText(QCoreApplication::translate("MappingWindow", "\346\222\244\351\224\200", nullptr));
        redoButton->setText(QCoreApplication::translate("MappingWindow", "\351\207\215\345\201\232", nullptr));
        editButton->setText(QCoreApplication::translate("MappingWindow", "\347\274\226\350\276\221\346\255\245\351\252\244", nullptr));
        detailsButton->setText(QCoreApplication::translate("MappingWindow", "\346\237\245\347\234\213\350\257\246\346\203\205", nullptr));
        testButton->setText(QCoreApplication::translate("MappingWindow", "\345\274\200\345\247\213\346\265\213\350\257\225", nullptr));
        testReportButton->setText(QCoreApplication::translate("MappingWindow", "\346\265\213\350\257\225\346\212\245\345\221\212", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MappingWindow: public Ui_MappingWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAPPINGWINDOW_H
