#include <QCoreApplication>
#include <QDebug>
#include <QByteArray>
#include "crcutils.h"

// 标准Modbus CRC16测试用例
struct ModbusCrcTestCase {
    QString description;
    QByteArray data;
    quint16 expectedCrc;
    QString expectedCrcHex;
};

// 验证CRC16计算是否符合Modbus标准
void testModbusCrc16()
{
    qDebug() << "=== Modbus CRC16 校验测试 ===";
    
    // 标准Modbus CRC16测试用例
    QList<ModbusCrcTestCase> testCases = {
        // 测试用例1: 读保持寄存器 (功能码03)
        {
            "读保持寄存器 - 01 03 00 00 00 01",
            QByteArray::fromHex("010300000001"),
            0x84C0,  // 标准CRC16值
            "84C0"
        },
        
        // 测试用例2: 写单个寄存器 (功能码06)
        {
            "写单个寄存器 - 01 06 00 01 00 03",
            QByteArray::fromHex("010600010003"),
            0x9A9B,  // 标准CRC16值
            "9A9B"
        },
        
        // 测试用例3: 读输入寄存器 (功能码04)
        {
            "读输入寄存器 - 01 04 00 00 00 01",
            QByteArray::fromHex("010400000001"),
            0x31C6,  // 标准CRC16值
            "31C6"
        },
        
        // 测试用例4: 写多个寄存器 (功能码16)
        {
            "写多个寄存器 - 01 10 00 01 00 02 04 00 0A 01 02",
            QByteArray::fromHex("01100001000204000A0102"),
            0xC6F0,  // 标准CRC16值
            "C6F0"
        },
        
        // 测试用例5: 简单数据
        {
            "简单测试 - 01 02",
            QByteArray::fromHex("0102"),
            0x4140,  // 标准CRC16值
            "4140"
        }
    };
    
    bool allTestsPassed = true;
    
    for (const auto &testCase : testCases) {
        qDebug() << "\n--- 测试:" << testCase.description << "---";
        qDebug() << "输入数据:" << testCase.data.toHex().toUpper();
        
        // 使用当前实现计算CRC16
        quint16 calculatedCrc = CrcUtils::calculateCRC16(testCase.data);
        QByteArray crcBytes = CrcUtils::calculateCRC16Bytes(testCase.data);
        
        qDebug() << "期望CRC16:" << QString::number(testCase.expectedCrc, 16).toUpper();
        qDebug() << "计算CRC16:" << QString::number(calculatedCrc, 16).toUpper();
        qDebug() << "CRC字节序 (低字节在前):" << crcBytes.toHex().toUpper();
        
        // 验证CRC16值
        bool crcMatch = (calculatedCrc == testCase.expectedCrc);
        
        // 验证字节序 (Modbus使用低字节在前)
        quint8 lowByte = crcBytes[0] & 0xFF;
        quint8 highByte = crcBytes[1] & 0xFF;
        quint16 reconstructedCrc = (highByte << 8) | lowByte;
        bool byteOrderCorrect = (reconstructedCrc == calculatedCrc);
        
        qDebug() << "CRC值匹配:" << (crcMatch ? "✓" : "✗");
        qDebug() << "字节序正确:" << (byteOrderCorrect ? "✓" : "✗");
        
        if (!crcMatch || !byteOrderCorrect) {
            allTestsPassed = false;
            qDebug() << "❌ 测试失败!";
        } else {
            qDebug() << "✅ 测试通过!";
        }
    }
    
    qDebug() << "\n=== 测试总结 ===";
    if (allTestsPassed) {
        qDebug() << "✅ 所有测试通过! CRC16实现符合Modbus标准";
    } else {
        qDebug() << "❌ 部分测试失败! CRC16实现可能不符合Modbus标准";
    }
}

// 手动验证CRC16算法实现
void verifyAlgorithmImplementation()
{
    qDebug() << "\n=== CRC16算法实现验证 ===";
    
    // Modbus CRC16标准参数
    qDebug() << "Modbus CRC16标准参数:";
    qDebug() << "- 初始值: 0xFFFF";
    qDebug() << "- 多项式: 0xA001 (反向)";
    qDebug() << "- 字节序: 低字节在前 (Little Endian)";
    qDebug() << "- 反射输入: 是";
    qDebug() << "- 反射输出: 是";
    
    // 检查算法实现
    QByteArray testData = QByteArray::fromHex("0102");
    quint16 crc = 0xFFFF;
    
    qDebug() << "\n手动计算过程 (数据: 01 02):";
    qDebug() << "初始CRC:" << QString::number(crc, 16).toUpper();
    
    for (int i = 0; i < testData.size(); ++i) {
        quint8 byte = static_cast<quint8>(testData[i]);
        qDebug() << QString("处理字节 %1: %2").arg(i).arg(QString::number(byte, 16).toUpper());
        
        crc ^= byte;
        qDebug() << "  异或后:" << QString::number(crc, 16).toUpper();
        
        for (int j = 0; j < 8; ++j) {
            if (crc & 0x0001) {
                crc = (crc >> 1) ^ 0xA001;
                qDebug() << QString("    位%1: 右移并异或 -> %2").arg(j).arg(QString::number(crc, 16).toUpper());
            } else {
                crc = crc >> 1;
                qDebug() << QString("    位%1: 仅右移 -> %2").arg(j).arg(QString::number(crc, 16).toUpper());
            }
        }
    }
    
    qDebug() << "最终CRC:" << QString::number(crc, 16).toUpper();
    qDebug() << "库函数结果:" << QString::number(CrcUtils::calculateCRC16(testData), 16).toUpper();
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    testModbusCrc16();
    verifyAlgorithmImplementation();
    
    return 0;
}
