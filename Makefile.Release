#############################################################################
# Makefile for building: JcSoft
# Generated by qmake (3.1) (Qt 6.8.2)
# Project:  JcSoft.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Release

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_OPENGLWIDGETS_LIB -DQT_PRINTSUPPORT_LIB -DQT_AXCONTAINER_LIB -DQT_WIDGETS_LIB -DQT_OPENGL_LIB -DQT_GUI_LIB -DQT_SERIALPORT_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -fno-keep-inline-dllexport -O2 -Wall -Wextra -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -O2 -std=gnu++1z -Wall -Wextra -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I. -I../../../Qt/6.8.2/mingw_64/include -I../../../Qt/6.8.2/mingw_64/include/QtCharts -I../../../Qt/6.8.2/mingw_64/include/QtOpenGLWidgets -I../../../Qt/6.8.2/mingw_64/include/QtPrintSupport -I../../../Qt/6.8.2/mingw_64/include/QtAxContainer -I../../../Qt/6.8.2/mingw_64/include/QtWidgets -I../../../Qt/6.8.2/mingw_64/include/QtOpenGL -I../../../Qt/6.8.2/mingw_64/include/QtGui -I../../../Qt/6.8.2/mingw_64/include/QtSerialPort -I../../../Qt/6.8.2/mingw_64/include/QtCore -Irelease -I. -I/include -I../../../Qt/6.8.2/mingw_64/mkspecs/win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-s -Wl,-subsystem,windows -mthreads
LIBS        =        D:\Qt\6.8.2\mingw_64\lib\libQt6Charts.a D:\Qt\6.8.2\mingw_64\lib\libQt6OpenGLWidgets.a D:\Qt\6.8.2\mingw_64\lib\libQt6PrintSupport.a D:\Qt\6.8.2\mingw_64\lib\libQt6AxContainer.a D:\Qt\6.8.2\mingw_64\lib\libQt6AxBase.a -ladvapi32 -lgdi32 -lole32 -loleaut32 -luser32 -luuid -lmpr -luserenv -ld3d11 -ldxgi -ldxguid -ld3d12 D:\Qt\6.8.2\mingw_64\lib\libQt6Widgets.a D:\Qt\6.8.2\mingw_64\lib\libQt6OpenGL.a D:\Qt\6.8.2\mingw_64\lib\libQt6Gui.a D:\Qt\6.8.2\mingw_64\lib\libQt6SerialPort.a D:\Qt\6.8.2\mingw_64\lib\libQt6Core.a -lmingw32 D:\Qt\6.8.2\mingw_64\lib\libQt6EntryPoint.a -lshell32  
QMAKE         = D:\Qt\6.8.2\mingw_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = D:\Qt\6.8.2\mingw_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = D:\Qt\6.8.2\mingw_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = release

####### Files

SOURCES       = main.cpp \
		mainwindow.cpp \
		mappingwindow.cpp \
		modbusserial.cpp \
		modbusserialbase.cpp \
		parseitemdialog.cpp \
		setvaluedialog.cpp \
		valuefileselectiondialog.cpp \
		dataconversionrulesdialog.cpp \
		datamappingrulesdialog.cpp \
		crcutils.cpp \
		serialmanager.cpp release\qrc_resources.cpp \
		release\qrc_icons.cpp \
		release\moc_mainwindow.cpp \
		release\moc_mappingwindow.cpp \
		release\moc_modbusserial.cpp \
		release\moc_modbusserialbase.cpp \
		release\moc_parseitemdialog.cpp \
		release\moc_setvaluedialog.cpp \
		release\moc_valuefileselectiondialog.cpp \
		release\moc_dataconversionrulesdialog.cpp \
		release\moc_datamappingrulesdialog.cpp \
		release\moc_serialmanager.cpp
OBJECTS       = release/main.o \
		release/mainwindow.o \
		release/mappingwindow.o \
		release/modbusserial.o \
		release/modbusserialbase.o \
		release/parseitemdialog.o \
		release/setvaluedialog.o \
		release/valuefileselectiondialog.o \
		release/dataconversionrulesdialog.o \
		release/datamappingrulesdialog.o \
		release/crcutils.o \
		release/serialmanager.o \
		release/qrc_resources.o \
		release/qrc_icons.o \
		release/moc_mainwindow.o \
		release/moc_mappingwindow.o \
		release/moc_modbusserial.o \
		release/moc_modbusserialbase.o \
		release/moc_parseitemdialog.o \
		release/moc_setvaluedialog.o \
		release/moc_valuefileselectiondialog.o \
		release/moc_dataconversionrulesdialog.o \
		release/moc_datamappingrulesdialog.o \
		release/moc_serialmanager.o

DIST          =  mainwindow.h \
		mappingwindow.h \
		modbusserial.h \
		modbusserialbase.h \
		modbustestcase.h \
		parseitemdialog.h \
		setvaluedialog.h \
		valuefileselectiondialog.h \
		dataconversionrulesdialog.h \
		datamappingrulesdialog.h \
		crcutils.h \
		serialmanager.h main.cpp \
		mainwindow.cpp \
		mappingwindow.cpp \
		modbusserial.cpp \
		modbusserialbase.cpp \
		parseitemdialog.cpp \
		setvaluedialog.cpp \
		valuefileselectiondialog.cpp \
		dataconversionrulesdialog.cpp \
		datamappingrulesdialog.cpp \
		crcutils.cpp \
		serialmanager.cpp
QMAKE_TARGET  = JcSoft
DESTDIR        = release\ #avoid trailing-slash linebreak
TARGET         = JcSoft.exe
DESTDIR_TARGET = release\JcSoft.exe

####### Build rules

first: all
all: Makefile.Release  release/JcSoft.exe

release/JcSoft.exe: D:/Qt/6.8.2/mingw_64/lib/libQt6Charts.a D:/Qt/6.8.2/mingw_64/lib/libQt6OpenGLWidgets.a D:/Qt/6.8.2/mingw_64/lib/libQt6PrintSupport.a D:/Qt/6.8.2/mingw_64/lib/libQt6AxContainer.a D:/Qt/6.8.2/mingw_64/lib/libQt6Widgets.a D:/Qt/6.8.2/mingw_64/lib/libQt6OpenGL.a D:/Qt/6.8.2/mingw_64/lib/libQt6Gui.a D:/Qt/6.8.2/mingw_64/lib/libQt6SerialPort.a D:/Qt/6.8.2/mingw_64/lib/libQt6Core.a D:/Qt/6.8.2/mingw_64/lib/libQt6EntryPoint.a ui_mainwindow.h ui_mappingwindow.h ui_setvaluedialog.h $(OBJECTS) 
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) @release\object_script.JcSoft.Release $(LIBS)

qmake: FORCE
	@$(QMAKE) -o Makefile.Release JcSoft.pro

qmake_all: FORCE

dist:
	$(ZIP) JcSoft.zip $(SOURCES) $(DIST) JcSoft.pro ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\spec_pre.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\device_config.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\common\sanitize.conf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\common\gcc-base.conf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\common\g++-base.conf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\win32\windows_vulkan_sdk.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\common\windows-vulkan.conf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\common\g++-win32.conf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\common\windows-desktop.conf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\qconfig.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_ext_freetype.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_ext_libjpeg.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_ext_libpng.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_ext_openxr_loader.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3danimation.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3danimation_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dcore.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dcore_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dextras.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dextras_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dinput.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dinput_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dlogic.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dlogic_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dquick.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dquick_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dquickanimation.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dquickanimation_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dquickextras.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dquickextras_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dquickinput.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dquickinput_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dquickrender.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dquickrender_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dquickscene2d.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dquickscene2d_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dquickscene3d.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3dquickscene3d_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3drender.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_3drender_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_activeqt.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_activeqt_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_axbase_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_axcontainer.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_axcontainer_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_axserver.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_axserver_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_bluetooth.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_bluetooth_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_bodymovin_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_charts.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_charts_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_chartsqml.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_chartsqml_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_concurrent.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_concurrent_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_core.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_core5compat.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_core5compat_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_core_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_datavisualization.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_datavisualization_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_datavisualizationqml.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_datavisualizationqml_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_dbus.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_dbus_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_designer.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_designer_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_designercomponents_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_entrypoint_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_example_icons_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_examples_asset_downloader_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_fb_support_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_freetype_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_graphs.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_graphs_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_graphswidgets.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_graphswidgets_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_grpc.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_grpc_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_grpcquick.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_grpcquick_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_gui.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_gui_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_harfbuzz_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_help.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_help_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_httpserver.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_httpserver_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_insighttracker.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_insighttracker_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_insighttrackerqml.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_insighttrackerqml_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_jpeg_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_jsonrpc_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_labsanimation.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_labsanimation_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_labsplatform.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_labsplatform_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_labssettings.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_labssettings_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_labssharedimage.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_labssharedimage_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_languageserver_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_linguist.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_location.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_location_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_multimedia.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_multimedia_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_multimediaquick_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_multimediatestlibprivate_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_multimediawidgets.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_network.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_network_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_networkauth.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_networkauth_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_nfc.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_nfc_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_opengl.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_opengl_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_openglwidgets.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_openglwidgets_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_packetprotocol_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_pdf.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_pdf_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_pdfquick.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_pdfquick_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_pdfwidgets.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_pdfwidgets_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_png_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_positioning.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_positioning_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_positioningquick.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_positioningquick_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_printsupport.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_printsupport_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_protobuf.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_protobuf_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_protobufqtcoretypes.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_protobufqtcoretypes_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_protobufqtguitypes.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_protobufqtguitypes_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_protobufquick.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_protobufquick_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_protobufwellknowntypes.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_protobufwellknowntypes_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qdoccatch_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qdoccatchconversions_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qdoccatchgenerators_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qml.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qml_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmlcompiler.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmlcore.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmlcore_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmldebug_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmldom_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmlintegration.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmlintegration_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmlls_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmlmeta.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmlmeta_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmlmodels.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmlmodels_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmlnetwork.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmlnetwork_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmltest.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmltest_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmltoolingsettings_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3d.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3d_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dassetimport.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dassetimport_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dassetutils.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dassetutils_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3deffects.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3deffects_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dglslparser_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dhelpers.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dhelpers_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dhelpersimpl.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dhelpersimpl_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3diblbaker.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3diblbaker_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dparticleeffects.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dparticleeffects_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dparticles.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dparticles_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dphysics.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dphysics_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dphysicshelpers.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dphysicshelpers_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3druntimerender.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3druntimerender_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dspatialaudio_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dutils.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dutils_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dxr.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick3dxr_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quick_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickdialogs2.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickeffects_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quicklayouts.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quicklayouts_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickparticles_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickshapes_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quicktemplates2.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quicktimeline.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quicktimeline_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quicktimelineblendtrees.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quicktimelineblendtrees_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickvectorimage.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickvectorimage_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickvectorimagegenerator_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickwidgets.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_quickwidgets_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_remoteobjects.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_remoteobjects_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_remoteobjectsqml.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_remoteobjectsqml_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_repparser.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_repparser_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_scxml.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_scxml_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_scxmlqml.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_scxmlqml_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_sensors.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_sensors_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_sensorsquick.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_sensorsquick_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_serialbus.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_serialbus_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_serialport.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_serialport_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_shadertools.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_shadertools_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_spatialaudio.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_spatialaudio_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_sql.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_sql_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_statemachine.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_statemachine_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_statemachineqml.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_statemachineqml_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_svg.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_svg_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_svgwidgets.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_svgwidgets_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_testlib.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_testlib_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_texttospeech.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_texttospeech_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_tools_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_uiplugin.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_uitools.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_uitools_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_virtualkeyboard.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_virtualkeyboard_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_virtualkeyboardsettings.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_virtualkeyboardsettings_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_webchannel.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_webchannel_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_webchannelquick.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_webchannelquick_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_websockets.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_websockets_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_webview.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_webview_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_webviewquick.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_webviewquick_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_widgets.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_widgets_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_xml.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_xml_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\modules\qt_lib_zlib_private.pri ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\qt_functions.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\qt_config.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\win32-g++\qmake.conf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\spec_post.prf .qmake.stash ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\exclusive_builds.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\toolchain.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\default_pre.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\win32\default_pre.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\resolve_config.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\exclusive_builds_post.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\default_post.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\build_pass.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\precompile_header.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\warn_on.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\permissions.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\qt.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\resources_functions.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\resources.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\moc.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\win32\opengl.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\uic.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\win32\dumpcpp.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\qmake_use.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\file_copies.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\win32\windows.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\testcase_targets.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\exceptions.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\yacc.prf ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\lex.prf JcSoft.pro resources.qrc icons.qrc ..\..\..\Qt\6.8.2\mingw_64\lib\Qt6Charts.prl ..\..\..\Qt\6.8.2\mingw_64\lib\Qt6OpenGLWidgets.prl ..\..\..\Qt\6.8.2\mingw_64\lib\Qt6PrintSupport.prl ..\..\..\Qt\6.8.2\mingw_64\lib\Qt6AxContainer.prl ..\..\..\Qt\6.8.2\mingw_64\lib\Qt6Widgets.prl ..\..\..\Qt\6.8.2\mingw_64\lib\Qt6OpenGL.prl ..\..\..\Qt\6.8.2\mingw_64\lib\Qt6Gui.prl ..\..\..\Qt\6.8.2\mingw_64\lib\Qt6SerialPort.prl ..\..\..\Qt\6.8.2\mingw_64\lib\Qt6Core.prl ..\..\..\Qt\6.8.2\mingw_64\lib\Qt6EntryPoint.prl   resources.qrc icons.qrc ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\data\dummy.cpp  mainwindow.h mappingwindow.h modbusserial.h modbusserialbase.h modbustestcase.h parseitemdialog.h setvaluedialog.h valuefileselectiondialog.h dataconversionrulesdialog.h datamappingrulesdialog.h crcutils.h serialmanager.h  main.cpp mainwindow.cpp mappingwindow.cpp modbusserial.cpp modbusserialbase.cpp parseitemdialog.cpp setvaluedialog.cpp valuefileselectiondialog.cpp dataconversionrulesdialog.cpp datamappingrulesdialog.cpp crcutils.cpp serialmanager.cpp mainwindow.ui mappingwindow.ui setvaluedialog.ui     

clean: compiler_clean 
	-$(DEL_FILE) release\main.o release\mainwindow.o release\mappingwindow.o release\modbusserial.o release\modbusserialbase.o release\parseitemdialog.o release\setvaluedialog.o release\valuefileselectiondialog.o release\dataconversionrulesdialog.o release\datamappingrulesdialog.o release\crcutils.o release\serialmanager.o release\qrc_resources.o release\qrc_icons.o release\moc_mainwindow.o release\moc_mappingwindow.o release\moc_modbusserial.o release\moc_modbusserialbase.o release\moc_parseitemdialog.o release\moc_setvaluedialog.o release\moc_valuefileselectiondialog.o release\moc_dataconversionrulesdialog.o release\moc_datamappingrulesdialog.o release\moc_serialmanager.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Release

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all: release/qrc_resources.cpp release/qrc_icons.cpp
compiler_rcc_clean:
	-$(DEL_FILE) release\qrc_resources.cpp release\qrc_icons.cpp
release/qrc_resources.cpp: resources.qrc \
		../../../Qt/6.8.2/mingw_64/bin/rcc.exe \
		images/refresh.png \
		images/send.svg \
		images/exit.png \
		images/disconnect.png \
		images/books.png \
		images/save.png \
		images/open.png \
		images/exit.svg \
		images/close.svg \
		images/settings.png \
		images/open.svg \
		images/Write.png \
		images/view.svg \
		images/app_icon_simple.svg \
		images/app_icon.svg \
		images/connect.png \
		images/close.png \
		images/send.png \
		images/save.svg
	D:\Qt\6.8.2\mingw_64\bin\rcc.exe -name resources --no-zstd resources.qrc -o release\qrc_resources.cpp

release/qrc_icons.cpp: icons.qrc \
		../../../Qt/6.8.2/mingw_64/bin/rcc.exe \
		icons/setvalue.svg \
		icons/test.svg \
		icons/refresh.svg \
		icons/settings.svg \
		icons/connect.svg \
		icons/clear.svg \
		icons/disconnect.svg \
		icons/modbus.svg
	D:\Qt\6.8.2\mingw_64\bin\rcc.exe -name icons --no-zstd icons.qrc -o release\qrc_icons.cpp

compiler_moc_predefs_make_all: release/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) release\moc_predefs.h
release/moc_predefs.h: ../../../Qt/6.8.2/mingw_64/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -O2 -std=gnu++1z -Wall -Wextra -Wextra -dM -E -o release\moc_predefs.h ..\..\..\Qt\6.8.2\mingw_64\mkspecs\features\data\dummy.cpp

compiler_dumpcpp_decl_make_all:
compiler_dumpcpp_decl_clean:
compiler_moc_header_make_all: release/moc_mainwindow.cpp release/moc_mappingwindow.cpp release/moc_modbusserial.cpp release/moc_modbusserialbase.cpp release/moc_parseitemdialog.cpp release/moc_setvaluedialog.cpp release/moc_valuefileselectiondialog.cpp release/moc_dataconversionrulesdialog.cpp release/moc_datamappingrulesdialog.cpp release/moc_serialmanager.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) release\moc_mainwindow.cpp release\moc_mappingwindow.cpp release\moc_modbusserial.cpp release\moc_modbusserialbase.cpp release\moc_parseitemdialog.cpp release\moc_setvaluedialog.cpp release\moc_valuefileselectiondialog.cpp release\moc_dataconversionrulesdialog.cpp release\moc_datamappingrulesdialog.cpp release\moc_serialmanager.cpp
release/moc_mainwindow.cpp: mainwindow.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMainWindow \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmainwindow.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtgui-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstring.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qchar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringliteral.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qanystringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qutf8stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringbuilder.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearraylist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringmatcher.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetatype.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatastream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevicebase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfloat16.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetacontainer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopeguard.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbindingstorage.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmargins.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q23utility.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qaction.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qkeysequence.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qicon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrect.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcolor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgb.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgba64.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qimage.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixelformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtransform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpolygon.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qregion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qspan.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20iterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qline.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvariant.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdebug.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtextstream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qset.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhash.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpalette.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbrush.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfont.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qendian.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontmetrics.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbitmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qurl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qeventpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvector2d.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvectornd.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpointingdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QRect \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSize \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSizeF \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTransform \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnativeinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qeventloop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfutureinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmutex.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtsan_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qresultstore.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthreadpool.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthread.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrunnable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexception.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpromise.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputmethod.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlocale.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/QSerialPort \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qproperty.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpropertyprivate.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qtserialportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/QSerialPortInfo \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialportinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QString \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTimer \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasictimer.h \
		modbusserial.h \
		modbusserialbase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QByteArray \
		serialmanager.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QMap \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStringList \
		mappingwindow.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QComboBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcombobox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyleoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvalidator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qregularexpression.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyle.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabbar.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qrubberband.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qframe.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QListWidget \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlistwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlistview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGraphicsScene \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgraphicsscene.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpen.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGraphicsView \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgraphicsview.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpainter.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qscrollarea.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGraphicsLineItem \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgraphicsitem.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpainterpath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStack \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstack.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTextEdit \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtextedit.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextdocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QPushButton \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qpushbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMenu \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmenu.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialogButtonBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		setvaluedialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTableWidgetItem \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtablewidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtableview.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QAction \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QContextMenuEvent \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QDesktopServices \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qdesktopservices.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QUrl \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTemporaryFile \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtemporaryfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfiledevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/QPrinter \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qprinter.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupport-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagedpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagelayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagesize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpageranges.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTextDocument \
		crcutils.h \
		release/moc_predefs.h \
		../../../Qt/6.8.2/mingw_64/bin/moc.exe
	D:\Qt\6.8.2\mingw_64\bin\moc.exe $(DEFINES) --include D:/freedom/VC++/JcSoft/release/moc_predefs.h -ID:/Qt/6.8.2/mingw_64/mkspecs/win32-g++ -ID:/freedom/VC++/JcSoft -ID:/Qt/6.8.2/mingw_64/include -ID:/Qt/6.8.2/mingw_64/include/QtCharts -ID:/Qt/6.8.2/mingw_64/include/QtOpenGLWidgets -ID:/Qt/6.8.2/mingw_64/include/QtPrintSupport -ID:/Qt/6.8.2/mingw_64/include/QtAxContainer -ID:/Qt/6.8.2/mingw_64/include/QtWidgets -ID:/Qt/6.8.2/mingw_64/include/QtOpenGL -ID:/Qt/6.8.2/mingw_64/include/QtGui -ID:/Qt/6.8.2/mingw_64/include/QtSerialPort -ID:/Qt/6.8.2/mingw_64/include/QtCore -IC:/MinGW-w64/include/c++/14.2.0 -IC:/MinGW-w64/include/c++/14.2.0/x86_64-w64-mingw32 -IC:/MinGW-w64/include/c++/14.2.0/backward -IC:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include -IC:/MinGW-w64/include -IC:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed -IC:/MinGW-w64/x86_64-w64-mingw32/include mainwindow.h -o release\moc_mainwindow.cpp

release/moc_mappingwindow.cpp: mappingwindow.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMainWindow \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmainwindow.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtgui-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstring.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qchar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringliteral.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qanystringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qutf8stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringbuilder.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearraylist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringmatcher.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetatype.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatastream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevicebase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfloat16.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetacontainer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopeguard.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbindingstorage.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmargins.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q23utility.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qaction.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qkeysequence.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qicon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrect.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcolor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgb.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgba64.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qimage.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixelformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtransform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpolygon.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qregion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qspan.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20iterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qline.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvariant.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdebug.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtextstream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qset.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhash.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpalette.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbrush.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfont.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qendian.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontmetrics.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbitmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qurl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qeventpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvector2d.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvectornd.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpointingdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QRect \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSize \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSizeF \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTransform \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnativeinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qeventloop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfutureinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmutex.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtsan_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qresultstore.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthreadpool.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthread.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrunnable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexception.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpromise.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputmethod.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlocale.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QComboBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcombobox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyleoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvalidator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qregularexpression.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyle.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabbar.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qrubberband.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qframe.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QListWidget \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlistwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlistview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGraphicsScene \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgraphicsscene.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpen.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGraphicsView \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgraphicsview.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpainter.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qscrollarea.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGraphicsLineItem \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgraphicsitem.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpainterpath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QMap \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStack \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstack.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QString \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTimer \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasictimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTextEdit \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtextedit.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextdocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QPushButton \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qpushbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMenu \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmenu.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialogButtonBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		modbusserial.h \
		modbusserialbase.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/QSerialPort \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qproperty.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpropertyprivate.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qtserialportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QByteArray \
		release/moc_predefs.h \
		../../../Qt/6.8.2/mingw_64/bin/moc.exe
	D:\Qt\6.8.2\mingw_64\bin\moc.exe $(DEFINES) --include D:/freedom/VC++/JcSoft/release/moc_predefs.h -ID:/Qt/6.8.2/mingw_64/mkspecs/win32-g++ -ID:/freedom/VC++/JcSoft -ID:/Qt/6.8.2/mingw_64/include -ID:/Qt/6.8.2/mingw_64/include/QtCharts -ID:/Qt/6.8.2/mingw_64/include/QtOpenGLWidgets -ID:/Qt/6.8.2/mingw_64/include/QtPrintSupport -ID:/Qt/6.8.2/mingw_64/include/QtAxContainer -ID:/Qt/6.8.2/mingw_64/include/QtWidgets -ID:/Qt/6.8.2/mingw_64/include/QtOpenGL -ID:/Qt/6.8.2/mingw_64/include/QtGui -ID:/Qt/6.8.2/mingw_64/include/QtSerialPort -ID:/Qt/6.8.2/mingw_64/include/QtCore -IC:/MinGW-w64/include/c++/14.2.0 -IC:/MinGW-w64/include/c++/14.2.0/x86_64-w64-mingw32 -IC:/MinGW-w64/include/c++/14.2.0/backward -IC:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include -IC:/MinGW-w64/include -IC:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed -IC:/MinGW-w64/x86_64-w64-mingw32/include mappingwindow.h -o release\moc_mappingwindow.cpp

release/moc_modbusserial.cpp: modbusserial.h \
		modbusserialbase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstring.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qchar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringliteral.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qanystringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qutf8stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringbuilder.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearraylist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringmatcher.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetatype.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatastream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevicebase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfloat16.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetacontainer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopeguard.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbindingstorage.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/QSerialPort \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qproperty.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpropertyprivate.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qtserialportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTimer \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasictimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QByteArray \
		release/moc_predefs.h \
		../../../Qt/6.8.2/mingw_64/bin/moc.exe
	D:\Qt\6.8.2\mingw_64\bin\moc.exe $(DEFINES) --include D:/freedom/VC++/JcSoft/release/moc_predefs.h -ID:/Qt/6.8.2/mingw_64/mkspecs/win32-g++ -ID:/freedom/VC++/JcSoft -ID:/Qt/6.8.2/mingw_64/include -ID:/Qt/6.8.2/mingw_64/include/QtCharts -ID:/Qt/6.8.2/mingw_64/include/QtOpenGLWidgets -ID:/Qt/6.8.2/mingw_64/include/QtPrintSupport -ID:/Qt/6.8.2/mingw_64/include/QtAxContainer -ID:/Qt/6.8.2/mingw_64/include/QtWidgets -ID:/Qt/6.8.2/mingw_64/include/QtOpenGL -ID:/Qt/6.8.2/mingw_64/include/QtGui -ID:/Qt/6.8.2/mingw_64/include/QtSerialPort -ID:/Qt/6.8.2/mingw_64/include/QtCore -IC:/MinGW-w64/include/c++/14.2.0 -IC:/MinGW-w64/include/c++/14.2.0/x86_64-w64-mingw32 -IC:/MinGW-w64/include/c++/14.2.0/backward -IC:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include -IC:/MinGW-w64/include -IC:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed -IC:/MinGW-w64/x86_64-w64-mingw32/include modbusserial.h -o release\moc_modbusserial.cpp

release/moc_modbusserialbase.cpp: modbusserialbase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstring.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qchar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringliteral.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qanystringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qutf8stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringbuilder.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearraylist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringmatcher.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetatype.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatastream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevicebase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfloat16.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetacontainer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopeguard.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbindingstorage.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/QSerialPort \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qproperty.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpropertyprivate.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qtserialportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTimer \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasictimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QByteArray \
		release/moc_predefs.h \
		../../../Qt/6.8.2/mingw_64/bin/moc.exe
	D:\Qt\6.8.2\mingw_64\bin\moc.exe $(DEFINES) --include D:/freedom/VC++/JcSoft/release/moc_predefs.h -ID:/Qt/6.8.2/mingw_64/mkspecs/win32-g++ -ID:/freedom/VC++/JcSoft -ID:/Qt/6.8.2/mingw_64/include -ID:/Qt/6.8.2/mingw_64/include/QtCharts -ID:/Qt/6.8.2/mingw_64/include/QtOpenGLWidgets -ID:/Qt/6.8.2/mingw_64/include/QtPrintSupport -ID:/Qt/6.8.2/mingw_64/include/QtAxContainer -ID:/Qt/6.8.2/mingw_64/include/QtWidgets -ID:/Qt/6.8.2/mingw_64/include/QtOpenGL -ID:/Qt/6.8.2/mingw_64/include/QtGui -ID:/Qt/6.8.2/mingw_64/include/QtSerialPort -ID:/Qt/6.8.2/mingw_64/include/QtCore -IC:/MinGW-w64/include/c++/14.2.0 -IC:/MinGW-w64/include/c++/14.2.0/x86_64-w64-mingw32 -IC:/MinGW-w64/include/c++/14.2.0/backward -IC:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include -IC:/MinGW-w64/include -IC:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed -IC:/MinGW-w64/x86_64-w64-mingw32/include modbusserialbase.h -o release\moc_modbusserialbase.cpp

release/moc_parseitemdialog.cpp: parseitemdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtgui-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstring.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qchar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringliteral.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qanystringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qutf8stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringbuilder.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearraylist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringmatcher.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetatype.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatastream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevicebase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfloat16.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetacontainer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopeguard.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbindingstorage.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmargins.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q23utility.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qaction.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qkeysequence.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qicon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrect.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcolor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgb.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgba64.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qimage.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixelformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtransform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpolygon.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qregion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qspan.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20iterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qline.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvariant.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdebug.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtextstream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qset.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhash.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpalette.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbrush.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfont.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qendian.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontmetrics.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbitmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qurl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qeventpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvector2d.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvectornd.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpointingdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QRect \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSize \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSizeF \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTransform \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnativeinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qeventloop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfutureinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmutex.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtsan_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qresultstore.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthreadpool.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthread.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrunnable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexception.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpromise.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputmethod.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlocale.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QComboBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcombobox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyleoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvalidator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qregularexpression.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyle.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabbar.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qrubberband.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qframe.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLineEdit \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlineedit.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextdocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpen.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QSpinBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLabel \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlabel.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpicture.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QPushButton \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qpushbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGridLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgridlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayoutitem.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qboxlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialogButtonBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QCheckBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcheckbox.h \
		setvaluedialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMainWindow \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmainwindow.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTimer \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasictimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTableWidgetItem \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtablewidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtableview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMenu \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmenu.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QAction \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QContextMenuEvent \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QDesktopServices \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qdesktopservices.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QUrl \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTemporaryFile \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtemporaryfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfiledevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/QPrinter \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qprinter.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupport-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagedpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagelayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagesize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpageranges.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTextDocument \
		serialmanager.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QMap \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/QSerialPort \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qproperty.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpropertyprivate.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qtserialportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QString \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStringList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QByteArray \
		modbusserial.h \
		modbusserialbase.h \
		crcutils.h \
		release/moc_predefs.h \
		../../../Qt/6.8.2/mingw_64/bin/moc.exe
	D:\Qt\6.8.2\mingw_64\bin\moc.exe $(DEFINES) --include D:/freedom/VC++/JcSoft/release/moc_predefs.h -ID:/Qt/6.8.2/mingw_64/mkspecs/win32-g++ -ID:/freedom/VC++/JcSoft -ID:/Qt/6.8.2/mingw_64/include -ID:/Qt/6.8.2/mingw_64/include/QtCharts -ID:/Qt/6.8.2/mingw_64/include/QtOpenGLWidgets -ID:/Qt/6.8.2/mingw_64/include/QtPrintSupport -ID:/Qt/6.8.2/mingw_64/include/QtAxContainer -ID:/Qt/6.8.2/mingw_64/include/QtWidgets -ID:/Qt/6.8.2/mingw_64/include/QtOpenGL -ID:/Qt/6.8.2/mingw_64/include/QtGui -ID:/Qt/6.8.2/mingw_64/include/QtSerialPort -ID:/Qt/6.8.2/mingw_64/include/QtCore -IC:/MinGW-w64/include/c++/14.2.0 -IC:/MinGW-w64/include/c++/14.2.0/x86_64-w64-mingw32 -IC:/MinGW-w64/include/c++/14.2.0/backward -IC:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include -IC:/MinGW-w64/include -IC:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed -IC:/MinGW-w64/x86_64-w64-mingw32/include parseitemdialog.h -o release\moc_parseitemdialog.cpp

release/moc_setvaluedialog.cpp: setvaluedialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtgui-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstring.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qchar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringliteral.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qanystringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qutf8stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringbuilder.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearraylist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringmatcher.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetatype.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatastream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevicebase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfloat16.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetacontainer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopeguard.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbindingstorage.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmargins.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q23utility.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qaction.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qkeysequence.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qicon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrect.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcolor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgb.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgba64.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qimage.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixelformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtransform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpolygon.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qregion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qspan.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20iterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qline.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvariant.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdebug.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtextstream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qset.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhash.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpalette.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbrush.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfont.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qendian.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontmetrics.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbitmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qurl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qeventpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvector2d.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvectornd.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpointingdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QRect \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSize \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSizeF \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTransform \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnativeinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qeventloop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfutureinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmutex.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtsan_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qresultstore.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthreadpool.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthread.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrunnable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexception.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpromise.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputmethod.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlocale.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMainWindow \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmainwindow.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTimer \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasictimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTableWidgetItem \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtablewidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtableview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qframe.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyleoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvalidator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qregularexpression.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyle.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabbar.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qrubberband.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMenu \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmenu.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QAction \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QContextMenuEvent \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QDesktopServices \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qdesktopservices.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QUrl \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTemporaryFile \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtemporaryfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfiledevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/QPrinter \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qprinter.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupport-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagedpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagelayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagesize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpageranges.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTextDocument \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextdocument.h \
		serialmanager.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QMap \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/QSerialPort \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qproperty.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpropertyprivate.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qtserialportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QString \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStringList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QByteArray \
		modbusserial.h \
		modbusserialbase.h \
		crcutils.h \
		release/moc_predefs.h \
		../../../Qt/6.8.2/mingw_64/bin/moc.exe
	D:\Qt\6.8.2\mingw_64\bin\moc.exe $(DEFINES) --include D:/freedom/VC++/JcSoft/release/moc_predefs.h -ID:/Qt/6.8.2/mingw_64/mkspecs/win32-g++ -ID:/freedom/VC++/JcSoft -ID:/Qt/6.8.2/mingw_64/include -ID:/Qt/6.8.2/mingw_64/include/QtCharts -ID:/Qt/6.8.2/mingw_64/include/QtOpenGLWidgets -ID:/Qt/6.8.2/mingw_64/include/QtPrintSupport -ID:/Qt/6.8.2/mingw_64/include/QtAxContainer -ID:/Qt/6.8.2/mingw_64/include/QtWidgets -ID:/Qt/6.8.2/mingw_64/include/QtOpenGL -ID:/Qt/6.8.2/mingw_64/include/QtGui -ID:/Qt/6.8.2/mingw_64/include/QtSerialPort -ID:/Qt/6.8.2/mingw_64/include/QtCore -IC:/MinGW-w64/include/c++/14.2.0 -IC:/MinGW-w64/include/c++/14.2.0/x86_64-w64-mingw32 -IC:/MinGW-w64/include/c++/14.2.0/backward -IC:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include -IC:/MinGW-w64/include -IC:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed -IC:/MinGW-w64/x86_64-w64-mingw32/include setvaluedialog.h -o release\moc_setvaluedialog.cpp

release/moc_valuefileselectiondialog.cpp: valuefileselectiondialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtgui-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstring.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qchar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringliteral.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qanystringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qutf8stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringbuilder.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearraylist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringmatcher.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetatype.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatastream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevicebase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfloat16.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetacontainer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopeguard.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbindingstorage.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmargins.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q23utility.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qaction.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qkeysequence.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qicon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrect.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcolor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgb.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgba64.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qimage.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixelformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtransform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpolygon.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qregion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qspan.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20iterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qline.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvariant.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdebug.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtextstream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qset.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhash.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpalette.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbrush.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfont.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qendian.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontmetrics.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbitmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qurl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qeventpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvector2d.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvectornd.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpointingdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QRect \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSize \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSizeF \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTransform \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnativeinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qeventloop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfutureinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmutex.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtsan_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qresultstore.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthreadpool.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthread.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrunnable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexception.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpromise.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputmethod.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlocale.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QVBoxLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qboxlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayoutitem.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgridlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QHBoxLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QFormLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qformlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLabel \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlabel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qframe.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpicture.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextdocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QComboBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcombobox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyleoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvalidator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qregularexpression.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyle.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabbar.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qrubberband.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLineEdit \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlineedit.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpen.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QPushButton \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qpushbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTableWidget \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtablewidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtableview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QCheckBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcheckbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QHeaderView \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qheaderview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMessageBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmessagebox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtAxContainer/QAxObject \
		../../../Qt/6.8.2/mingw_64/include/QtAxContainer/qaxobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtAxContainer/qaxbase.h \
		../../../Qt/6.8.2/mingw_64/include/QtAxContainer/qaxobjectinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QVariant \
		setvaluedialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMainWindow \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmainwindow.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTimer \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasictimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTableWidgetItem \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMenu \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmenu.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QAction \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QContextMenuEvent \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QDesktopServices \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qdesktopservices.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QUrl \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTemporaryFile \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtemporaryfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfiledevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/QPrinter \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qprinter.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupport-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagedpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagelayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagesize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpageranges.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTextDocument \
		serialmanager.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QMap \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/QSerialPort \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qproperty.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpropertyprivate.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qtserialportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QString \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStringList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QByteArray \
		modbusserial.h \
		modbusserialbase.h \
		crcutils.h \
		dataconversionrulesdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGridLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QSpinBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGroupBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgroupbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialogButtonBox \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonDocument \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsondocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonvalue.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcborvalue.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcborcommon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatetime.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcalendar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/quuid.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonArray \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonarray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QFile \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStandardPaths \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstandardpaths.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QDir \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdir.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdirlisting.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfileinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimezone.h \
		datamappingrulesdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QListWidget \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlistwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlistview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QFileDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qfiledialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QDateTime \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QInputDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qinputdialog.h \
		release/moc_predefs.h \
		../../../Qt/6.8.2/mingw_64/bin/moc.exe
	D:\Qt\6.8.2\mingw_64\bin\moc.exe $(DEFINES) --include D:/freedom/VC++/JcSoft/release/moc_predefs.h -ID:/Qt/6.8.2/mingw_64/mkspecs/win32-g++ -ID:/freedom/VC++/JcSoft -ID:/Qt/6.8.2/mingw_64/include -ID:/Qt/6.8.2/mingw_64/include/QtCharts -ID:/Qt/6.8.2/mingw_64/include/QtOpenGLWidgets -ID:/Qt/6.8.2/mingw_64/include/QtPrintSupport -ID:/Qt/6.8.2/mingw_64/include/QtAxContainer -ID:/Qt/6.8.2/mingw_64/include/QtWidgets -ID:/Qt/6.8.2/mingw_64/include/QtOpenGL -ID:/Qt/6.8.2/mingw_64/include/QtGui -ID:/Qt/6.8.2/mingw_64/include/QtSerialPort -ID:/Qt/6.8.2/mingw_64/include/QtCore -IC:/MinGW-w64/include/c++/14.2.0 -IC:/MinGW-w64/include/c++/14.2.0/x86_64-w64-mingw32 -IC:/MinGW-w64/include/c++/14.2.0/backward -IC:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include -IC:/MinGW-w64/include -IC:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed -IC:/MinGW-w64/x86_64-w64-mingw32/include valuefileselectiondialog.h -o release\moc_valuefileselectiondialog.cpp

release/moc_dataconversionrulesdialog.cpp: dataconversionrulesdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtgui-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstring.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qchar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringliteral.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qanystringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qutf8stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringbuilder.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearraylist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringmatcher.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetatype.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatastream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevicebase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfloat16.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetacontainer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopeguard.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbindingstorage.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmargins.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q23utility.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qaction.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qkeysequence.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qicon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrect.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcolor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgb.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgba64.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qimage.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixelformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtransform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpolygon.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qregion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qspan.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20iterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qline.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvariant.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdebug.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtextstream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qset.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhash.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpalette.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbrush.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfont.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qendian.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontmetrics.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbitmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qurl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qeventpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvector2d.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvectornd.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpointingdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QRect \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSize \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSizeF \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTransform \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnativeinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qeventloop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfutureinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmutex.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtsan_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qresultstore.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthreadpool.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthread.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrunnable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexception.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpromise.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputmethod.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlocale.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QVBoxLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qboxlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayoutitem.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgridlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QHBoxLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGridLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLabel \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlabel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qframe.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpicture.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextdocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLineEdit \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlineedit.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpen.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QPushButton \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qpushbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTableWidget \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtablewidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtableview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyleoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvalidator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qregularexpression.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyle.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabbar.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qrubberband.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QHeaderView \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qheaderview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMessageBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmessagebox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QCheckBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcheckbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QComboBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcombobox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QSpinBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGroupBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgroupbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialogButtonBox \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonDocument \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsondocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonvalue.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcborvalue.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcborcommon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatetime.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcalendar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/quuid.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonArray \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonarray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QFile \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfiledevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStandardPaths \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstandardpaths.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QDir \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdir.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdirlisting.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfileinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimezone.h \
		release/moc_predefs.h \
		../../../Qt/6.8.2/mingw_64/bin/moc.exe
	D:\Qt\6.8.2\mingw_64\bin\moc.exe $(DEFINES) --include D:/freedom/VC++/JcSoft/release/moc_predefs.h -ID:/Qt/6.8.2/mingw_64/mkspecs/win32-g++ -ID:/freedom/VC++/JcSoft -ID:/Qt/6.8.2/mingw_64/include -ID:/Qt/6.8.2/mingw_64/include/QtCharts -ID:/Qt/6.8.2/mingw_64/include/QtOpenGLWidgets -ID:/Qt/6.8.2/mingw_64/include/QtPrintSupport -ID:/Qt/6.8.2/mingw_64/include/QtAxContainer -ID:/Qt/6.8.2/mingw_64/include/QtWidgets -ID:/Qt/6.8.2/mingw_64/include/QtOpenGL -ID:/Qt/6.8.2/mingw_64/include/QtGui -ID:/Qt/6.8.2/mingw_64/include/QtSerialPort -ID:/Qt/6.8.2/mingw_64/include/QtCore -IC:/MinGW-w64/include/c++/14.2.0 -IC:/MinGW-w64/include/c++/14.2.0/x86_64-w64-mingw32 -IC:/MinGW-w64/include/c++/14.2.0/backward -IC:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include -IC:/MinGW-w64/include -IC:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed -IC:/MinGW-w64/x86_64-w64-mingw32/include dataconversionrulesdialog.h -o release\moc_dataconversionrulesdialog.cpp

release/moc_datamappingrulesdialog.cpp: datamappingrulesdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtgui-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstring.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qchar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringliteral.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qanystringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qutf8stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringbuilder.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearraylist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringmatcher.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetatype.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatastream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevicebase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfloat16.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetacontainer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopeguard.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbindingstorage.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmargins.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q23utility.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qaction.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qkeysequence.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qicon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrect.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcolor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgb.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgba64.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qimage.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixelformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtransform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpolygon.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qregion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qspan.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20iterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qline.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvariant.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdebug.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtextstream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qset.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhash.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpalette.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbrush.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfont.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qendian.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontmetrics.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbitmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qurl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qeventpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvector2d.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvectornd.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpointingdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QRect \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSize \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSizeF \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTransform \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnativeinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qeventloop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfutureinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmutex.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtsan_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qresultstore.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthreadpool.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthread.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrunnable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexception.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpromise.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputmethod.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlocale.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QVBoxLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qboxlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayoutitem.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgridlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QHBoxLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGridLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QFormLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qformlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLabel \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlabel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qframe.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpicture.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextdocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QComboBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcombobox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyleoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvalidator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qregularexpression.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyle.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabbar.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qrubberband.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QListWidget \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlistwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlistview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLineEdit \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlineedit.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpen.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QPushButton \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qpushbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTableWidget \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtablewidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtableview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTableWidgetItem \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QHeaderView \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qheaderview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMessageBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmessagebox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGroupBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgroupbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QFileDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qfiledialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdir.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdirlisting.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfiledevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatetime.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcalendar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfileinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimezone.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonDocument \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsondocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonvalue.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcborvalue.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcborcommon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/quuid.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonArray \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonarray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStandardPaths \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstandardpaths.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QDir \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QDateTime \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QInputDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qinputdialog.h \
		release/moc_predefs.h \
		../../../Qt/6.8.2/mingw_64/bin/moc.exe
	D:\Qt\6.8.2\mingw_64\bin\moc.exe $(DEFINES) --include D:/freedom/VC++/JcSoft/release/moc_predefs.h -ID:/Qt/6.8.2/mingw_64/mkspecs/win32-g++ -ID:/freedom/VC++/JcSoft -ID:/Qt/6.8.2/mingw_64/include -ID:/Qt/6.8.2/mingw_64/include/QtCharts -ID:/Qt/6.8.2/mingw_64/include/QtOpenGLWidgets -ID:/Qt/6.8.2/mingw_64/include/QtPrintSupport -ID:/Qt/6.8.2/mingw_64/include/QtAxContainer -ID:/Qt/6.8.2/mingw_64/include/QtWidgets -ID:/Qt/6.8.2/mingw_64/include/QtOpenGL -ID:/Qt/6.8.2/mingw_64/include/QtGui -ID:/Qt/6.8.2/mingw_64/include/QtSerialPort -ID:/Qt/6.8.2/mingw_64/include/QtCore -IC:/MinGW-w64/include/c++/14.2.0 -IC:/MinGW-w64/include/c++/14.2.0/x86_64-w64-mingw32 -IC:/MinGW-w64/include/c++/14.2.0/backward -IC:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include -IC:/MinGW-w64/include -IC:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed -IC:/MinGW-w64/x86_64-w64-mingw32/include datamappingrulesdialog.h -o release\moc_datamappingrulesdialog.cpp

release/moc_serialmanager.cpp: serialmanager.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstring.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qchar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringliteral.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qanystringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qutf8stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringbuilder.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearraylist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringmatcher.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetatype.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatastream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevicebase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfloat16.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetacontainer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopeguard.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbindingstorage.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QMap \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/QSerialPort \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qproperty.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpropertyprivate.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qtserialportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QString \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStringList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QByteArray \
		modbusserial.h \
		modbusserialbase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTimer \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasictimer.h \
		release/moc_predefs.h \
		../../../Qt/6.8.2/mingw_64/bin/moc.exe
	D:\Qt\6.8.2\mingw_64\bin\moc.exe $(DEFINES) --include D:/freedom/VC++/JcSoft/release/moc_predefs.h -ID:/Qt/6.8.2/mingw_64/mkspecs/win32-g++ -ID:/freedom/VC++/JcSoft -ID:/Qt/6.8.2/mingw_64/include -ID:/Qt/6.8.2/mingw_64/include/QtCharts -ID:/Qt/6.8.2/mingw_64/include/QtOpenGLWidgets -ID:/Qt/6.8.2/mingw_64/include/QtPrintSupport -ID:/Qt/6.8.2/mingw_64/include/QtAxContainer -ID:/Qt/6.8.2/mingw_64/include/QtWidgets -ID:/Qt/6.8.2/mingw_64/include/QtOpenGL -ID:/Qt/6.8.2/mingw_64/include/QtGui -ID:/Qt/6.8.2/mingw_64/include/QtSerialPort -ID:/Qt/6.8.2/mingw_64/include/QtCore -IC:/MinGW-w64/include/c++/14.2.0 -IC:/MinGW-w64/include/c++/14.2.0/x86_64-w64-mingw32 -IC:/MinGW-w64/include/c++/14.2.0/backward -IC:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include -IC:/MinGW-w64/include -IC:/MinGW-w64/lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed -IC:/MinGW-w64/x86_64-w64-mingw32/include serialmanager.h -o release\moc_serialmanager.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_mainwindow.h ui_mappingwindow.h ui_setvaluedialog.h
compiler_uic_clean:
	-$(DEL_FILE) ui_mainwindow.h ui_mappingwindow.h ui_setvaluedialog.h
ui_mainwindow.h: mainwindow.ui \
		../../../Qt/6.8.2/mingw_64/bin/uic.exe
	D:\Qt\6.8.2\mingw_64\bin\uic.exe mainwindow.ui -o ui_mainwindow.h

ui_mappingwindow.h: mappingwindow.ui \
		../../../Qt/6.8.2/mingw_64/bin/uic.exe
	D:\Qt\6.8.2\mingw_64\bin\uic.exe mappingwindow.ui -o ui_mappingwindow.h

ui_setvaluedialog.h: setvaluedialog.ui \
		../../../Qt/6.8.2/mingw_64/bin/uic.exe
	D:\Qt\6.8.2\mingw_64\bin\uic.exe setvaluedialog.ui -o ui_setvaluedialog.h

compiler_dumpcpp_impl_make_all:
compiler_dumpcpp_impl_clean:
compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 



####### Compile

release/main.o: main.cpp mainwindow.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMainWindow \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmainwindow.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtgui-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstring.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qchar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringliteral.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qanystringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qutf8stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringbuilder.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearraylist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringmatcher.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetatype.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatastream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevicebase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfloat16.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetacontainer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopeguard.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbindingstorage.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmargins.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q23utility.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qaction.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qkeysequence.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qicon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrect.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcolor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgb.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgba64.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qimage.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixelformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtransform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpolygon.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qregion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qspan.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20iterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qline.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvariant.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdebug.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtextstream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qset.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhash.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpalette.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbrush.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfont.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qendian.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontmetrics.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbitmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qurl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qeventpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvector2d.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvectornd.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpointingdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QRect \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSize \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSizeF \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTransform \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnativeinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qeventloop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfutureinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmutex.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtsan_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qresultstore.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthreadpool.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthread.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrunnable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexception.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpromise.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputmethod.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlocale.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/QSerialPort \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qproperty.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpropertyprivate.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qtserialportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/QSerialPortInfo \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialportinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QString \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTimer \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasictimer.h \
		modbusserial.h \
		modbusserialbase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QByteArray \
		serialmanager.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QMap \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStringList \
		mappingwindow.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QComboBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcombobox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyleoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvalidator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qregularexpression.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyle.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabbar.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qrubberband.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qframe.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QListWidget \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlistwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlistview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGraphicsScene \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgraphicsscene.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpen.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGraphicsView \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgraphicsview.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpainter.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qscrollarea.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGraphicsLineItem \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgraphicsitem.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpainterpath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStack \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstack.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTextEdit \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtextedit.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextdocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QPushButton \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qpushbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMenu \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmenu.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialogButtonBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		setvaluedialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTableWidgetItem \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtablewidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtableview.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QAction \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QContextMenuEvent \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QDesktopServices \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qdesktopservices.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QUrl \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTemporaryFile \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtemporaryfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfiledevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/QPrinter \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qprinter.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupport-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagedpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagelayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagesize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpageranges.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTextDocument \
		crcutils.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QApplication \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QIcon
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\main.o main.cpp

release/mainwindow.o: mainwindow.cpp mainwindow.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMainWindow \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmainwindow.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtgui-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstring.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qchar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringliteral.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qanystringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qutf8stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringbuilder.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearraylist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringmatcher.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetatype.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatastream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevicebase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfloat16.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetacontainer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopeguard.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbindingstorage.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmargins.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q23utility.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qaction.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qkeysequence.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qicon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrect.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcolor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgb.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgba64.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qimage.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixelformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtransform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpolygon.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qregion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qspan.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20iterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qline.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvariant.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdebug.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtextstream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qset.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhash.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpalette.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbrush.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfont.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qendian.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontmetrics.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbitmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qurl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qeventpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvector2d.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvectornd.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpointingdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QRect \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSize \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSizeF \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTransform \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnativeinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qeventloop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfutureinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmutex.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtsan_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qresultstore.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthreadpool.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthread.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrunnable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexception.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpromise.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputmethod.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlocale.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/QSerialPort \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qproperty.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpropertyprivate.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qtserialportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/QSerialPortInfo \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialportinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QString \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTimer \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasictimer.h \
		modbusserial.h \
		modbusserialbase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QByteArray \
		serialmanager.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QMap \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStringList \
		mappingwindow.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QComboBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcombobox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyleoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvalidator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qregularexpression.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyle.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabbar.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qrubberband.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qframe.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QListWidget \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlistwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlistview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGraphicsScene \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgraphicsscene.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpen.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGraphicsView \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgraphicsview.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpainter.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qscrollarea.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGraphicsLineItem \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgraphicsitem.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpainterpath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStack \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstack.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTextEdit \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtextedit.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextdocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QPushButton \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qpushbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMenu \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmenu.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialogButtonBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		setvaluedialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTableWidgetItem \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtablewidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtableview.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QAction \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QContextMenuEvent \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QDesktopServices \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qdesktopservices.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QUrl \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTemporaryFile \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtemporaryfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfiledevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/QPrinter \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qprinter.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupport-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagedpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagelayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagesize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpageranges.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTextDocument \
		crcutils.h \
		ui_mainwindow.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QVariant \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QApplication \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGridLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgridlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayoutitem.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qboxlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGroupBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgroupbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QHBoxLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLabel \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlabel.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpicture.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLineEdit \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlineedit.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMenuBar \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmenubar.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QStatusBar \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstatusbar.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTextBrowser \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtextbrowser.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QToolBar \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtoolbar.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QVBoxLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QWidget \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QDebug \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMessageBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmessagebox.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QIcon \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QDateTime \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatetime.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcalendar.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QFileDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qfiledialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdir.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdirlisting.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfileinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimezone.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonDocument \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsondocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonvalue.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcborvalue.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcborcommon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/quuid.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonArray \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonarray.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QHeaderView \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qheaderview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QAbstractScrollArea
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\mainwindow.o mainwindow.cpp

release/mappingwindow.o: mappingwindow.cpp mappingwindow.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMainWindow \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmainwindow.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtgui-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstring.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qchar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringliteral.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qanystringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qutf8stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringbuilder.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearraylist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringmatcher.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetatype.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatastream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevicebase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfloat16.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetacontainer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopeguard.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbindingstorage.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmargins.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q23utility.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qaction.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qkeysequence.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qicon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrect.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcolor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgb.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgba64.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qimage.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixelformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtransform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpolygon.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qregion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qspan.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20iterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qline.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvariant.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdebug.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtextstream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qset.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhash.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpalette.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbrush.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfont.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qendian.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontmetrics.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbitmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qurl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qeventpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvector2d.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvectornd.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpointingdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QRect \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSize \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSizeF \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTransform \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnativeinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qeventloop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfutureinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmutex.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtsan_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qresultstore.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthreadpool.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthread.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrunnable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexception.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpromise.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputmethod.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlocale.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QComboBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcombobox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyleoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvalidator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qregularexpression.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyle.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabbar.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qrubberband.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qframe.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QListWidget \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlistwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlistview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGraphicsScene \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgraphicsscene.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpen.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGraphicsView \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgraphicsview.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpainter.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qscrollarea.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGraphicsLineItem \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgraphicsitem.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpainterpath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QMap \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStack \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstack.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QString \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTimer \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasictimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTextEdit \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtextedit.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextdocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QPushButton \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qpushbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMenu \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmenu.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialogButtonBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		modbusserial.h \
		modbusserialbase.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/QSerialPort \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qproperty.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpropertyprivate.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qtserialportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QByteArray \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QVBoxLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qboxlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayoutitem.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgridlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QHBoxLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLabel \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlabel.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpicture.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QFileDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qfiledialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdir.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdirlisting.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfiledevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatetime.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcalendar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfileinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimezone.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QXmlStreamReader \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxmlstream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QXmlStreamWriter \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonDocument \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsondocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonvalue.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcborvalue.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcborcommon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/quuid.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonArray \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonarray.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QInputDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qinputdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlineedit.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMessageBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmessagebox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTabWidget \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QCheckBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcheckbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QRegularExpression \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QDateTime \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTextCursor \
		ui_mappingwindow.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QVariant \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QApplication \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QFrame \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGroupBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgroupbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QSpacerItem \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QSpinBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QSplitter \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qsplitter.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QWidget \
		crcutils.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\mappingwindow.o mappingwindow.cpp

release/modbusserial.o: modbusserial.cpp modbusserial.h \
		modbusserialbase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstring.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qchar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringliteral.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qanystringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qutf8stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringbuilder.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearraylist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringmatcher.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetatype.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatastream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevicebase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfloat16.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetacontainer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopeguard.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbindingstorage.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/QSerialPort \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qproperty.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpropertyprivate.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qtserialportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTimer \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasictimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QByteArray
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\modbusserial.o modbusserial.cpp

release/modbusserialbase.o: modbusserialbase.cpp modbusserialbase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstring.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qchar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringliteral.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qanystringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qutf8stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringbuilder.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearraylist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringmatcher.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetatype.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatastream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevicebase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfloat16.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetacontainer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopeguard.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbindingstorage.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/QSerialPort \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qproperty.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpropertyprivate.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qtserialportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTimer \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasictimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QByteArray \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QDebug \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdebug.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtextstream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qset.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhash.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QDateTime \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatetime.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcalendar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlocale.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvariant.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q23utility.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\modbusserialbase.o modbusserialbase.cpp

release/parseitemdialog.o: parseitemdialog.cpp parseitemdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtgui-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstring.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qchar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringliteral.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qanystringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qutf8stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringbuilder.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearraylist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringmatcher.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetatype.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatastream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevicebase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfloat16.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetacontainer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopeguard.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbindingstorage.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmargins.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q23utility.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qaction.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qkeysequence.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qicon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrect.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcolor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgb.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgba64.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qimage.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixelformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtransform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpolygon.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qregion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qspan.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20iterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qline.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvariant.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdebug.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtextstream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qset.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhash.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpalette.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbrush.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfont.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qendian.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontmetrics.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbitmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qurl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qeventpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvector2d.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvectornd.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpointingdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QRect \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSize \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSizeF \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTransform \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnativeinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qeventloop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfutureinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmutex.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtsan_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qresultstore.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthreadpool.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthread.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrunnable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexception.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpromise.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputmethod.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlocale.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QComboBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcombobox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyleoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvalidator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qregularexpression.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyle.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabbar.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qrubberband.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qframe.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLineEdit \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlineedit.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextdocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpen.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QSpinBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLabel \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlabel.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpicture.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QPushButton \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qpushbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGridLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgridlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayoutitem.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qboxlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialogButtonBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QCheckBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcheckbox.h \
		setvaluedialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMainWindow \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmainwindow.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTimer \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasictimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTableWidgetItem \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtablewidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtableview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMenu \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmenu.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QAction \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QContextMenuEvent \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QDesktopServices \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qdesktopservices.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QUrl \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTemporaryFile \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtemporaryfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfiledevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/QPrinter \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qprinter.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupport-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagedpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagelayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagesize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpageranges.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTextDocument \
		serialmanager.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QMap \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/QSerialPort \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qproperty.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpropertyprivate.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qtserialportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QString \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStringList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QByteArray \
		modbusserial.h \
		modbusserialbase.h \
		crcutils.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QVBoxLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QFormLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qformlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGroupBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgroupbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMessageBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmessagebox.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\parseitemdialog.o parseitemdialog.cpp

release/setvaluedialog.o: setvaluedialog.cpp setvaluedialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtgui-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstring.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qchar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringliteral.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qanystringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qutf8stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringbuilder.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearraylist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringmatcher.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetatype.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatastream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevicebase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfloat16.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetacontainer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopeguard.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbindingstorage.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmargins.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q23utility.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qaction.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qkeysequence.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qicon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrect.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcolor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgb.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgba64.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qimage.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixelformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtransform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpolygon.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qregion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qspan.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20iterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qline.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvariant.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdebug.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtextstream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qset.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhash.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpalette.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbrush.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfont.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qendian.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontmetrics.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbitmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qurl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qeventpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvector2d.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvectornd.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpointingdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QRect \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSize \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSizeF \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTransform \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnativeinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qeventloop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfutureinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmutex.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtsan_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qresultstore.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthreadpool.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthread.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrunnable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexception.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpromise.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputmethod.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlocale.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMainWindow \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmainwindow.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTimer \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasictimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTableWidgetItem \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtablewidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtableview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qframe.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyleoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvalidator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qregularexpression.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyle.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabbar.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qrubberband.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMenu \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmenu.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QAction \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QContextMenuEvent \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QDesktopServices \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qdesktopservices.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QUrl \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTemporaryFile \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtemporaryfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfiledevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/QPrinter \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qprinter.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupport-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagedpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagelayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagesize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpageranges.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTextDocument \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextdocument.h \
		serialmanager.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QMap \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/QSerialPort \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qproperty.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpropertyprivate.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qtserialportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QString \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStringList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QByteArray \
		modbusserial.h \
		modbusserialbase.h \
		crcutils.h \
		ui_setvaluedialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QVariant \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QApplication \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QCheckBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcheckbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QComboBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcombobox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGroupBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgroupbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QHBoxLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qboxlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayoutitem.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgridlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QHeaderView \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qheaderview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLabel \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlabel.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpicture.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLineEdit \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlineedit.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpen.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QPushButton \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qpushbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QSpacerItem \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QSpinBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QSplitter \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qsplitter.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTableWidget \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTextBrowser \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtextbrowser.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtextedit.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QVBoxLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QWidget \
		parseitemdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGridLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialogButtonBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		valuefileselectiondialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QFormLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qformlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMessageBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmessagebox.h \
		../../../Qt/6.8.2/mingw_64/include/QtAxContainer/QAxObject \
		../../../Qt/6.8.2/mingw_64/include/QtAxContainer/qaxobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtAxContainer/qaxbase.h \
		../../../Qt/6.8.2/mingw_64/include/QtAxContainer/qaxobjectinterface.h \
		dataconversionrulesdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonDocument \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsondocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonvalue.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcborvalue.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcborcommon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatetime.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcalendar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/quuid.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonArray \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonarray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QFile \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStandardPaths \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstandardpaths.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QDir \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdir.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdirlisting.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfileinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimezone.h \
		datamappingrulesdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QListWidget \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlistwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlistview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QFileDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qfiledialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QDateTime \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QInputDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qinputdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QDebug \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QFileInfo \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QThread \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QKeyEvent \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSet \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QCoreApplication \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QScreen
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\setvaluedialog.o setvaluedialog.cpp

release/valuefileselectiondialog.o: valuefileselectiondialog.cpp valuefileselectiondialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtgui-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstring.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qchar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringliteral.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qanystringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qutf8stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringbuilder.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearraylist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringmatcher.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetatype.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatastream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevicebase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfloat16.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetacontainer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopeguard.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbindingstorage.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmargins.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q23utility.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qaction.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qkeysequence.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qicon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrect.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcolor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgb.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgba64.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qimage.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixelformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtransform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpolygon.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qregion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qspan.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20iterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qline.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvariant.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdebug.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtextstream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qset.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhash.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpalette.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbrush.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfont.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qendian.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontmetrics.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbitmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qurl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qeventpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvector2d.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvectornd.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpointingdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QRect \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSize \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSizeF \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTransform \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnativeinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qeventloop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfutureinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmutex.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtsan_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qresultstore.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthreadpool.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthread.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrunnable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexception.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpromise.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputmethod.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlocale.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QVBoxLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qboxlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayoutitem.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgridlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QHBoxLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QFormLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qformlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLabel \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlabel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qframe.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpicture.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextdocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QComboBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcombobox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyleoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvalidator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qregularexpression.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyle.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabbar.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qrubberband.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLineEdit \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlineedit.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpen.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QPushButton \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qpushbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTableWidget \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtablewidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtableview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QCheckBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcheckbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QHeaderView \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qheaderview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMessageBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmessagebox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtAxContainer/QAxObject \
		../../../Qt/6.8.2/mingw_64/include/QtAxContainer/qaxobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtAxContainer/qaxbase.h \
		../../../Qt/6.8.2/mingw_64/include/QtAxContainer/qaxobjectinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QVariant \
		setvaluedialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMainWindow \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmainwindow.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTimer \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasictimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTableWidgetItem \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMenu \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmenu.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QAction \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QContextMenuEvent \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QDesktopServices \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qdesktopservices.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QUrl \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTemporaryFile \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtemporaryfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfiledevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/QPrinter \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qprinter.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupport-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtPrintSupport/qtprintsupportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagedpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagelayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpagesize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpageranges.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTextDocument \
		serialmanager.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QMap \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/QSerialPort \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qproperty.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpropertyprivate.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qtserialportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QString \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStringList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QByteArray \
		modbusserial.h \
		modbusserialbase.h \
		crcutils.h \
		dataconversionrulesdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGridLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QSpinBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGroupBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgroupbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialogButtonBox \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonDocument \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsondocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonvalue.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcborvalue.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcborcommon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatetime.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcalendar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/quuid.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonArray \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonarray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QFile \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStandardPaths \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstandardpaths.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QDir \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdir.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdirlisting.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfileinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimezone.h \
		datamappingrulesdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QListWidget \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlistwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlistview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QFileDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qfiledialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QDateTime \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QInputDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qinputdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QDebug \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QFileInfo \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QApplication \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QRegularExpression
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\valuefileselectiondialog.o valuefileselectiondialog.cpp

release/dataconversionrulesdialog.o: dataconversionrulesdialog.cpp dataconversionrulesdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtgui-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstring.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qchar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringliteral.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qanystringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qutf8stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringbuilder.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearraylist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringmatcher.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetatype.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatastream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevicebase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfloat16.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetacontainer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopeguard.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbindingstorage.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmargins.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q23utility.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qaction.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qkeysequence.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qicon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrect.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcolor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgb.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgba64.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qimage.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixelformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtransform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpolygon.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qregion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qspan.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20iterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qline.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvariant.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdebug.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtextstream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qset.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhash.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpalette.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbrush.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfont.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qendian.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontmetrics.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbitmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qurl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qeventpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvector2d.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvectornd.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpointingdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QRect \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSize \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSizeF \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTransform \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnativeinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qeventloop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfutureinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmutex.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtsan_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qresultstore.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthreadpool.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthread.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrunnable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexception.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpromise.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputmethod.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlocale.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QVBoxLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qboxlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayoutitem.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgridlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QHBoxLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGridLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLabel \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlabel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qframe.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpicture.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextdocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLineEdit \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlineedit.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpen.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QPushButton \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qpushbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTableWidget \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtablewidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtableview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyleoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvalidator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qregularexpression.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyle.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabbar.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qrubberband.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QHeaderView \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qheaderview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMessageBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmessagebox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QCheckBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcheckbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QComboBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcombobox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QSpinBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGroupBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgroupbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialogButtonBox \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonDocument \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsondocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonvalue.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcborvalue.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcborcommon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatetime.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcalendar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/quuid.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonArray \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonarray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QFile \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfiledevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStandardPaths \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstandardpaths.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QDir \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdir.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdirlisting.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfileinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimezone.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QDebug \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QRegularExpression
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\dataconversionrulesdialog.o dataconversionrulesdialog.cpp

release/datamappingrulesdialog.o: datamappingrulesdialog.cpp datamappingrulesdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtgui-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtguiexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstring.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qchar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringliteral.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qanystringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qutf8stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringbuilder.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearraylist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringmatcher.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetatype.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatastream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevicebase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfloat16.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetacontainer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopeguard.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbindingstorage.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmargins.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q23utility.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qaction.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qkeysequence.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qicon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsize.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpaintdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrect.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcolor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgb.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qrgba64.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qimage.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpixelformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtransform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpolygon.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qregion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qspan.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20iterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qline.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvariant.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdebug.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtextstream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontiguouscache.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qset.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhash.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qvarlengtharray.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpalette.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbrush.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfont.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qendian.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontmetrics.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qfontinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qbitmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qurl.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qeventpoint.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvector2d.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvectornd.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpointingdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputdevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QRect \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSize \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QSizeF \
		../../../Qt/6.8.2/mingw_64/include/QtGui/QTransform \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnativeinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qscreen_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdeadlinetimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qelapsedtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qeventloop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfutureinterface.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmutex.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtsan_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qresultstore.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfuture_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthreadpool.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qthread.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrunnable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexception.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpromise.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qinputmethod.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlocale.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qguiapplication_platform.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QVBoxLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qboxlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlayoutitem.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgridlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QHBoxLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGridLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QFormLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qformlayout.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLayout \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLabel \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlabel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qframe.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpicture.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextdocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QComboBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qcombobox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyleoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qvalidator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qregularexpression.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractslider.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qstyle.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabbar.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtabwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qrubberband.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QListWidget \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlistwidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlistview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QLineEdit \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qlineedit.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextcursor.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextformat.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qpen.h \
		../../../Qt/6.8.2/mingw_64/include/QtGui/qtextoption.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QPushButton \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qpushbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qabstractbutton.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTableWidget \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtablewidget.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qtableview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QTableWidgetItem \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QHeaderView \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qheaderview.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QMessageBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qmessagebox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QGroupBox \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qgroupbox.h \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QFileDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qfiledialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdir.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdirlisting.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfiledevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatetime.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcalendar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfile.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfileinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimezone.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonDocument \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsondocument.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonvalue.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcborvalue.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcborcommon.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/quuid.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QJsonArray \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qjsonarray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStandardPaths \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstandardpaths.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QDir \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QDateTime \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QInputDialog \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qinputdialog.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QDebug \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/QApplication \
		../../../Qt/6.8.2/mingw_64/include/QtWidgets/qapplication.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\datamappingrulesdialog.o datamappingrulesdialog.cpp

release/crcutils.o: crcutils.cpp crcutils.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QByteArray \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\crcutils.o crcutils.cpp

release/serialmanager.o: serialmanager.cpp serialmanager.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QObject \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnamespace.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversionchecks.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfiginclude.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconfig.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcore-config.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtconfigmacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtcoreexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompilerdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qprocessordetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsystemdetection.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtclasshelpermacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qassert.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtnoop.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypes.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtversion.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtypeinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qsysinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlogging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qflags.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasicatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qgenericatomic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qyieldcpu.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qconstructormacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdarwinhelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qexceptionhandling.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qforeach.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttypetraits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qglobalstatic.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmalloc.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qminmax.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qnumeric.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qoverload.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qswap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtenvironmentvariables.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtresource.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qttranslation.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qversiontagging.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcompare.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcomparehelpers.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20type_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtmetamacros.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstring.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qchar.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearray.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qrefcount.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpair.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydatapointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qarraydataops.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainertools_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qxptype_traits.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20functional.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/q20memory.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearrayview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringfwd.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringliteral.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlatin1stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qanystringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qutf8stringview.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringtokenizer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringbuilder.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringconverter_base.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qhashfunctions.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterator.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbytearraylist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringlist.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qalgorithms.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qstringmatcher.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcoreevent.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetatype.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qdatastream.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevicebase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qfloat16.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmath.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiterable.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmetacontainer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qcontainerinfo.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtaggedpointer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qscopeguard.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qobject_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbindingstorage.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QMap \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qmap.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qshareddata_impl.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/QSerialPort \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialport.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qiodevice.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qproperty.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qpropertyprivate.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialportglobal.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qtserialportexports.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QString \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QStringList \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QByteArray \
		modbusserial.h \
		modbusserialbase.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/QTimer \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qtimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtCore/qbasictimer.h \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/QSerialPortInfo \
		../../../Qt/6.8.2/mingw_64/include/QtSerialPort/qserialportinfo.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\serialmanager.o serialmanager.cpp

release/qrc_resources.o: release/qrc_resources.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\qrc_resources.o release\qrc_resources.cpp

release/qrc_icons.o: release/qrc_icons.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\qrc_icons.o release\qrc_icons.cpp

release/moc_mainwindow.o: release/moc_mainwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_mainwindow.o release\moc_mainwindow.cpp

release/moc_mappingwindow.o: release/moc_mappingwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_mappingwindow.o release\moc_mappingwindow.cpp

release/moc_modbusserial.o: release/moc_modbusserial.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_modbusserial.o release\moc_modbusserial.cpp

release/moc_modbusserialbase.o: release/moc_modbusserialbase.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_modbusserialbase.o release\moc_modbusserialbase.cpp

release/moc_parseitemdialog.o: release/moc_parseitemdialog.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_parseitemdialog.o release\moc_parseitemdialog.cpp

release/moc_setvaluedialog.o: release/moc_setvaluedialog.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_setvaluedialog.o release\moc_setvaluedialog.cpp

release/moc_valuefileselectiondialog.o: release/moc_valuefileselectiondialog.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_valuefileselectiondialog.o release\moc_valuefileselectiondialog.cpp

release/moc_dataconversionrulesdialog.o: release/moc_dataconversionrulesdialog.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_dataconversionrulesdialog.o release\moc_dataconversionrulesdialog.cpp

release/moc_datamappingrulesdialog.o: release/moc_datamappingrulesdialog.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_datamappingrulesdialog.o release\moc_datamappingrulesdialog.cpp

release/moc_serialmanager.o: release/moc_serialmanager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_serialmanager.o release\moc_serialmanager.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

.SUFFIXES:

