/********************************************************************************
** Form generated from reading UI file 'setvaluedialog.ui'
**
** Created by: Qt User Interface Compiler version 6.8.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_SETVALUEDIALOG_H
#define UI_SETVALUEDIALOG_H

#include <QtCore/QVariant>
#include <QtGui/QIcon>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QSplitter>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QTextBrowser>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_SetValueDialog
{
public:
    QWidget *centralwidget;
    QVBoxLayout *verticalLayout;
    QGroupBox *groupBoxConfig;
    QHBoxLayout *horizontalLayout_config;
    QLabel *labelPort;
    QComboBox *comboBoxPort;
    QLabel *labelDeviceModel;
    QComboBox *comboBoxDeviceModel;
    QPushButton *pushButtonSelectValueFile;
    QLabel *labelDeviceAddress;
    QSpinBox *spinBoxDeviceAddress;
    QLabel *labelFunctionCode;
    QComboBox *comboBoxFunctionCode;
    QLabel *labelDataAddress;
    QLineEdit *lineEditDataAddress;
    QLabel *labelDataCount;
    QSpinBox *spinBoxDataCount;
    QLabel *labelDelay;
    QSpinBox *spinBoxDelay;
    QCheckBox *checkBoxCRC16;
    QHBoxLayout *horizontalLayout;
    QSpacerItem *horizontalSpacer;
    QPushButton *pushButtonSend;
    QPushButton *pushButtonGenerateReport;
    QPushButton *pushButtonWriteValue;
    QCheckBox *checkBoxHideParseConfig;
    QSplitter *splitter;
    QGroupBox *groupBoxParse;
    QVBoxLayout *verticalLayout_2;
    QTableWidget *tableWidgetParse;
    QLabel *labelParseHint;
    QGroupBox *groupBoxLog;
    QVBoxLayout *verticalLayout_3;
    QTextBrowser *textBrowserLog;
    QHBoxLayout *horizontalLayoutLog;
    QSpacerItem *horizontalSpacerLog;
    QPushButton *pushButtonClearLog;

    void setupUi(QMainWindow *SetValueDialog)
    {
        if (SetValueDialog->objectName().isEmpty())
            SetValueDialog->setObjectName("SetValueDialog");
        SetValueDialog->resize(800, 600);
        centralwidget = new QWidget(SetValueDialog);
        centralwidget->setObjectName("centralwidget");
        verticalLayout = new QVBoxLayout(centralwidget);
        verticalLayout->setObjectName("verticalLayout");
        groupBoxConfig = new QGroupBox(centralwidget);
        groupBoxConfig->setObjectName("groupBoxConfig");
        groupBoxConfig->setMinimumSize(QSize(0, 60));
        horizontalLayout_config = new QHBoxLayout(groupBoxConfig);
        horizontalLayout_config->setSpacing(15);
        horizontalLayout_config->setObjectName("horizontalLayout_config");
        horizontalLayout_config->setContentsMargins(10, 10, 10, 10);
        labelPort = new QLabel(groupBoxConfig);
        labelPort->setObjectName("labelPort");

        horizontalLayout_config->addWidget(labelPort);

        comboBoxPort = new QComboBox(groupBoxConfig);
        comboBoxPort->setObjectName("comboBoxPort");
        comboBoxPort->setMinimumSize(QSize(80, 0));

        horizontalLayout_config->addWidget(comboBoxPort);

        labelDeviceModel = new QLabel(groupBoxConfig);
        labelDeviceModel->setObjectName("labelDeviceModel");

        horizontalLayout_config->addWidget(labelDeviceModel);

        comboBoxDeviceModel = new QComboBox(groupBoxConfig);
        comboBoxDeviceModel->addItem(QString());
        comboBoxDeviceModel->addItem(QString());
        comboBoxDeviceModel->addItem(QString());
        comboBoxDeviceModel->setObjectName("comboBoxDeviceModel");
        comboBoxDeviceModel->setMinimumSize(QSize(100, 0));

        horizontalLayout_config->addWidget(comboBoxDeviceModel);

        pushButtonSelectValueFile = new QPushButton(groupBoxConfig);
        pushButtonSelectValueFile->setObjectName("pushButtonSelectValueFile");
        pushButtonSelectValueFile->setMinimumSize(QSize(100, 0));

        horizontalLayout_config->addWidget(pushButtonSelectValueFile);

        labelDeviceAddress = new QLabel(groupBoxConfig);
        labelDeviceAddress->setObjectName("labelDeviceAddress");

        horizontalLayout_config->addWidget(labelDeviceAddress);

        spinBoxDeviceAddress = new QSpinBox(groupBoxConfig);
        spinBoxDeviceAddress->setObjectName("spinBoxDeviceAddress");
        spinBoxDeviceAddress->setMinimum(1);
        spinBoxDeviceAddress->setMaximum(255);
        spinBoxDeviceAddress->setMinimumSize(QSize(60, 0));

        horizontalLayout_config->addWidget(spinBoxDeviceAddress);

        labelFunctionCode = new QLabel(groupBoxConfig);
        labelFunctionCode->setObjectName("labelFunctionCode");

        horizontalLayout_config->addWidget(labelFunctionCode);

        comboBoxFunctionCode = new QComboBox(groupBoxConfig);
        comboBoxFunctionCode->addItem(QString());
        comboBoxFunctionCode->addItem(QString());
        comboBoxFunctionCode->addItem(QString());
        comboBoxFunctionCode->addItem(QString());
        comboBoxFunctionCode->addItem(QString());
        comboBoxFunctionCode->addItem(QString());
        comboBoxFunctionCode->addItem(QString());
        comboBoxFunctionCode->addItem(QString());
        comboBoxFunctionCode->setObjectName("comboBoxFunctionCode");
        comboBoxFunctionCode->setMinimumSize(QSize(120, 0));

        horizontalLayout_config->addWidget(comboBoxFunctionCode);

        labelDataAddress = new QLabel(groupBoxConfig);
        labelDataAddress->setObjectName("labelDataAddress");

        horizontalLayout_config->addWidget(labelDataAddress);

        lineEditDataAddress = new QLineEdit(groupBoxConfig);
        lineEditDataAddress->setObjectName("lineEditDataAddress");

        horizontalLayout_config->addWidget(lineEditDataAddress);

        labelDataCount = new QLabel(groupBoxConfig);
        labelDataCount->setObjectName("labelDataCount");

        horizontalLayout_config->addWidget(labelDataCount);

        spinBoxDataCount = new QSpinBox(groupBoxConfig);
        spinBoxDataCount->setObjectName("spinBoxDataCount");
        spinBoxDataCount->setMinimum(1);
        spinBoxDataCount->setMaximum(125);
        spinBoxDataCount->setValue(30);

        horizontalLayout_config->addWidget(spinBoxDataCount);

        labelDelay = new QLabel(groupBoxConfig);
        labelDelay->setObjectName("labelDelay");

        horizontalLayout_config->addWidget(labelDelay);

        spinBoxDelay = new QSpinBox(groupBoxConfig);
        spinBoxDelay->setObjectName("spinBoxDelay");
        spinBoxDelay->setMinimum(100);
        spinBoxDelay->setMaximum(10000);
        spinBoxDelay->setSingleStep(100);
        spinBoxDelay->setValue(1000);

        horizontalLayout_config->addWidget(spinBoxDelay);

        checkBoxCRC16 = new QCheckBox(groupBoxConfig);
        checkBoxCRC16->setObjectName("checkBoxCRC16");
        checkBoxCRC16->setChecked(true);

        horizontalLayout_config->addWidget(checkBoxCRC16);

        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName("horizontalLayout");
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayout->addItem(horizontalSpacer);

        pushButtonSend = new QPushButton(groupBoxConfig);
        pushButtonSend->setObjectName("pushButtonSend");
        QIcon icon;
        icon.addFile(QString::fromUtf8(":/images/images/send.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButtonSend->setIcon(icon);

        horizontalLayout->addWidget(pushButtonSend);

        pushButtonGenerateReport = new QPushButton(groupBoxConfig);
        pushButtonGenerateReport->setObjectName("pushButtonGenerateReport");
        QIcon icon1;
        icon1.addFile(QString::fromUtf8(":/images/images/view.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButtonGenerateReport->setIcon(icon1);

        horizontalLayout->addWidget(pushButtonGenerateReport);

        pushButtonWriteValue = new QPushButton(groupBoxConfig);
        pushButtonWriteValue->setObjectName("pushButtonWriteValue");
        QIcon icon2;
        icon2.addFile(QString::fromUtf8(":/icons/setvalue.svg"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButtonWriteValue->setIcon(icon2);

        horizontalLayout->addWidget(pushButtonWriteValue);

        checkBoxHideParseConfig = new QCheckBox(groupBoxConfig);
        checkBoxHideParseConfig->setObjectName("checkBoxHideParseConfig");
        checkBoxHideParseConfig->setChecked(true);

        horizontalLayout->addWidget(checkBoxHideParseConfig);


        horizontalLayout_config->addLayout(horizontalLayout);


        verticalLayout->addWidget(groupBoxConfig);

        splitter = new QSplitter(centralwidget);
        splitter->setObjectName("splitter");
        splitter->setOrientation(Qt::Vertical);
        groupBoxParse = new QGroupBox(splitter);
        groupBoxParse->setObjectName("groupBoxParse");
        verticalLayout_2 = new QVBoxLayout(groupBoxParse);
        verticalLayout_2->setObjectName("verticalLayout_2");
        tableWidgetParse = new QTableWidget(groupBoxParse);
        tableWidgetParse->setObjectName("tableWidgetParse");

        verticalLayout_2->addWidget(tableWidgetParse);

        labelParseHint = new QLabel(groupBoxParse);
        labelParseHint->setObjectName("labelParseHint");

        verticalLayout_2->addWidget(labelParseHint);

        splitter->addWidget(groupBoxParse);
        groupBoxLog = new QGroupBox(splitter);
        groupBoxLog->setObjectName("groupBoxLog");
        verticalLayout_3 = new QVBoxLayout(groupBoxLog);
        verticalLayout_3->setObjectName("verticalLayout_3");
        textBrowserLog = new QTextBrowser(groupBoxLog);
        textBrowserLog->setObjectName("textBrowserLog");

        verticalLayout_3->addWidget(textBrowserLog);

        horizontalLayoutLog = new QHBoxLayout();
        horizontalLayoutLog->setObjectName("horizontalLayoutLog");
        horizontalSpacerLog = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        horizontalLayoutLog->addItem(horizontalSpacerLog);

        pushButtonClearLog = new QPushButton(groupBoxLog);
        pushButtonClearLog->setObjectName("pushButtonClearLog");
        QIcon icon3;
        icon3.addFile(QString::fromUtf8(":/images/images/close.png"), QSize(), QIcon::Mode::Normal, QIcon::State::Off);
        pushButtonClearLog->setIcon(icon3);

        horizontalLayoutLog->addWidget(pushButtonClearLog);


        verticalLayout_3->addLayout(horizontalLayoutLog);

        splitter->addWidget(groupBoxLog);

        verticalLayout->addWidget(splitter);

        SetValueDialog->setCentralWidget(centralwidget);

        retranslateUi(SetValueDialog);

        QMetaObject::connectSlotsByName(SetValueDialog);
    } // setupUi

    void retranslateUi(QMainWindow *SetValueDialog)
    {
        SetValueDialog->setWindowTitle(QCoreApplication::translate("SetValueDialog", "\345\256\232\345\200\274\345\217\254\345\224\244", nullptr));
        groupBoxConfig->setTitle(QCoreApplication::translate("SetValueDialog", "\351\200\232\344\277\241\351\205\215\347\275\256", nullptr));
        labelPort->setText(QCoreApplication::translate("SetValueDialog", "\351\200\232\344\277\241\344\270\262\345\217\243\357\274\232", nullptr));
        labelDeviceModel->setText(QCoreApplication::translate("SetValueDialog", "\350\256\276\345\244\207\345\236\213\345\217\267\357\274\232", nullptr));
        comboBoxDeviceModel->setItemText(0, QCoreApplication::translate("SetValueDialog", "\346\227\240\350\256\276\345\244\207", nullptr));
        comboBoxDeviceModel->setItemText(1, QCoreApplication::translate("SetValueDialog", "LL510P", nullptr));
        comboBoxDeviceModel->setItemText(2, QCoreApplication::translate("SetValueDialog", "LM510P", nullptr));

        pushButtonSelectValueFile->setText(QCoreApplication::translate("SetValueDialog", "\345\256\232\345\200\274\346\226\207\344\273\266\351\200\211\346\213\251", nullptr));
        labelDeviceAddress->setText(QCoreApplication::translate("SetValueDialog", "\350\256\276\345\244\207\345\234\260\345\235\200\357\274\232", nullptr));
        labelFunctionCode->setText(QCoreApplication::translate("SetValueDialog", "\345\212\237\350\203\275\347\240\201\357\274\232", nullptr));
        comboBoxFunctionCode->setItemText(0, QCoreApplication::translate("SetValueDialog", "01 - \350\257\273\347\272\277\345\234\210", nullptr));
        comboBoxFunctionCode->setItemText(1, QCoreApplication::translate("SetValueDialog", "02 - \350\257\273\347\246\273\346\225\243\350\276\223\345\205\245", nullptr));
        comboBoxFunctionCode->setItemText(2, QCoreApplication::translate("SetValueDialog", "03 - \350\257\273\344\277\235\346\214\201\345\257\204\345\255\230\345\231\250", nullptr));
        comboBoxFunctionCode->setItemText(3, QCoreApplication::translate("SetValueDialog", "04 - \350\257\273\350\276\223\345\205\245\345\257\204\345\255\230\345\231\250", nullptr));
        comboBoxFunctionCode->setItemText(4, QCoreApplication::translate("SetValueDialog", "05 - \345\206\231\345\215\225\344\270\252\347\272\277\345\234\210", nullptr));
        comboBoxFunctionCode->setItemText(5, QCoreApplication::translate("SetValueDialog", "06 - \345\206\231\345\215\225\344\270\252\345\257\204\345\255\230\345\231\250", nullptr));
        comboBoxFunctionCode->setItemText(6, QCoreApplication::translate("SetValueDialog", "15 - \345\206\231\345\244\232\344\270\252\347\272\277\345\234\210", nullptr));
        comboBoxFunctionCode->setItemText(7, QCoreApplication::translate("SetValueDialog", "16 - \345\206\231\345\244\232\344\270\252\345\257\204\345\255\230\345\231\250", nullptr));

        labelDataAddress->setText(QCoreApplication::translate("SetValueDialog", "\346\225\260\346\215\256\345\234\260\345\235\200\357\274\232", nullptr));
        lineEditDataAddress->setText(QCoreApplication::translate("SetValueDialog", "00", nullptr));
        lineEditDataAddress->setPlaceholderText(QCoreApplication::translate("SetValueDialog", "\345\215\201\345\205\255\350\277\233\345\210\266\345\234\260\345\235\200", nullptr));
        labelDataCount->setText(QCoreApplication::translate("SetValueDialog", "\346\225\260\346\215\256\344\270\252\346\225\260\357\274\232", nullptr));
        labelDelay->setText(QCoreApplication::translate("SetValueDialog", "\346\265\213\350\257\225\345\273\266\346\227\266(ms)\357\274\232", nullptr));
        checkBoxCRC16->setText(QCoreApplication::translate("SetValueDialog", "CRC16\346\240\241\351\252\214", nullptr));
        pushButtonSend->setText(QCoreApplication::translate("SetValueDialog", "\345\217\221\351\200\201", nullptr));
        pushButtonGenerateReport->setText(QCoreApplication::translate("SetValueDialog", "\347\224\237\346\210\220\346\212\245\345\221\212", nullptr));
        pushButtonWriteValue->setText(QCoreApplication::translate("SetValueDialog", "\345\206\231\345\205\245\345\256\232\345\200\274", nullptr));
        checkBoxHideParseConfig->setText(QCoreApplication::translate("SetValueDialog", "\351\232\220\350\227\217", nullptr));
        groupBoxParse->setTitle(QCoreApplication::translate("SetValueDialog", "\346\212\245\346\226\207\350\247\243\346\236\220\351\205\215\347\275\256", nullptr));
        labelParseHint->setText(QCoreApplication::translate("SetValueDialog", "\346\217\220\347\244\272\357\274\232\345\217\263\351\224\256\347\202\271\345\207\273\350\241\250\346\240\274\345\217\257\346\267\273\345\212\240/\347\274\226\350\276\221/\345\210\240\351\231\244\350\247\243\346\236\220\351\241\271\357\274\214\345\217\214\345\207\273\350\241\250\346\240\274\351\241\271\345\217\257\347\274\226\350\276\221", nullptr));
        groupBoxLog->setTitle(QCoreApplication::translate("SetValueDialog", "\351\200\232\344\277\241\346\227\245\345\277\227", nullptr));
        pushButtonClearLog->setText(QCoreApplication::translate("SetValueDialog", "\346\270\205\347\251\272\346\227\245\345\277\227", nullptr));
    } // retranslateUi

};

namespace Ui {
    class SetValueDialog: public Ui_SetValueDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_SETVALUEDIALOG_H
